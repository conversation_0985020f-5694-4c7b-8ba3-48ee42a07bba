package com.yunqu.crawler.service;

import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.mapper.XtyTariffCrawlRecordMapper;
import com.yunqu.emergency.common.mybatis.annotation.Dynamic;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 *
 * </p>
 *
 * @ClassName DynamicTableService
 * <AUTHOR> Copy This Tag)
 * @Description TODO 描述文件用途
 * @Since create in 2025/7/15 17:19
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@RequiredArgsConstructor
@Service
public class DynamicTableService {

    private final XtyTariffCrawlRecordMapper tariffCrawlRecordMapper;

    @Dynamic(version = "version")
    public void extracted(String version) {
        XtyTariffCrawlRecord xtyTariffCrawlRecord = new XtyTariffCrawlRecord();
        xtyTariffCrawlRecord.setTaskId(1L);
        xtyTariffCrawlRecord.setProvinceCode("BJ");
        xtyTariffCrawlRecord.setProvinceName("北京");
        xtyTariffCrawlRecord.setEntCode("001");
        xtyTariffCrawlRecord.setEntName("电信");
        xtyTariffCrawlRecord.setVersionNo(version);
        tariffCrawlRecordMapper.insert(xtyTariffCrawlRecord);
    }


}
