package com.yunqu.crawler.service;

import java.util.Collection;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunqu.crawler.domain.XtyCrawlerVersion;
import com.yunqu.crawler.domain.bo.XtyCrawlerVersionBo;
import com.yunqu.crawler.domain.vo.XtyCrawlerVersionVo;
import com.yunqu.emergency.common.mybatis.core.page.PageQuery;
import com.yunqu.emergency.common.mybatis.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
public interface IXtyCrawlerVersionService extends IService<XtyCrawlerVersion> {

    /**
     * 查询【请填写功能名称】
     *
     * @param id 主键
     * @return 【请填写功能名称】
     */
    XtyCrawlerVersionVo queryById(Long id);

    /**
     * 分页查询【请填写功能名称】列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 【请填写功能名称】分页列表
     */
    TableDataInfo<XtyCrawlerVersionVo> queryPageList(XtyCrawlerVersionBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的【请填写功能名称】列表
     *
     * @param bo 查询条件
     * @return 【请填写功能名称】列表
     */
    List<XtyCrawlerVersionVo> queryList(XtyCrawlerVersionBo bo);

    /**
     * 新增【请填写功能名称】
     *
     * @param bo 【请填写功能名称】
     * @return 是否新增成功
     */
    Boolean insertByBo(XtyCrawlerVersionBo bo);

    /**
     * 修改【请填写功能名称】
     *
     * @param bo 【请填写功能名称】
     * @return 是否修改成功
     */
    Boolean updateByBo(XtyCrawlerVersionBo bo);

    /**
     * 校验并批量删除【请填写功能名称】信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
