package com.yunqu.crawler.domain.bo;

import com.yunqu.crawler.domain.XtyTariffCrawlRecordLib;
import com.yunqu.emergency.common.core.validate.EditGroup;
import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 信通院资费爬虫数据业务对象 xty_tariff_crawl_record_lib
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = XtyTariffCrawlRecordLib.class, reverseConvertGenerate = false)
public class XtyTariffCrawlRecordLibBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 报送库资费ID
     */
    private String tariffRecordId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 运营商编码
     */
    private String entCode;

    /**
     * 运营商名称
     */
    private String entName;

    /**
     * 资费名称
     */
    private String name;

    /**
     * 资费方案编号
     */
    private String tariffNo;

    /**
     * 资费标准
     */
    private String fees;

    /**
     * 资费单位
     */
    private String feesUnit;

    /**
     * 语音
     */
    private String call;

    /**
     * 通用流量
     */
    private String data;

    /**
     * 流量单位
     */
    private String dataUnit;

    /**
     * 短彩信
     */
    private String sms;

    /**
     * 定向流量
     */
    private String orientTraffic;

    /**
     * 定向流量单位
     */
    private String orientTrafficUnit;

    /**
     * IPTV
     */
    private String iptv;

    /**
     * 带宽
     */
    private String bandwidth;

    /**
     * 权益
     */
    private String rights;

    /**
     * 增值业务
     */
    private String incrementBusiness;

    /**
     * 服务内容
     */
    private String otherContent;

    /**
     * 上线日期
     */
    private String onlineDay;

    /**
     * 下线日期
     */
    private String offlineDay;

    /**
     * 资费属性
     */
    private String tariffAttr;

    /**
     * 适用范围
     */
    private String applicablePeople;

    /**
     * 适用地区
     */
    private String applicableArea;

    /**
     * 有效期限
     */
    private String validPeriod;

    /**
     * 销售渠道
     */
    private String channel;

    /**
     * 在网要求
     */
    private String duration;

    /**
     * 退订方式
     */
    private String unsubscribe;

    /**
     * 违约责任
     */
    private String responsibility;

    /**
     * 其他说明
     */
    private String others;

    /**
     * 扩展字段
     */
    private String extendedFields;

    /**
     * 爬取数据日期ID
     */
    private Long dateId;

    /**
     * 爬取数据月ID
     */
    private Long monthId;

    /**
     * 是否已报送 1是0否
     */
    private Integer reported;

    /**
     * 版本号
     */
    private String versionNo;

    /**
     * 资费省份编码
     */
    private String tariffProvinceCode;

    /**
     * 资费省名称
     */
    private String tariffProvinceName;

    /**
     * 爬取省份
     */
    private String crawlerProvinceName;

    /**
     * 爬取类型
     */
    private String crawlerType;

    /**
     * 一级分类
     */
    private String classicTypeOne;

    /**
     * 儿子分类
     */
    private String classicTypeTwo;

    /**
     * 资费分类
     */
    private String tariffType;

    /**
     * 备用字段
     */
    private String bak1;

    /**
     * 备用字段
     */
    private String bak2;

    /**
     * 备用字段
     */
    private String bak3;

    /**
     * 备用字段
     */
    private String bak4;

    /**
     * 备用字段
     */
    private String bak5;

    /**
     * 备用字段
     */
    private String bak6;

    /**
     * 备用字段
     */
    private String bak7;

    /**
     * 备用字段
     */
    private String bak8;

    /**
     * 备用字段
     */
    private String bak9;

    /**
     * 备用字段
     */
    private String bak10;

    /**
     * 备用字段
     */
    private String bak11;


    /**
     * 备用字段
     */
    private String bak12;

    /**
     * 备用字段
     */
    private String bak13;

    /**
     * 备用字段
     */
    private String bak14;

    /**
     * 备用字段
     */
    private String bak15;


}
