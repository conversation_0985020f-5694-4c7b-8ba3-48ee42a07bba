package com.yunqu.crawler.service;

import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.crawler.domain.XtyCrawlerVersion;

import java.util.List;

/**
 * 爬虫任务业务服务接口
 *
 * 主要功能：
 * 1. 运营商爬取任务的执行逻辑
 * 2. 任务状态检查和管理
 * 3. 任务调度和分发
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface ICrawlerTariffTaskService {

    /**
     * 创建版本任务
     */
    void excueteCreateVersionTask();

    /**
     * 执行爬虫任务
     */
    void excueteCrawlerTask(boolean withUnicom);

    /**
     * 按顺序执行各运营商的爬取任务
     *
     * @param dateId 日期ID
     * @param versionNo 版本号
     */
    void executeOperatorCrawler(Integer dateId, String versionNo);

    /**
     * 检查是否有正在运行的任务
     *
     * @return true=有任务运行中，false=无运行任务
     */
    boolean isTaskRunning(boolean withUnicom);

    /**
     * 处理溢出任务
     *
     * @param versionNo 版本号
     * @return 溢出任务，如果没有返回null
     */
    XtyCrawlerTask handleOverflowTask(String versionNo);

    /**
     * 获取下一个待执行的任务
     *
     * @param operators 运营商列表
     * @param versionNo 版本号
     * @return 待执行任务，如果没有返回null
     */
    XtyCrawlerTask getNextTask(List<String> operators, String versionNo);

    /**
     * 执行具体的爬虫任务
     *
     * @param task 爬虫任务
     */
    void executeTask(XtyCrawlerTask task);

    /**
     * 处理任务执行完成后的版本状态更新
     *
     * @param currentVersion 当前版本信息
     * @param operators 运营商列表
     * @param versionNo 版本号
     */
    void handleTaskCompletion(XtyCrawlerVersion currentVersion, List<String> operators, String versionNo);

    /**
     * 处理任务执行完成后的版本状态更新
     *
     * @param currentVersion 当前版本信息
     * @param versionNo 版本号
     */

    void handleTaskCompletionWithUnicom(XtyCrawlerVersion currentVersion, String versionNo);
}
