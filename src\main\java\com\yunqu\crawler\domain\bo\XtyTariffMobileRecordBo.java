package com.yunqu.crawler.domain.bo;

import com.yunqu.crawler.domain.XtyTariffMobileRecord;
import com.yunqu.emergency.common.core.validate.EditGroup;
import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 信通院-中国移动公示资费数据业务对象 xty_tariff_mobile_record
 *
 * <AUTHOR>
 * @date 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = XtyTariffMobileRecord.class, reverseConvertGenerate = false)
public class XtyTariffMobileRecordBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 主套餐
     */
    private String mainPackage;

    /**
     * 类别
     */
    private String packageCategory;

    /**
     * 资费标准
     */
    private String tariffStandard;

    /**
     * 资费方案编号
     */
    private String tariffNo;

    /**
     * 资费类型
     */
    private String tariffType;

    /**
     * 适用范围
     */
    private String tariffScope;

    /**
     *
     */
    private String applicationArea;

    /**
     * 销售渠道
     */
    private String tariffChannel;

    /**
     * 上线日期
     */
    private String onlineDate;

    /**
     * 下线日期
     */
    private String offlineDate;

    /**
     * 有效期限
     */
    private String validPeriod;

    /**
     * 在网要求
     */
    private String onlineRequirement;

    /**
     * 退订方式
     */
    private String unsubscribeMethod;

    /**
     * 违约责任
     */
    private String breachResponsibility;

    /**
     * 服务内容
     */
    private String serviceContent;

    /**
     * 其他说明
     */
    private String others;

    /**
     * 资费来源
     */
    private String tariffSource;

    /**
     * 定向流量
     */
    private String targetedTraffic;

    /**
     * 宽带
     */
    private String broadband;

    /**
     * 移动高清
     */
    private String mobileHd;

    /**
     * 通话
     */
    private String call;

    /**
     * 通用流量
     */
    private String generalFlows;

    /**
     * 权益
     */
    private String rightsInterests;

    /**
     * 国际通话
     */
    private String internationalCall;

    /**
     * 短彩信
     */
    private String shortMms;

    /**
     * 扩展字段
     */
    private String attr1;

    /**
     * 扩展字段
     */
    private String attr2;

    /**
     * 扩展字段
     */
    private String attr3;

    /**
     * 扩展字段
     */
    private String attr4;

    /**
     * 扩展字段
     */
    private String attr5;


}
