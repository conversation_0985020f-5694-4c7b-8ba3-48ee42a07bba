# xty-crawler-service

## 项目介绍

`xty-crawler-service` 是一个基于 Spring Boot 的运营商资费信息爬虫服务，用于自动化收集、处理和存储中国电信、中国移动、中国联通和中国广电等运营商的资费套餐数据。项目采用模块化设计，具备良好的扩展性和可维护性。

## 主要功能

- **多运营商支持**：支持中国电信、中国移动、中国联通和中国广电四大运营商的资费数据爬取
- **定时任务调度**：自动定时（每天凌晨1点）触发各运营商资费信息的爬取任务
- **异步任务处理**：使用 Spring 的异步特性处理耗时操作，提高系统响应能力
- **文件解压解析**：支持回调接口，自动解压上传的 ZIP 格式资费数据，并进行结构化解析
- **事务管理**：对数据处理过程进行事务控制，确保数据一致性
- **错误追踪**：完善的日志记录和错误处理机制，方便定位和解决问题

## 技术栈

- **核心框架**：Spring Boot 
- **任务调度**：Spring Schedule
- **异步处理**：Spring Async
- **数据存储**：MyBatis-Plus，MySQL
- **分布式锁**：Lock4j
- **日志管理**：Slf4j + Logback
- **工具库**：Hutool、Lombok

## 架构设计

项目采用典型的分层架构设计：

- **控制层（Controller）**：提供外部访问接口，主要是爬虫回调接口
- **服务层（Service）**：核心业务逻辑处理，包括任务创建、文件解析和数据处理
- **数据访问层（Mapper）**：负责与数据库交互
- **定时任务（Task）**：定时触发爬虫任务和检查任务状态
- **实体层（Domain）**：业务实体和数据传输对象
- **枚举（Enums）**：定义公共常量和业务枚举

## 主要流程

### 1. 资费爬取定时任务流程

```
定时任务触发(CrawlerTariffTask) 
  -> 获取当前日期ID
  -> 按顺序执行各运营商爬取(电信->移动->联通->广电)
    -> 针对每个运营商的配置省份列表
      -> 创建爬取任务(XtyCrawlerTask)并设置状态为待执行(0)
      -> 保存到数据库
```

### 2. 任务执行定时任务流程

```
定时检查任务(每分钟)
  -> 检查是否有正在运行的任务
  -> 获取一个待执行任务(status=0)
  -> 根据任务的运营商类型获取对应服务(xxxCrawlerTariffService)
  -> 异步执行具体爬虫请求
    -> 构建回调URL(包含taskId和运营商标识)
    -> 发送请求到外部爬虫服务
    -> 外部服务爬取完成后回调
```

### 3. 爬虫回调处理流程

```
回调接口接收数据(ZIP文件)
  -> 根据URL中的运营商和taskId参数，确定处理服务
  -> 将ZIP文件解压到临时目录
  -> 遍历解压后的资费数据文件
    -> 根据运营商特定格式解析文件内容
    -> 解析套餐资费信息
    -> 按照统一结构保存到数据库(XtyTariffCrawlRecord)
  -> 更新任务状态为已完成(2)
  -> 清理临时文件
```

## 模块说明

- **base**：基础常量和工具类
- **controller**：控制器，主要是爬虫回调接口
- **domain**：实体类和VO/BO对象
- **enums**：枚举定义，如运营商类型、错误类型等
- **mapper**：MyBatis接口
- **runner**：应用启动任务
- **service**：业务逻辑接口和实现
- **task**：定时任务

## 运营商支持

项目支持以下四大运营商的资费数据爬取：

1. **中国电信**（TELECOM，代码：1）
2. **中国移动**（MOBILE，代码：2）
3. **中国联通**（UNICOM，代码：3）
4. **中国广电**（GDT，代码：5）

## 快速开始

### 环境要求

- JDK 17+
- Maven 3.6+
- MySQL 5.7+
- Redis (用于分布式锁)

### 配置说明

主要配置项在 `application.yml` 中：

```yaml
# 爬虫配置
crawler:
  # 爬虫基础URL
  base-url: http://crawler-service-url
  # 本地服务URL（用于回调）
  local-server: http://your-service-url
  # 电信省份列表
  telecom-provinces: 省份1,省份2
  # 移动省份列表
  mobile-provinces: 省份1,省份2
  # 联通省份列表
  unicom-provinces: 省份1,省份2
  # 广电省份列表
  gdt-provinces: 省份1,省份2

# 文件配置
file:
  # 临时文件路径
  tmp-path: /path/to/temp
```

### 构建与运行

```bash
# 克隆项目
git clone https://your-repository/xty-crawler-service.git

# 进入项目目录
cd xty-crawler-service

# 编译打包
mvn clean package

# 运行应用
java -jar target/xty-crawler-service.jar
```

## API 接口

### 爬虫回调接口

```
POST /callback/{operator}/{taskId}
```

**参数说明：**
- `operator`：运营商代码（telecom/mobile/unicom/gdt）
- `taskId`：任务ID
- `file`：上传的ZIP格式文件（包含资费数据）

**响应：**
- 成功：`{"code": 200, "msg": "success"}`

## 定时任务

- **资费爬取任务**：每天凌晨1点执行（`0 0 1 * * ?`）
- **任务执行检查**：每分钟执行一次（`0 0/1 * * * ?`）

## 扩展指南

### 添加新的运营商支持

1. 在 `Constants` 类中添加新的运营商常量
2. 在 `OperatorEnum` 中添加新的运营商枚举
3. 创建新的运营商实现类：`NewOperatorCrawlerTariffServiceImpl`，实现 `ICrawlerTariffService` 和 `ICrawlerCallbackService` 接口
4. 在 `application.yml` 中添加新运营商的配置

## 开发团队

- 广州云趣信息科技有限公司

## 许可证

版权所有 © 2025 广州云趣信息科技有限公司

