package com.yunqu.crawler.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.base.CacheConstants;
import com.yunqu.crawler.base.Constants;
import com.yunqu.crawler.base.StringAmountUnitParser;
import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.domain.vo.XtyTariffProvinceVo;
import com.yunqu.crawler.enums.OperatorEnum;
import com.yunqu.crawler.error.CrawlerException;
import com.yunqu.crawler.event.CrawlerTaskEvent;
import com.yunqu.crawler.mapper.XtyCrawlerTaskMapper;
import com.yunqu.crawler.mapper.XtyCrawlerVersionMapper;
import com.yunqu.crawler.mapper.XtyTariffCrawlRecordMapper;
import com.yunqu.crawler.service.ICrawlerCallbackService;
import com.yunqu.crawler.service.ICrawlerTariffService;
import com.yunqu.crawler.service.IXtyTariffProvinceService;
import com.yunqu.crawler.service.impl.telecom.TelecomRecordAnalysisHandler;
import com.yunqu.emergency.common.core.exception.ServiceException;
import com.yunqu.emergency.common.core.utils.SpringUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.mybatis.annotation.Dynamic;
import com.yunqu.emergency.common.redis.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  电信资费爬取服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/15
 */
@RequiredArgsConstructor
@Slf4j
@Service(Constants.OPERATOR_TELECOM + ICrawlerTariffService.BASE_NAME)
public class TelecomCrawlerTariffServiceImpl implements ICrawlerTariffService, ICrawlerCallbackService {

    private static final String LOG_PREFIX = "[TELECOM]"; // 电信日志前缀

    // 常量定义
    private static final String FILE_NAME_SEPARATOR = "_";
    private static final String FILE_EXTENSION_SEPARATOR = ".";

    private final XtyCrawlerTaskMapper crawlerTaskMapper;
    private final XtyTariffCrawlRecordMapper xtyTariffCrawlRecordMapper;
    private final IXtyTariffProvinceService xtyTariffProvinceService;
    private final XtyCrawlerVersionMapper versionMapper;

    @Value("${crawler.local-server}")
    private String localUrl;
    @Value("${crawler.base-url}")
    private String crawlerBaseUrl;
    @Value("${crawler.telecom-provinces}")
    private String provinces;
    @Value("${crawler.telecom-provinces-name}")
    private String provincesName;
    @Value("${file.tmp-path}")
    private String localFilePath;

    /**
     * 抓取数据
     *
     * @param dateId 日期ID
     */
    @Override
    public void crawler(int dateId, String versionNo) {
        log.info("{} [TASK-START] 开始资费数据抓取任务, dateId: {}", LOG_PREFIX, dateId);

        if (StringUtils.isBlank(provinces)) {
            log.error("{} [TASK-FAIL] 资费爬虫省份配置为空", LOG_PREFIX);
            return;
        }

        String[] provinceArray = StringUtils.split(provinces, ",");
        String[] provinceNameArray = StringUtils.split(provincesName, ",");
        log.info("{} [TASK-INFO] 需要抓取的省份数量: {}", LOG_PREFIX, provinceArray.length);

        for (int i = 0; i < provinceArray.length; i++) {
            String province = provinceArray[i];
            String provinceName = provinceNameArray[i];
            log.info("{} [TASK-PROCESS] 开始创建资费爬虫任务, 省份: {}", LOG_PREFIX, province);

            try {
                XtyCrawlerTask crawlerTask = new XtyCrawlerTask();
                crawlerTask.setDateId(dateId);
                crawlerTask.setProvinceCode(province);
                crawlerTask.setProvinceName(provinceName);
                crawlerTask.setStatus(0);
                crawlerTask.setEntType(1);
                crawlerTask.setOperatorCode(OperatorEnum.TELECOM.getAlias());
                crawlerTask.setOperatorName(OperatorEnum.TELECOM.getName());
                crawlerTask.setVersionNo(versionNo);
                crawlerTaskMapper.insert(crawlerTask);
                log.info("{} [TASK-SUCCESS] 成功创建资费爬虫任务, taskId: {}, 省份: {}", LOG_PREFIX, crawlerTask.getId(), province);
            } catch (Exception e) {
                log.error("{} [TASK-FAIL] 创建资费爬虫任务失败, 省份: {}, 错误: {}", LOG_PREFIX, province, e.getMessage(), e);
            }
        }

        log.info("{} [TASK-END] 完成资费数据抓取任务创建, dateId: {}", LOG_PREFIX, dateId);
    }

    /**
     * 执行请求爬虫
     *
     * @param task 爬虫任务
     */
    @Async
    @Override
    public void excueteRequestCrawler(XtyCrawlerTask task) {
        log.info("{} [CRAWLER-START] 开始执行资费爬虫请求, taskId: {}, 省份: {}", LOG_PREFIX, task.getId(), task.getProvinceCode());

        String operatorCode = task.getOperatorCode();
        String callback = localUrl + "/callback/" + operatorCode + "/" + task.getId();

        Map<String, String> map = new HashMap<>();
        map.put("callback_url", callback);
        map.put("provinces", task.getProvinceCode());
        map.put("crawler_type", OperatorEnum.TELECOM.getAlias());

        try {
            log.info("{} [CRAWLER-PROCESS] 发送爬虫请求, taskId: {}, url: {}, 参数: {}", LOG_PREFIX, task.getId(), crawlerBaseUrl + "/api/v1/crawl", map);
            requestToCrawler(crawlerBaseUrl + "/api/v1/crawl", JSONUtil.toJsonStr(map));
            log.info("{} [CRAWLER-SUCCESS] 成功发送爬虫请求, taskId: {}", LOG_PREFIX, task.getId());
        } catch (Exception e) {
            log.error("{} [CRAWLER-FAIL] 发送爬虫请求失败, taskId: {}, 错误: {}", LOG_PREFIX, task.getId(), e.getMessage(), e);
        }
    }

    /**
     * 回调接口
     *
     * @param taskId 任务id
     * @param file   回调文件
     */
    @Override
    public void callback(Long taskId, MultipartFile file, String crawlerUrl) {
        log.info("{} [CALLBACK-START] 收到资费爬虫回调, taskId: {}, 文件名: {}", LOG_PREFIX, taskId, file.getOriginalFilename());
        long startTime = System.currentTimeMillis();

        XtyCrawlerTask task = crawlerTaskMapper.selectById(taskId);
        String tempDirPath = localFilePath + File.separator + taskId;
        try {
            String versionNo = task.getVersionNo();
            SpringUtils.getAopProxy(this).excute(taskId, versionNo, file, crawlerUrl, task, tempDirPath, startTime);
        } catch (CrawlerException e) {
            log.error(e.getMessage(), e);
            task.setStatus(0);
            task.setMemo(e.getMessage());
            crawlerTaskMapper.updateById(task);
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        } finally {
            ICrawlerCallbackService.deleteDirectory(tempDirPath);
        }
    }

    @Transactional(rollbackFor = {Exception.class, RuntimeException.class, ServiceException.class, CrawlerException.class})
    @Dynamic(version = "versionNo")
    protected void excute(Long taskId, String versionNo, MultipartFile file, String crawlerUrl, XtyCrawlerTask task, String tempDirPath, long startTime) throws IOException {
        JSONObject provinceInfo = RedisUtils.getCacheMapValue(CacheConstants.TELECOM_PROVINCE_INFO_DICT_KEY, task.getProvinceCode());
        String provinceCode = provinceInfo.getStr("provinceCode");
        String provinceName = provinceInfo.getStr("provinceName");
        String taskProvinceName = provinceInfo.getStr("name");
        String crawlerCallbackFileName = file.getOriginalFilename();
        String zipFileFolderPath = localFilePath + File.separator + task.getVersionNo() + File.separator + OperatorEnum.TELECOM.getAlias();
        File zipFileFolder = new File(zipFileFolderPath);
        if (!zipFileFolder.exists()) zipFileFolder.mkdirs();
        String zipFilePath = zipFileFolderPath + File.separator + crawlerCallbackFileName;
        File zipFile = FileUtil.file(zipFilePath);
        file.transferTo(zipFile);
        File folder = new File(tempDirPath);
        ICrawlerCallbackService.unzipFile(zipFile, folder, System.currentTimeMillis());

        if (task == null) {
            log.error("{} [CALLBACK-FAIL] 未找到对应的任务, taskId: {}", LOG_PREFIX, taskId);
            throw new RuntimeException("未找到对应的任务");
        }
        try {
            log.warn("{} [CALLBACK-WARN] 回调处理逻辑尚未实现, taskId: {}", LOG_PREFIX, taskId);
            Map<String, Integer> map = processUnzippedFiles(folder, task, file.getOriginalFilename(), crawlerUrl, versionNo, provinceCode, provinceName, taskProvinceName);
            if (map == null) {
                return;
            }
            Integer localProvinceCount = map.get("localProvinceCount");
            Integer groupCount = map.get("groupCount");
            if (localProvinceCount == 0 || groupCount == 0) {
                log.warn("{} [FILE-WARN] 本省资费或全网资费爬取异常, 忽略更新任务状态, 运营商：{}{}, localProvinceCount:{}, groupCount:{}", LOG_PREFIX, provincesName, OperatorEnum.TELECOM.getName(), localProvinceCount, groupCount);
                throw new CrawlerException("本省资费或全网资费爬取异常");
            }
            task.setStatus(2);
            task.setLocalProvinceCount(localProvinceCount);
            task.setGroupCount(groupCount);
            task.setCrawlerEndTime(new Date());
            crawlerTaskMapper.updateById(task);
            log.info("{} [CALLBACK-SUCCESS] 完成资费爬虫回调处理, taskId: {}, 耗时: {}ms", LOG_PREFIX, taskId, System.currentTimeMillis() - startTime);
            SpringUtils.publishEvent(new CrawlerTaskEvent(taskId));
        } catch (CrawlerException e) {
            log.error("{} [CALLBACK-FAIL] 回调处理异常, taskId: {}, 错误: {}", LOG_PREFIX, taskId, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("{} [CALLBACK-FAIL] 回调处理异常, taskId: {}, 错误: {}", LOG_PREFIX, taskId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理解压后的文件
     *
     * @param folder 解压后的文件夹
     * @param task 任务
     * @param crawlerCallbackFileName 回调文件名
     * @param crawlerUrl 爬虫url
     * @param versionNo 版本号
     */
    private Map<String, Integer> processUnzippedFiles(File folder, XtyCrawlerTask task, String crawlerCallbackFileName,
                                                      String crawlerUrl, String versionNo, String provinceCode, String provinceName, String taskProvinceName) {
        if (!folder.isDirectory()) {
            log.error("{} [CALLBACK-FAIL] 解压文件夹不存在, taskId: {}, 文件名: {}", LOG_PREFIX, task.getId(), crawlerCallbackFileName);
            throw new RuntimeException("解压文件夹不存在");
        }
        File[] listOfFiles = folder.listFiles();
        if (listOfFiles == null) {
            log.error("{} [CALLBACK-FAIL] 解压文件为空, taskId: {}, 文件名: {}", LOG_PREFIX, task.getId(), crawlerCallbackFileName);
            throw new RuntimeException("解压文件为空");
        }


        if (listOfFiles.length < 2) {
            task.setStatus(0);
            crawlerTaskMapper.updateById(task);
            log.info("{} [TASK-UPDATE] 状态更新成功, taskId: {}", LOG_PREFIX, task.getId());
            return null;
        }

        int localProvinceCount = 0;
        int groupCount = 0;
        for (File tariffFile : listOfFiles) {
            Map<String, Integer> map = processSingleTariffFile(tariffFile, task, crawlerCallbackFileName, crawlerUrl, versionNo, provinceCode, provinceName, taskProvinceName);
            localProvinceCount += map.get("localProvinceCount");
            groupCount += map.get("groupCount");
        }
        return Map.of("localProvinceCount", localProvinceCount, "groupCount", groupCount);
    }

    private Map<String, Integer> processSingleTariffFile(File tariffFile, XtyCrawlerTask task, String callbackFileName,
                                                         String crawlerUrl, String versionNo, String provinceCode, String provinceName, String taskProvinceName) {
        String fileName = tariffFile.getName();
        log.info("{} [FILE-START] 开始处理资费文件: {}", LOG_PREFIX, fileName);
        FileNameInfo fileNameInfo = parseFileName(fileName, task.getProvinceName());
        String content = readFileContent(tariffFile);
        int localProvinceCount = 0;
        int groupCount = 0;
        if (StringUtils.isBlank(content)) {
            log.warn("{} [FILE-WARN] 文件内容为空: {}", LOG_PREFIX, fileName);
            return Map.of("localProvinceCount", localProvinceCount, "groupCount", groupCount);
        }

        if (StringUtils.equals("全网资费", fileNameInfo.crawlerType)) {
            // 集团资费仅需爬取一次即可
            try {
                JSONObject entries = JSONUtil.parseObj(content);
                log.debug("{} [FILE-SUCCESS] 解析JSON内容成功, 文件: {}, 数据条数: {}", LOG_PREFIX, fileName, entries.size());
                for (String key : entries.keySet()) {
                    try {
                        String[] classicType = StringUtils.split(key, FILE_NAME_SEPARATOR);
                        groupCount = groupCount + processClassicType(classicType, entries.getJSONArray(key), fileNameInfo, task, callbackFileName, crawlerUrl, versionNo, provinceCode, provinceName, taskProvinceName);
                    } catch (Exception e) {
                        log.error("{} [FILE-FAIL] 解析文件名失败, 文件: {}, 错误: {}", LOG_PREFIX, fileName, e.getMessage(), e);
                    }
                }
            } catch (Exception e) {
                log.error("{} [FILE-FAIL] 处理资费文件失败, 文件: {}, 错误: {}", LOG_PREFIX, fileName, e.getMessage(), e);
            }
        } else {
            int size = analysisLocalProvinceTariff(task, callbackFileName, crawlerUrl, content, fileName, provinceCode, provinceName, taskProvinceName);
            localProvinceCount = localProvinceCount + size;
        }

        return Map.of("localProvinceCount", localProvinceCount, "groupCount", groupCount);
    }


    private int analysisLocalProvinceTariff(XtyCrawlerTask task, String callbackFileName,
                                            String crawlerUrl, String content, String fileName, String provinceCode, String provinceName, String taskProvinceName) {

        List<XtyTariffCrawlRecord> records = TelecomRecordAnalysisHandler.getInstance().analysisCrawlerData(content, fileName, task.getProvinceCode());
        if (CollectionUtil.isEmpty(records)) {
            log.warn("{} [FILE-WARN] 无效的资费文件, 文件名: {}", LOG_PREFIX, callbackFileName);
            return 0;
        }

        if ("js".equals(task.getProvinceCode())) {
            records = records.stream().filter(o -> {
                String name = StringUtils.trimToEmpty(o.getName());
                String tariffNo = StringUtils.trimToEmpty(o.getTariffNo());
                String versionNo = o.getVersionNo();
                String key = name + tariffNo + versionNo;
                return RedisUtils.setObjectIfAbsent(key, "1", Duration.ofHours(2));
            }).toList();
        }

        String crawlerType = task.getProvinceName() + "资费";
        String tariffType;
        if (StringUtils.isNotBlank(fileName)) {
            String tmpName = fileName.substring(0, fileName.lastIndexOf("."));
            String[] tmpNames = tmpName.split("_");
            tariffType = tmpNames[tmpNames.length - 1];
        } else {
            tariffType = "";
        }
        //安徽,河北,黑龙江,江西,宁夏,新疆,云南,山东,广东,甘肃 等地市不需要从文件名中获取二级分类信息
        String rejectProvince = "ah,he,hl,jx,nx,xj,yn,sd,gd,gs,fj";
        List<XtyTariffCrawlRecord> list = records.stream().filter(o -> !StringUtils.equals("未知编号", o.getTariffNo()) || !StringUtils.equals("未知标题", o.getName())).peek(o -> {
            o.setTaskId(task.getId());
            o.setDateId(task.getDateId());
            o.setVersionNo(task.getVersionNo());
            o.setProvinceCode(provinceCode);
            o.setProvinceName(provinceName);
            o.setEntCode(OperatorEnum.TELECOM.getCode());
            o.setEntName(OperatorEnum.TELECOM.getName());
            o.setCrawlUrl(crawlerUrl);
            o.setCrawlerProvinceName(taskProvinceName);
            o.setCrawlerType(crawlerType);
            if (!rejectProvince.contains(task.getProvinceCode())) {
                o.setTariffType(tariffType);
            }
            o.setCrawlerCallbackFileName(callbackFileName);
            o.setCrawlerDataBelongFileName(fileName);

            String tariffNo = o.getTariffNo();
            if (StringUtils.startsWith(tariffNo, "方案编号：")) {
                tariffNo = tariffNo.replace("方案编号：", "");
                o.setTariffNo(tariffNo);
            } else if (StringUtils.equals(tariffNo, "未知编号")) {
                tariffNo = "";
                o.setTariffNo(tariffNo);
            }

            if (StringUtils.isNotBlank(tariffNo) && tariffNo.length() >= 4) {
                try {
                    String tariffProvinceCode = tariffNo.substring(2, 4);
                    XtyTariffProvinceVo tariffProvince = xtyTariffProvinceService.getByTariffProvinceCode(tariffProvinceCode);
                    o.setTariffProvinceCode(tariffProvinceCode);
                    o.setTariffProvinceName(tariffProvince.getProvinceName());
                    log.debug("{} [DATA-SUCCESS] 省份解析成功, tariffNo: {}, 省份: {}", LOG_PREFIX, tariffNo, tariffProvince.getProvinceName());
                } catch (Exception e) {
                    log.error("{} [DATA-FAIL] 省份解析失败, tariffNo: {}, 错误: {}", LOG_PREFIX, tariffNo, e.getMessage());
                }
            } else {
                log.warn("{} [DATA-WARN] 资费编号异常, tariffNo: {}", LOG_PREFIX, tariffNo);
            }
        }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(list)) {
            xtyTariffCrawlRecordMapper.insertBatch(list);
        }
        return list.size();
    }

    private Integer processClassicType(String[] classicType, JSONArray tariffDetailInfoList, FileNameInfo fileFormatInfo,
                                       XtyCrawlerTask crawlerTask, String callbackFileName, String crawlerUrl, String versionNo, String provinceCode, String provinceName, String taskProvinceName) {
        if (classicType.length < 2) {
            log.warn("{} [FILE-WARN] 无效的经典类型格式: {}", LOG_PREFIX, String.join(FILE_NAME_SEPARATOR, classicType));
            return 0;
        }
        String classicTypeOne = classicType[0];
        String classicTypeTwo = classicType[1];
        log.debug("{} [FILE-SUCCESS] 处理资费类型数据: classicTypeOne={}, classicTypeTwo={}", LOG_PREFIX, classicTypeOne, classicTypeTwo);

        List<XtyTariffCrawlRecord> records = new ArrayList<>();
        for (int i = 0; i < tariffDetailInfoList.size(); i++) {
            try {
                JSONObject tariffDetailInfo = tariffDetailInfoList.getJSONObject(i);
                XtyTariffCrawlRecord record = processBasicBusiness(tariffDetailInfo, fileFormatInfo, crawlerTask,
                        callbackFileName, classicTypeOne, classicTypeTwo, crawlerUrl, versionNo, provinceCode);
                if (record == null) continue;
                record.setExtendedFields(JSONUtil.toJsonStr(tariffDetailInfo));
                record.setProvinceCode(provinceCode);
                record.setProvinceName(provinceName);
                record.setCrawlerProvinceName(taskProvinceName);
                records.add(record);
            } catch (Exception e) {
                log.error("{} [FILE-FAIL] 处理资费明细数据失败, index: {}, 错误: {}", LOG_PREFIX, i, e.getMessage(), e);
                throw new RuntimeException(e.getMessage(), e);
            }
        }
        if (CollectionUtil.isNotEmpty(records)) {
            xtyTariffCrawlRecordMapper.insertBatch(records);
        }
        return records.size();
    }


    private XtyTariffCrawlRecord processBasicBusiness(JSONObject tariffDetailInfo, FileNameInfo fileNameInfo, XtyCrawlerTask crawlerTask,
                                                      String callbackFileName, String classicTypeOne, String classicTypeTwo,
                                                      String crawlerUrl, String versionNo, String provinceCode) {
        String name = StringUtils.trimToEmpty(tariffDetailInfo.getStr("title"));
        String tariffNo = StringUtils.trimToEmpty(tariffDetailInfo.getStr("code"));
        if (StringUtils.equals("未知编号", tariffNo) && StringUtils.equals("未知标题", name)) {
            return null;
        }
        JSONObject basicInfo = tariffDetailInfo.getJSONObject("basic_info");
        JSONObject serviceContent = tariffDetailInfo.getJSONObject("service_content");


        XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
        record.setTariffType(fileNameInfo.crawlerType);
        record.setTaskId(crawlerTask.getId());
        record.setDateId(crawlerTask.getDateId());
        record.setProvinceName(crawlerTask.getProvinceName());
        record.setProvinceCode(provinceCode);
        record.setVersionNo(versionNo);
        record.setEntCode(OperatorEnum.TELECOM.getCode());
        record.setEntName(OperatorEnum.TELECOM.getName());
        record.setName(name);
        record.setTariffNo(tariffNo);
        record.setCrawlerType(fileNameInfo.crawlerType);
        if (StringUtils.isNotBlank(tariffNo)) {
            try {
                String tariffProvinceCode = tariffNo.substring(2, 4);
                XtyTariffProvinceVo tariffProvince = xtyTariffProvinceService.getByTariffProvinceCode(tariffProvinceCode);
                record.setTariffProvinceCode(tariffProvinceCode);
                record.setTariffProvinceName(tariffProvince.getProvinceName());
                log.debug("{} [DATA-SUCCESS] 省份解析成功, tariffNo: {}, 省份: {}", LOG_PREFIX, tariffNo, tariffProvince.getProvinceName());
            } catch (Throwable e) {
                log.error("{} [DATA-FAIL] 省份解析失败, tariffNo: {}, 错误: {}", LOG_PREFIX, tariffNo, e.getMessage());
            }
        }

        String feeInfo = StringUtils.trimToEmpty(basicInfo.getStr("资费标准"));
        if (StringUtils.isNotBlank(feeInfo)) {
            StringAmountUnitParser.AmountUnitResult parseResult = StringAmountUnitParser.parse(feeInfo);
            if (parseResult != null) {
                String amount = parseResult.getAmount();
                String unit = parseResult.getUnit();
                record.setFees(amount);
                record.setFeesUnit(unit);
            }
        }
        String tariffType = StringUtils.trimToEmpty(basicInfo.getStr("资费类型"));
        record.setTariffType(tariffType);

        // 适用范围
        String scop = StringUtils.trimToEmpty(basicInfo.getStr("适用范围"));
        record.setApplicablePeople(scop);

        String validPeriod = StringUtils.trimToEmpty(basicInfo.getStr("有效期限"));
        record.setValidPeriod(validPeriod);

        String salesChannel = StringUtils.trimToEmpty(basicInfo.getStr("销售渠道"));
        record.setChannel(salesChannel);

        String duration = StringUtils.trimToEmpty(basicInfo.getStr("在网要求"));
        record.setDuration(duration);

        String unsubscribe = StringUtils.trimToEmpty(basicInfo.getStr("退订方式"));
        record.setUnsubscribe(unsubscribe);

        String responsibility = StringUtils.trimToEmpty(basicInfo.getStr("违约责任"));
        record.setResponsibility(responsibility);

        String date = StringUtils.trimToEmpty(basicInfo.getStr("上下线时间"));
        if (StringUtils.isNotBlank(date)) {
            String[] dateArr = date.split("至");
            String dateBeginStr = StringUtils.trimToEmpty(dateArr[0]);
            String dateEndStr = StringUtils.trimToEmpty(dateArr[1]);
            record.setOnlineDay(formatDateStr(dateBeginStr));
            record.setOfflineDay(formatDateStr(dateEndStr));
        }

        String callInfo = StringUtils.trimToEmpty(serviceContent.getStr("国内语音"));
        if (StringUtils.isNotBlank(callInfo)) {
            StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(callInfo);
            if (parse != null) {
                record.setCall(parse.getAmount());
            }
        }

        // 通用流量
        String dataInfo = StringUtils.trimToEmpty(serviceContent.getStr("通用流量"));
        if (StringUtils.isNotBlank(dataInfo)) {
            StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(dataInfo);
            if (parse != null) {
                record.setData(parse.getAmount());
                record.setDataUnit(parse.getUnit());
            }
        }

        // 短信
        String smsInfo = StringUtils.trimToEmpty(serviceContent.getStr("国内短彩信"));
        if (StringUtils.isNotBlank(smsInfo)) {
            record.setSms(smsInfo);
        }
        // 定向流量
        String orientTrafficInfo = StringUtils.trimToEmpty(serviceContent.getStr("定向流量"));
        if (StringUtils.isNotBlank(orientTrafficInfo)) {
            StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(orientTrafficInfo);
            if (parse != null) {
                record.setOrientTraffic(parse.getAmount());
                record.setOrientTrafficUnit(parse.getUnit());
            }
        }

        // iptv
        String iptv = StringUtils.trimToEmpty(serviceContent.getStr("IPTV"));
        record.setIptv(iptv);

        /// 带宽
        String bandwith = StringUtils.trimToEmpty(serviceContent.getStr("带宽"));
        record.setBandwidth(bandwith);

        String rights = StringUtils.trimToEmpty(serviceContent.getStr("权益"));
        record.setRights(rights);


        String remark = StringUtils.trimToEmpty(tariffDetailInfo.getStr("remark"));
        record.setOtherContent(remark);
        String others = tariffDetailInfo.getStr("others");
        record.setOthers(others);

        record.setCrawlUrl(crawlerUrl);
        record.setClassicTypeOne(classicTypeOne);
        record.setClassicTypeTwo(classicTypeTwo);
        record.setCrawlerCallbackFileName(callbackFileName);
        record.setCrawlerDataBelongFileName(fileNameInfo.originalFileName);
        record.setCrawlerProvinceName(crawlerTask.getProvinceName());
        // xtyTariffCrawlRecordMapper.insert(record);
        return record;
    }

    /**
     * 解析文件名信息
     */
    private FileNameInfo parseFileName(String fileName, String provinceName) {
        log.debug("{} [FILE-START] 开始解析文件名: {}", LOG_PREFIX, fileName);
        FileNameInfo info = new FileNameInfo();
        info.originalFileName = fileName;

        try {
            String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf(FILE_EXTENSION_SEPARATOR));
            String[] parts = nameWithoutExt.split(FILE_NAME_SEPARATOR);

            if (parts.length > 0) {
                info.provinceName = parts[0];
            }
            if (parts.length > 1) {
                String crawlerType = parts[parts.length - 1];
                if (StringUtils.equals("集团资费公示", crawlerType)) {
                    crawlerType = "全网资费";
                } else {
                    crawlerType = provinceName + "资费";
                }
                info.crawlerType = crawlerType;
            }
            log.debug("{} [FILE-SUCCESS] 文件名解析完成: provinceName={}, crawlerType={}", LOG_PREFIX, info.provinceName, info.crawlerType);
        } catch (Exception e) {
            log.error("{} [FILE-FAIL] 解析文件名失败: {}, 错误: {}", LOG_PREFIX, fileName, e.getMessage(), e);
        }

        return info;
    }


    /**
     * 文件名信息存储类
     */
    private static class FileNameInfo {
        String originalFileName;
        String provinceName;
        String crawlerType;
    }
}
