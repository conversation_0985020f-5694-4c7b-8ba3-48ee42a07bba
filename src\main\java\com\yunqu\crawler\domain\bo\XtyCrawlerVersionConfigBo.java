package com.yunqu.crawler.domain.bo;

import com.yunqu.crawler.domain.XtyCrawlerVersionConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.util.Date;

/**
 * 爬虫版本生成配置业务对象 xty_crawler_version_config
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@AutoMapper(target = XtyCrawlerVersionConfig.class, reverseConvertGenerate = false)
public class XtyCrawlerVersionConfigBo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置类型：SYSTEM-系统配置，CUSTOM-自定义配置
     */
    private String configType;

    /**
     * 执行日期：每月的第几天（1-31）
     */
    private String ruleValue;

    /**
     * 是否启用：Y-启用，N-禁用
     */
    private String isEnabled;

    /**
     * 优先级顺序，数字越小优先级越高
     */
    private Long priorityOrder;

    /**
     * 生效类型 1：长期有效 2：固定时限
     */
    private Integer effectiveType;

    /**
     * 生效开始日期，格式：yyyy-MM-dd
     */
    private Date effectiveStartDate;

    /**
     * 生效结束日期，格式：yyyy-MM-dd
     */
    private Date effectiveEndDate;

    /**
     * 配置描述
     */
    private String description;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 备注信息
     */
    private String remark;


}
