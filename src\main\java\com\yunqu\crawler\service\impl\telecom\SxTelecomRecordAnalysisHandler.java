package com.yunqu.crawler.service.impl.telecom;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.util.ParseDateUtil;
import com.yunqu.emergency.common.core.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 山西电信资费数据处理器
 * </p>
 *
 * @ClassName SxTelecomRecordAnalysisHandler
 * <AUTHOR> Copy This Tag)
 * @Description 山西电信资费数据解析处理
 * @Since create in 2025/5/28 14:08
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
public class SxTelecomRecordAnalysisHandler implements IRecordAnalysisHandler {
    @Override
    public List<XtyTariffCrawlRecord> analysis(String content) {
        JSONObject entries = JSONUtil.parseObj(content);
        JSONArray data = entries.getJSONArray("data");
        return analysisJson(data);
    }



    private List<XtyTariffCrawlRecord> analysisJson(JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.size() == 0) {
            return List.of();
        }

        List<XtyTariffCrawlRecord> records = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
            record.setExtendedFields(JSONUtil.toJsonStr(object));
            record.setName(StringUtils.trimToEmpty(object.getStr("name")));
            record.setTariffNo(StringUtils.trimToEmpty(object.getStr("reportNo")));

            record.setFees(StringUtils.trimToEmpty(object.getStr("fees")));
            record.setFeesUnit(StringUtils.trimToEmpty(object.getStr("feesUnit")));
            record.setCall(StringUtils.trimToEmpty(object.getStr("call")));
            record.setData(StringUtils.trimToEmpty(object.getStr("data")));
            record.setDataUnit(StringUtils.trimToEmpty(object.getStr("dataUnit")));
            record.setSms(StringUtils.trimToEmpty(object.getStr("sms")));
            record.setOrientTraffic(StringUtils.trimToEmpty(object.getStr("orientTraffic")));
            record.setOrientTrafficUnit(StringUtils.trimToEmpty(object.getStr("orientTrafficUnit")));
            record.setIptv(StringUtils.trimToEmpty(object.getStr("iptv")));
            record.setBandwidth(StringUtils.trimToEmpty(object.getStr("bandwidth")));
            record.setRights(StringUtils.trimToEmpty(object.getStr("rights")));
            record.setOtherContent(StringUtils.trimToEmpty(object.getStr("otherContent")));
            String onlineDay = StringUtils.trimToEmpty(object.getStr("onlineDay"));
            if (StringUtils.isNotBlank(onlineDay)) {
                record.setOnlineDay(ParseDateUtil.formatDateStr(onlineDay));
            }
            String offlineDay = StringUtils.trimToEmpty(object.getStr("offlineDay"));
            if (StringUtils.isNotBlank(offlineDay)) {
                record.setOfflineDay(ParseDateUtil.formatDateStr(offlineDay));
            }

            record.setApplicablePeople(StringUtils.trimToEmpty(object.getStr("applicablePeople")));
            record.setValidPeriod(StringUtils.trimToEmpty(object.getStr("validPeriod")));
            record.setChannel(StringUtils.trimToEmpty(object.getStr("channel")));
            record.setDuration(StringUtils.trimToEmpty(object.getStr("duration")));
            record.setUnsubscribe(StringUtils.trimToEmpty(object.getStr("unsubscribe")));
            record.setResponsibility(StringUtils.trimToEmpty(object.getStr("responsibility")));
            record.setOthers(StringUtils.trimToEmpty(object.getStr("others")));
            records.add(record);
        }
        return records;
    }
}
