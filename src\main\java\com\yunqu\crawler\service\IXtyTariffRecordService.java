package com.yunqu.crawler.service;

import com.yunqu.crawler.domain.bo.XtyTariffRecordBo;
import com.yunqu.crawler.domain.vo.XtyTariffRecordVo;
import com.yunqu.emergency.common.mybatis.core.page.TableDataInfo;
import com.yunqu.emergency.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 资费记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface IXtyTariffRecordService {

    /**
     * 查询资费记录
     *
     * @param id 主键
     * @return 资费记录
     */
    XtyTariffRecordVo queryById(String id);

    /**
     * 分页查询资费记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 资费记录分页列表
     */
    TableDataInfo<XtyTariffRecordVo> queryPageList(XtyTariffRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的资费记录列表
     *
     * @param bo 查询条件
     * @return 资费记录列表
     */
    List<XtyTariffRecordVo> queryList(XtyTariffRecordBo bo);

    /**
     * 新增资费记录
     *
     * @param bo 资费记录
     * @return 是否新增成功
     */
    Boolean insertByBo(XtyTariffRecordBo bo);

    /**
     * 修改资费记录
     *
     * @param bo 资费记录
     * @return 是否修改成功
     */
    Boolean updateByBo(XtyTariffRecordBo bo);

    /**
     * 校验并批量删除资费记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
