package com.yunqu.crawler.util;

import com.yunqu.emergency.common.core.exception.ServiceException;
import com.yunqu.emergency.common.core.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * 动态表操作工具类
 */
@Slf4j
public class TableUtils {

    private static final String LOG_PREFIX = "[TABLE]";
    private static final String BASE_TABLE_NAME = "xty_tariff_crawl_record";

    /**
     * 创建爬虫数据表
     *
     * @param versionNo 版本号
     * @return 是否创建成功
     */
    public static String createCrawlRecordTable(String versionNo) {
        String tableName = generateTableName(versionNo);
        String createTableSql = generateCreateTableSql(tableName);

        try {
            JdbcTemplate jdbcTemplate = SpringUtils.getBean(JdbcTemplate.class);
            jdbcTemplate.execute(createTableSql);
            log.info("{} Successfully created table: {}", LOG_PREFIX, tableName);
            return tableName;
        } catch (Exception e) {
            log.error("{} Failed to create table: {}, error: {}", LOG_PREFIX, tableName, e.getMessage(), e);
            throw new ServiceException("创建公式库版本表失败，失败原因:" + e.getMessage());
        }
    }

    /**
     * 检查表是否存在
     *
     * @param versionNo 版本号
     * @return 表是否存在
     */
    public static boolean isTableExists(String versionNo) {
        String tableName = generateTableName(versionNo);
        String checkSql = "SELECT COUNT(1) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?";

        try {
            JdbcTemplate jdbcTemplate = SpringUtils.getBean(JdbcTemplate.class);
            Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class, tableName);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("{} Failed to check table existence: {}, error: {}", LOG_PREFIX, tableName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 生成表名
     *
     * @param versionNo 版本号
     * @return 表名
     */
    public static String generateTableName(String versionNo) {
        return BASE_TABLE_NAME + "_" + versionNo.toLowerCase();
    }

    /**
     * 生成建表SQL
     *
     * @param newTableName 新表名
     * @return 建表SQL
     */
    private static String generateCreateTableSql(String newTableName) {
        return String.format(
                "CREATE TABLE IF NOT EXISTS %s LIKE %s",
                newTableName,
                BASE_TABLE_NAME
        );
    }
}
