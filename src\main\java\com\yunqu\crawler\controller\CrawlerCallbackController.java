package com.yunqu.crawler.controller;

import cn.hutool.core.io.FileUtil;
import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.crawler.domain.bo.ResponseNotifyBo;
import com.yunqu.crawler.domain.entity.CrawlerCallbackData;
import com.yunqu.crawler.domain.vo.XtyCrawlerTaskVo;
import com.yunqu.crawler.service.ICrawlerCallbackService;
import com.yunqu.crawler.service.ICrawlerTariffService;
import com.yunqu.crawler.service.IXtyCrawlerTaskService;
import com.yunqu.emergency.common.core.domain.R;
import com.yunqu.emergency.common.core.utils.SpringUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 爬虫回调接口
 * 处理各运营商爬虫任务的回调请求，包括：
 * 1. 验证任务状态
 * 2. 分发到对应运营商的回调处理服务
 * 3. 统一的错误处理和日志记录
 */
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/callback")
@RestController
public class CrawlerCallbackController {

    private static final String LOG_PREFIX = "[CALLBACK]"; // 回调日志前缀

    private final IXtyCrawlerTaskService crawlerTaskService;

    /**
     * 统一的运营商回调处理入口
     * 主要流程：
     * 1. 验证任务是否存在和状态是否正确
     * 2. 根据运营商类型获取对应的回调处理服务
     * 3. 调用具体的回调处理逻辑
     *
     * @param operator 运营商代码
     * @param taskId   任务ID
     * @param data     回调数据，包含文件和爬虫URL
     * @return 处理结果
     */
    @PostMapping("/{operator}/{taskId}")
    public R<Void> callback(@PathVariable("operator") String operator, @PathVariable("taskId") Long taskId,
                            CrawlerCallbackData data) {
        log.info("{} [REQUEST-START] 收到运营商回调请求, operator: {}, taskId: {}, data:{}", LOG_PREFIX, operator, taskId, data.getStatus());
        long startTime = System.currentTimeMillis();

        try {
            // 1. 验证任务是否存在
            XtyCrawlerTaskVo crawlerTask = crawlerTaskService.queryById(taskId);
            if (crawlerTask == null) {
                log.error("{} [TASK-ERROR] 任务不存在, operator: {}, taskId: {}", LOG_PREFIX, operator, taskId);
                return R.fail("任务不存在");
            }

            String resultStatus = data.getStatus();
            if (StringUtils.equals("500", resultStatus)) {
                crawlerTaskService.updateById(crawlerTask.getId(), 0, "python爬虫异常");
                return R.ok();
            }

            // 2. 验证任务状态
            Integer status = crawlerTask.getStatus();
            if (status != 1) {
                log.error("{} [STATUS-ERROR] 任务状态异常, operator: {}, taskId: {}, status: {}",
                        LOG_PREFIX, operator, taskId, status);
                return R.fail("任务状态异常");
            }

            MultipartFile file = data.getFile();
            if (file == null) {
                log.error("{} [FILE-ERROR] 文件为空, operator: {}, taskId: {}", LOG_PREFIX, operator, taskId);
                return R.fail("文件为空");
            }

            String fileName = file.getOriginalFilename();
            String fileExt = FileUtil.extName(fileName);

            if (!"zip".equals(fileExt)) {
                log.error("{} [FILE-ERROR] 文件格式异常, operator: {}, taskId: {}, fileName: {}",
                        LOG_PREFIX, operator, taskId, fileName);
                return R.fail("文件格式异常");
            }

            // 3. 获取对应的回调处理服务
            String callbackBeanName = operator + ICrawlerTariffService.BASE_NAME;
            log.debug("{} [SERVICE-INFO] 获取回调处理服务, beanName: {}", LOG_PREFIX, callbackBeanName);

            ICrawlerCallbackService callbackService = SpringUtils.getBean(callbackBeanName,
                    ICrawlerCallbackService.class);

            // 4. 执行回调处理
            log.info("{} [PROCESS-START] 开始处理回调数据, operator: {}, taskId: {}, fileName: {}",
                    LOG_PREFIX, operator, taskId, fileName);

            callbackService.callback(taskId, file, data.getCrawlerUrl());

            log.info("{} [REQUEST-SUCCESS] 完成回调请求处理, operator: {}, taskId: {}, 耗时: {}ms",
                    LOG_PREFIX, operator, taskId, System.currentTimeMillis() - startTime);

            return R.ok();
        } catch (Throwable e) {
            log.error("{} [REQUEST-FAIL] 回调请求处理异常, operator: {}, taskId: {}, 错误: {}",
                    LOG_PREFIX, operator, taskId, e.getMessage(), e);
            XtyCrawlerTask task = crawlerTaskService.getById(taskId);
            task.setMemo(e.getMessage());
            task.setStatus(3);
            crawlerTaskService.updateById(task);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 运营商回调通知
     *
     * @param bo 回调通知数据
     * @return 处理结果
     */
    @PostMapping("/notify")
    public R<Void> notify(@RequestBody ResponseNotifyBo bo) {
        log.info("{} [NOTIFY-START] 运营商回调通知, bo: {}", LOG_PREFIX, bo);
        crawlerTaskService.notify();
        return R.ok();
    }

}
