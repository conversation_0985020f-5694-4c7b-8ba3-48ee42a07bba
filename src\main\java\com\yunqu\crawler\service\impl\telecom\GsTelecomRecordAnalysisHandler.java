package com.yunqu.crawler.service.impl.telecom;

import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.yunqu.emergency.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 甘肃电信流量包解析器
 *
 * @ClassName GsTelecomRecordAnalysisHandler
 * <AUTHOR> Copy This Tag)
 * @Description 甘肃电信流量包解析器
 * @Since create in 2025/5/28 14:09
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@Slf4j
public class GsTelecomRecordAnalysisHandler extends BaseTelecomRecordAnalysisHandler {

    /**
     * 解析HTML内容，获取index-content下的所有list-box div节点
     * @param htmlContent HTML内容
     * @return list-box div元素列表
     */
    public List<Element> parseVanListDivs(String htmlContent) {
        if (StringUtils.isBlank(htmlContent)) {
            return new ArrayList<>();
        }

        try {
            Document document = Jsoup.parse(htmlContent);
            // 获取index-content下的所有list-box div
            Elements listBoxElements = document.select(".index-content .list-box");
            return new ArrayList<>(listBoxElements);
        } catch (Exception e) {
            // 记录异常但不中断程序
            log.error(e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 将单个套餐div内容解析为JSON对象
     * @param element div元素
     * @return JSON对象
     */
    public JSONObject parseTariffDivToJson(Element element) {
        JSONObject result = new JSONObject();

        try {
            // Top section
            Element topDiv = element.selectFirst("div[class=top]");
            if (topDiv != null) {
                JSONObject top = new JSONObject();
                Element title = topDiv.selectFirst("p[class=fl title]");
                Element planNumber = topDiv.selectFirst("p[class=fr small]");
                if (title != null) {
                    top.put("title", title.text().trim());
                }
                if (planNumber != null) {
                    top.put("planNumber", planNumber.text().replace("方案编号：", "").trim());
                }
                result.put("top", top);
            }

            // Table section
            Element tableDiv = element.selectFirst("div[class=table]");
            if (tableDiv != null) {
                JSONObject table = new JSONObject();

                // Basic information section
                Element textDiv = tableDiv.selectFirst("div[class=text]");
                if (textDiv != null) {
                    JSONObject basicInfo = new JSONObject();
                    Elements infoDivs = textDiv.select("div[class=textcont]");

                    // Process all info divs and merge them into basicInfo
                    for (Element infoDiv : infoDivs) {
                        Elements items = infoDiv.select("p");
                        for (Element item : items) {
                            String text = item.text().trim();
                            if (text.contains("：")) {
                                String[] parts = text.split("：", 2);
                                basicInfo.put(parts[0].trim(), parts[1].trim());
                            }
                        }
                    }

                    table.put("basicInfo", basicInfo);
                }

                // Service content section
                Element serviceContent = tableDiv.selectFirst("p[class=s-title]:contains(服务内容)");
                if (serviceContent != null) {
                    table.put("serviceTitle", serviceContent.text());
                }

                // Table out section
                Element tableOutDiv = tableDiv.selectFirst("div[class=table-out]");
                if (tableOutDiv != null) {
                    JSONObject tableOut = new JSONObject();
                    Element tableElement = tableOutDiv.selectFirst("table");
                    if (tableElement != null) {
                        // 获取表头作为key
                        Elements headers = tableElement.select("tr:first-child th");
                        // 获取数据行
                        Elements dataRows = tableElement.select("tr:not(:first-child)");

                        if (!dataRows.isEmpty()) {
                            Elements dataCells = dataRows.get(0).select("td");
                            for (int i = 0; i < headers.size() && i < dataCells.size(); i++) {
                                String headerText = headers.get(i).text().trim();
                                String cellText = dataCells.get(i).text().trim();
                                if (StringUtils.isNotEmpty(headerText)) {
                                    // 处理空值和占位符
                                    String value = StringUtils.isNotEmpty(cellText) && !"--".equals(cellText.trim()) ?
                                                 cellText.trim() : "";
                                    tableOut.put(headerText, value);
                                }
                            }
                        }
                    }
                    table.put("tableOut", tableOut);
                }

                // Remark section
                Element remarkDiv = tableDiv.selectFirst("div[class=remark]");
                if (remarkDiv != null) {
                    JSONObject remark = new JSONObject();
                    remark.put("content", remarkDiv.text().trim());
                    table.put("remark", remark);
                }

                // More section
                Element moreDiv = tableDiv.selectFirst("div[class=more]");
                if (moreDiv != null) {
                    JSONObject more = new JSONObject();
                    Element button = moreDiv.selectFirst("button");
                    if (button != null) {
                        String buttonText = button.text().trim();
                        more.put("text", buttonText.replace("查看详情", "").trim());
                    }
                    table.put("more", more);
                }

                result.put("table", table);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.put("error", "Failed to parse element: " + e.getMessage());
        }

        return result;
    }

    public static void main(String[] args) throws FileNotFoundException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\甘肃\\甘肃资费_1002_可选包.json";
        String read = IoUtil.read(new FileInputStream(filePath), StandardCharsets.UTF_8);
        GsTelecomRecordAnalysisHandler handler = new GsTelecomRecordAnalysisHandler();
        List<Element> elements = handler.parseVanListDivs(read);

        JSONArray results = new JSONArray();
        for (Element element : elements) {
            JSONObject json = handler.parseTariffDivToJson(element);
            if (!json.isEmpty()) {
                results.add(json);
            }
        }
        System.out.println(results.toStringPretty());
    }


    /*@Override
    public List<XtyTariffCrawlRecord> analysis(String content) {
        if (StringUtils.isBlank(content))
            return List.of();

        List<Element> elements = parseVanListDivs(content);
        if (elements == null || elements.isEmpty())
            return List.of();
        List<XtyTariffCrawlRecord> result = new ArrayList<>();
        for (Element element : elements) {
            JSONObject json = parseTariffDivToJson(element);
            XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
            JSONObject top = json.getJSONObject("top");
            if (top == null)
                continue;
            record.setName(StringUtils.trimToEmpty(top.getStr("title")));
            record.setTariffNo(StringUtils.trimToEmpty(top.getStr("planNumber")));
            JSONObject table = json.getJSONObject("table");
            if (table != null) {
                JSONObject basicInfo = table.getJSONObject("basicInfo");
                if (basicInfo != null) {
                    String fees = StringUtils.trimToEmpty(basicInfo.getStr("资费标准"));
                    if (StringUtils.isNotEmpty(fees)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(fees);
                        if (parse != null) {
                            record.setFees(parse.getAmount());
                            record.setFeesUnit(parse.getUnit());
                        }
                    }

                    record.setApplicablePeople(StringUtils.trimToEmpty(basicInfo.getStr("适用范围")));
                    record.setChannel(StringUtils.trimToEmpty(basicInfo.getStr("销售渠道")));

                    String upDownDay = StringUtils.trimToEmpty(basicInfo.getStr("上下线时间"));
                    if (StringUtils.isNotEmpty(upDownDay)) {
                        String[] split = upDownDay.split("至");
                        if (split.length == 2) {
                            record.setOnlineDay(ParseDateUtil.formatDateStr(split[0]));
                            record.setOfflineDay(ParseDateUtil.formatDateStr(split[1]));
                        }
                    }

                    record.setValidPeriod(StringUtils.trimToEmpty(basicInfo.getStr("有效期限")));
                    record.setUnsubscribe(StringUtils.trimToEmpty(basicInfo.getStr("退订方式")));
                    record.setDuration(StringUtils.trimToEmpty(basicInfo.getStr("在网要求")));
                    record.setResponsibility(StringUtils.trimToEmpty(basicInfo.getStr("违约责任")));
                    record.setTariffType(StringUtils.trimToEmpty(basicInfo.getStr("资费类型")));
                }

                JSONObject tableOut = table.getJSONObject("tableOut");
                if (tableOut != null) {
                    String callInfo = StringUtils.trimToEmpty(tableOut.getStr("语音"));
                    if (StringUtils.isNotBlank(callInfo)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(callInfo);
                        if (parse != null) {
                            record.setCall(parse.getAmount());
                        }
                    }

                    String dataInfo = StringUtils.trimToEmpty(tableOut.getStr("通用流量"));
                    if (StringUtils.isNotBlank(dataInfo)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(dataInfo);
                        if (parse != null) {
                            record.setData(parse.getAmount());
                            record.setDataUnit(parse.getUnit());
                        }
                    }

                    record.setBandwidth(StringUtils.trimToEmpty(tableOut.getStr("宽带")));
                    record.setIptv(StringUtils.trimToEmpty(tableOut.getStr("IPTV")));

                    String smsInfo = StringUtils.trimToEmpty(tableOut.getStr("短彩信"));
                    if (StringUtils.isNotBlank(smsInfo)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(smsInfo);
                        if (parse != null) {
                            record.setSms(parse.getAmount());
                        }
                    }

                    // 定向流量处理
                    String dxll = StringUtils.trimToEmpty(tableOut.getStr("定向流量"));
                    if (StringUtils.isNotBlank(dxll)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(dxll);
                        if (parse != null) {
                            record.setOrientTraffic(parse.getAmount());
                            record.setOrientTrafficUnit(parse.getUnit());
                        }
                    }

                    record.setRights(StringUtils.trimToEmpty(tableOut.getStr("权益")));
                }

                JSONObject remark = table.getJSONObject("remark");
                if (remark != null) {
                    record.setOtherContent(StringUtils.trimToEmpty(remark.getStr("content")));
                }

            }
            result.add(record);
        }

        return result;
    }*/

}
