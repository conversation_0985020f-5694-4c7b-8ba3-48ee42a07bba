package com.yunqu.crawler.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunqu.crawler.domain.XtyCrawlerVersionConfig;
import com.yunqu.crawler.domain.bo.XtyCrawlerVersionConfigBo;
import com.yunqu.crawler.domain.vo.XtyCrawlerVersionConfigVo;
import com.yunqu.emergency.common.mybatis.core.page.PageQuery;
import com.yunqu.emergency.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 爬虫版本生成配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IXtyCrawlerVersionConfigService extends IService<XtyCrawlerVersionConfig> {

    /**
     * 查询爬虫版本生成配置
     *
     * @param id 主键
     * @return 爬虫版本生成配置
     */
    XtyCrawlerVersionConfigVo queryById(String id);

    /**
     * 分页查询爬虫版本生成配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 爬虫版本生成配置分页列表
     */
    TableDataInfo<XtyCrawlerVersionConfigVo> queryPageList(XtyCrawlerVersionConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的爬虫版本生成配置列表
     *
     * @param bo 查询条件
     * @return 爬虫版本生成配置列表
     */
    List<XtyCrawlerVersionConfigVo> queryList(XtyCrawlerVersionConfigBo bo);

    /**
     * 新增爬虫版本生成配置
     *
     * @param bo 爬虫版本生成配置
     * @return 是否新增成功
     */
    Boolean insertByBo(XtyCrawlerVersionConfigBo bo);

    /**
     * 修改爬虫版本生成配置
     *
     * @param bo 爬虫版本生成配置
     * @return 是否修改成功
     */
    Boolean updateByBo(XtyCrawlerVersionConfigBo bo);

    /**
     * 校验并批量删除爬虫版本生成配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
