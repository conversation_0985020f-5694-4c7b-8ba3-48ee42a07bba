package com.yunqu.crawler.service.impl.telecom;

import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.base.StringAmountUnitParser;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.util.ParseDateUtil;
import com.yunqu.emergency.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 上海电信资费数据处理器
 * </p>
 *
 * @ClassName ShTelecomRecordAnalysisHandler
 * <AUTHOR> Copy This Tag)
 * @Description 上海电信资费数据解析处理
 * @Since create in 2025/5/28 14:08
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@Slf4j
public class ShTelecomRecordAnalysisHandler implements IRecordAnalysisHandler {
    @Override
    public List<XtyTariffCrawlRecord> analysis(String content) {
        if (StringUtils.isBlank(content))
            return List.of();

        List<Element> elements = parseIndexPackageDivs(content);
        if (elements == null || elements.isEmpty())
            return List.of();
        List<XtyTariffCrawlRecord> result = new ArrayList<>();
        for (Element element : elements) {
            JSONObject json = parsePackageToJson(element);
            XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
            record.setExtendedFields(JSONUtil.toJsonStr(json));
            JSONObject top = json.getJSONObject("header");
            if (top == null)
                continue;
            record.setName(StringUtils.trimToEmpty(top.getStr("title")));
            record.setTariffNo(StringUtils.trimToEmpty(top.getStr("planNumber")));
            JSONObject table = json.getJSONObject("content");
            if (table != null) {
                JSONObject basicInfo = table.getJSONObject("basicInfo");
                if (basicInfo != null) {
                    String fees = StringUtils.trimToEmpty(basicInfo.getStr("资费标准"));
                    if (StringUtils.isNotEmpty(fees)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(fees);
                        if (parse != null) {
                            record.setFees(parse.getAmount());
                            record.setFeesUnit(parse.getUnit());
                        }
                    }

                    record.setApplicablePeople(StringUtils.trimToEmpty(basicInfo.getStr("适用范围")));
                    record.setChannel(StringUtils.trimToEmpty(basicInfo.getStr("销售渠道")));

                    String upDownDay = StringUtils.trimToEmpty(basicInfo.getStr("上下线时间"));
                    if (StringUtils.isNotEmpty(upDownDay)) {
                        String[] split = upDownDay.split("至");
                        if (split.length == 2) {
                            record.setOnlineDay(ParseDateUtil.formatDateStr(split[0]));
                            record.setOfflineDay(ParseDateUtil.formatDateStr(split[1]));
                        }
                    }

                    record.setValidPeriod(StringUtils.trimToEmpty(basicInfo.getStr("有效期限")));
                    record.setUnsubscribe(StringUtils.trimToEmpty(basicInfo.getStr("退订方式")));
                    record.setDuration(StringUtils.trimToEmpty(basicInfo.getStr("在网要求")));
                    record.setResponsibility(StringUtils.trimToEmpty(basicInfo.getStr("违约责任")));
                }

                JSONObject tableOut = table.getJSONObject("tableOut");
                if (tableOut != null) {
                    String callInfo = StringUtils.trimToEmpty(tableOut.getStr("语音"));
                    if (StringUtils.isNotBlank(callInfo)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(callInfo);
                        if (parse != null) {
                            record.setCall(parse.getAmount());
                        }
                    }

                    String dataInfo = StringUtils.trimToEmpty(tableOut.getStr("通用流量"));
                    if (StringUtils.isNotBlank(dataInfo)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(dataInfo);
                        if (parse != null) {
                            record.setData(parse.getAmount());
                            record.setDataUnit(parse.getUnit());
                        }
                    }

                    record.setBandwidth(StringUtils.trimToEmpty(tableOut.getStr("宽带")));
                    record.setIptv(StringUtils.trimToEmpty(tableOut.getStr("IPTV")));

                    String smsInfo = StringUtils.trimToEmpty(tableOut.getStr("短彩信"));
                    if (StringUtils.isNotBlank(smsInfo)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(smsInfo);
                        if (parse != null) {
                            record.setSms(parse.getAmount());
                        }
                    }

                    // 定向流量处理
                    String dxll = StringUtils.trimToEmpty(tableOut.getStr("定向流量"));
                    if (StringUtils.isNotBlank(dxll)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(dxll);
                        if (parse != null) {
                            record.setOrientTraffic(parse.getAmount());
                            record.setOrientTrafficUnit(parse.getUnit());
                        }
                    }

                    record.setRights(StringUtils.trimToEmpty(tableOut.getStr("权益")));
                }

                JSONObject remark = table.getJSONObject("remark");
                if (remark != null) {
                    record.setOtherContent(StringUtils.trimToEmpty(remark.getStr("content")));
                }

                result.add(record);
            }
        }

        return result;
    }

    /**
     * Parse HTML content and extract div elements under div.index_package
     *
     * @param htmlContent the HTML content to parse
     * @return List of div elements found within the index_package div
     */
    public List<Element> parseIndexPackageDivs(String htmlContent) {
        if (StringUtils.isEmpty(htmlContent)) {
            return new ArrayList<>();
        }

        try {
            // Clean script tags using regex first
            String cleanedHtml = htmlContent;
            // Remove single line script tags
            cleanedHtml = cleanedHtml.replaceAll("<script.*?</script>", "");
            // Remove multi-line script tags
            cleanedHtml = cleanedHtml.replaceAll("(?s)<script.*?</script>", "");
            // Remove self-closing script tags
            cleanedHtml = cleanedHtml.replaceAll("<script.*?/>", "");
            // Remove any remaining script tags
            cleanedHtml = cleanedHtml.replaceAll("<script.*?>", "");

            // Parse the cleaned HTML
            Document doc = Jsoup.parse(cleanedHtml);

            // Find div.index_package
            Element indexPackageDiv = doc.selectFirst("div.index_package");
            if (indexPackageDiv != null) {
                // 获取直接子div元素，避免获取到嵌套的div
                return indexPackageDiv.children().stream()
                        .filter(element -> element.is("div"))
                        .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return new ArrayList<>();
    }

    /**
     * Parse a single package div element to JSON object
     *
     * @param element the div element to parse
     * @return JSONObject containing the parsed information
     */
    public JSONObject parsePackageToJson(Element element) {
        JSONObject json = new JSONObject();

        try {
            // Parse header section
            Element headerDiv = element.selectFirst("div.package_header");
            if (headerDiv != null) {
                JSONObject header = new JSONObject();
                Element title = headerDiv.selectFirst("div.package_title");
                Element record = headerDiv.selectFirst("div.package_record");
                if (title != null) {
                    header.put("title", title.text());
                }
                if (record != null) {
                    header.put("planNumber", record.text());
                }
                json.put("header", header);
            }

            // Parse content section
            Element contentDiv = element.selectFirst("div.package_content");
            if (contentDiv != null) {
                JSONObject content = new JSONObject();

                // Parse basic information
                Element infoDiv = contentDiv.selectFirst("div.package_information");
                if (infoDiv != null) {
                    JSONObject basicInfo = new JSONObject();
                    Elements infoLists = infoDiv.select("div.information_list");
                    for (Element info : infoLists) {
                        Element nameElement = info.selectFirst("div.information_name");
                        Element fontElement = info.selectFirst("div.information_font");
                        if (nameElement != null && fontElement != null) {
                            String key = nameElement.text().replace("：", "").trim();
                            String value = fontElement.text().trim();
                            basicInfo.put(key, value);
                        }
                    }
                    content.put("basicInfo", basicInfo);
                }

                // Parse service content
                Element messageDiv = contentDiv.selectFirst("div.package_message");
                if (messageDiv != null) {
                    // Parse table content
                    Element tableDiv = messageDiv.selectFirst("div.table-out");
                    if (tableDiv != null) {
                        JSONObject tableOut = new JSONObject();
                        Elements headers = tableDiv.select("table tr th");
                        Elements values = tableDiv.select("table tr:last-child td");

                        for (int i = 0; i < headers.size() && i < values.size(); i++) {
                            String header = headers.get(i).text().trim();
                            String value = values.get(i).text().trim();
                            if (StringUtils.isNotEmpty(header) && !value.equals("—")) {
                                tableOut.put(header, value);
                            }
                        }
                        content.put("tableOut", tableOut);
                    }

                    // Parse remark
                    Element remarkDiv = messageDiv.selectFirst("div.remark");
                    if (remarkDiv != null) {
                        JSONObject remark = new JSONObject();
                        remark.put("content", remarkDiv.text().trim());
                        content.put("remark", remark);
                    }

                    // Parse more link
                    Element moreDiv = messageDiv.selectFirst("div.footer_more");
                    if (moreDiv != null) {
                        JSONObject more = new JSONObject();
                        Element link = moreDiv.selectFirst("a.more_btn");
                        if (link != null) {
                            more.put("link", link.attr("href"));
                            Element text = link.selectFirst("p");
                            if (text != null) {
                                more.put("text", text.text());
                            }
                        }
                        content.put("more", more);
                    }
                }

                json.put("content", content);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            json.put("error", "Failed to parse element: " + e.getMessage());
        }

        return json;
    }

    /**
     * Parse Zhejiang Telecom tariff HTML content to JSON
     *
     * @param htmlContent the HTML content string to parse
     * @return JSONObject containing the parsed information
     */
    public JSONObject parseZjTelecomToJson(String htmlContent) {
        JSONObject json = new JSONObject();

        try {
            if (StringUtils.isEmpty(htmlContent)) {
                json.put("error", "HTML content is empty");
                return json;
            }

            // Clean script tags using regex first
            String cleanedHtml = htmlContent;
            // Remove single line script tags
            cleanedHtml = cleanedHtml.replaceAll("<script.*?</script>", "");
            // Remove multi-line script tags
            cleanedHtml = cleanedHtml.replaceAll("(?s)<script.*?</script>", "");
            // Remove self-closing script tags
            cleanedHtml = cleanedHtml.replaceAll("<script.*?/>", "");
            // Remove any remaining script tags
            cleanedHtml = cleanedHtml.replaceAll("<script.*?>", "");

            // Parse the cleaned HTML
            Document doc = Jsoup.parse(cleanedHtml);
            Element element = doc.selectFirst("div.newZifei");

            if (element == null) {
                json.put("error", "No div with class 'newZifei' found");
                return json;
            }

            // Parse title section
            Element titleDiv = element.selectFirst("div.content-title");
            if (titleDiv != null) {
                JSONObject header = new JSONObject();
                Element title = titleDiv.selectFirst("span[tagname=pcTitle]");
                Element planNumber = titleDiv.selectFirst("span[tagname=contentCode]");
                if (title != null) {
                    header.put("title", title.text());
                }
                if (planNumber != null) {
                    header.put("planNumber", planNumber.text());
                }
                json.put("header", header);
            }

            // Parse content section
            Element modelDiv = element.selectFirst("div.model");
            if (modelDiv != null) {
                JSONObject content = new JSONObject();

                // Parse basic information
                Element jbxxContent = modelDiv.selectFirst("div.jbxx-content");
                if (jbxxContent != null) {
                    JSONObject basicInfo = new JSONObject();
                    Elements items = jbxxContent.select("div.item");
                    for (Element item : items) {
                        Element titleElement = item.selectFirst("div.item-title");
                        Element valueElement = item.selectFirst("div[tagname]");

                        if (titleElement != null && valueElement != null) {
                            String key = titleElement.text().replace("：", "").trim();
                            String value = valueElement.text().trim();
                            basicInfo.put(key, value);
                        }

                        // Special handling for fees with unit
                        if (item.hasAttr("tagparent") && item.attr("tagparent").equals("fees")) {
                            Element feesSpan = item.selectFirst("span[tagname=fees]");
                            Element feesUnitSpan = item.selectFirst("span[tagname=fees_unit]");
                            if (feesSpan != null && feesUnitSpan != null) {
                                basicInfo.put("资费标准", feesSpan.text() + feesUnitSpan.text());
                            }
                        }
                    }
                    content.put("basicInfo", basicInfo);
                }

                // Parse service content table
                Element table = modelDiv.selectFirst("table");
                if (table != null) {
                    JSONObject tableOut = new JSONObject();
                    Elements headers = table.select("thead td");
                    Elements values = table.select("tbody td");

                    for (int i = 0; i < headers.size() && i < values.size(); i++) {
                        String header = headers.get(i).text().trim();
                        Element valueCell = values.get(i);
                        Element valueSpan = valueCell.selectFirst("span[tagname]");
                        String value = valueSpan != null ? valueSpan.text().trim() : "";
                        if (StringUtils.isNotEmpty(header) && !value.equals("—")) {
                            tableOut.put(header, value);
                        }
                    }
                    content.put("tableOut", tableOut);
                }

                // Parse other content
                Element otherContent = modelDiv.selectFirst("div[tagname=other_content]");
                if (otherContent != null) {
                    JSONObject remark = new JSONObject();
                    remark.put("content", otherContent.text().trim());
                    content.put("remark", remark);
                }

                // Parse detail button
                Element detailBtn = modelDiv.selectFirst("div.lookDetailBtn");
                if (detailBtn != null) {
                    JSONObject more = new JSONObject();
                    more.put("text", detailBtn.text().trim());
                    content.put("more", more);
                }

                json.put("content", content);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            json.put("error", "Failed to parse HTML: " + e.getMessage());
        }

        return json;
    }

    public static void main(String[] args) throws FileNotFoundException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\中国电信\\上海资费_20250605_211214_9519_套餐.json";
        String read = IoUtil.read(new FileInputStream(filePath), StandardCharsets.UTF_8);
        ShTelecomRecordAnalysisHandler handler = new ShTelecomRecordAnalysisHandler();
        System.out.println(JSONUtil.toJsonStr(handler.analysis(read)));
    }
}
