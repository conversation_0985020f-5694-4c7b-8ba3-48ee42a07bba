package com.yunqu.crawler.base;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.yunqu.emergency.common.core.utils.StringUtils;

/**
 * 字符串数量单位解析工具类
 */
public class StringAmountUnitParser {

    /**
     * 解析字符串中的数量和单位
     * 
     * @param input 输入字符串，格式支持：
     *              - "100元/10天"
     *              - "100元"
     *              - "100"
     *              - "19(元/月)"
     *              - "19元/月"
     * @return 解析结果对象
     */
    public static AmountUnitResult parse(String input) {
        if (StringUtils.isBlank(input)) {
            return null;
        }

        input = input.trim();

        // 移除括号
        if (input.contains("(") && input.contains(")")) {
            input = input.replaceAll("[()]", "");
        }

        // 使用正则表达式匹配数字部分和可选的单位部分
        Pattern pattern = Pattern.compile("^(\\d+(?:\\.\\d+)?)\\s*(.*)$");
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            String amount = matcher.group(1);
            String unit = matcher.group(2);
            // 如果unit为null，设置为空字符串
            unit = unit == null ? "" : unit.trim();
            return new AmountUnitResult(amount, unit);
        }

        return null;
    }

    /**
     * 解析结果类
     */
    public static class AmountUnitResult {
        private final String amount;
        private final String unit;

        public AmountUnitResult(String amount, String unit) {
            this.amount = amount;
            this.unit = unit;
        }

        public String getAmount() {
            return amount;
        }

        public String getUnit() {
            return unit;
        }

        @Override
        public String toString() {
            return "AmountUnitResult{" +
                    "amount='" + amount + '\'' +
                    ", unit='" + unit + '\'' +
                    '}';
        }
    }
}
