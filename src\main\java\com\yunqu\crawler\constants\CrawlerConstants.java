package com.yunqu.crawler.constants;

/**
 * 爬虫系统常量定义
 *
 * <AUTHOR>
 * @version 1.0
 */
public class CrawlerConstants {

    /**
     * 日志前缀
     */
    public static final String LOG_PREFIX = "[TASK]";

    /**
     * 分布式锁名称
     */
    public static final String LOCK_CRAWLER_TARIFF = "crawlerTariff";
    public static final String LOCK_EXECUTE_CRAWLER_TASK = "sys:executeCrawlerTask";
    public static final String LOCK_EXECUTE_CRAWLER_UNICOM_TASK = "sys:executeCrawlerUnicomTask";
    public static final String LOCK_CLEAN_REPORT_TARIFF_FLAG = "cleanReportTariffFlag";

    /**
     * 日期格式
     */
    public static final String DATE_FORMAT = "yyyyMMdd";

    /**
     * Redis键前缀
     */
    public static final String REDIS_VERSION_KEY_PREFIX = "sys:crawler:version:";
    public static final String REDIS_VERSION_ORDER_KEY = "sys:crawler:version:order";

    /**
     * 版本状态
     */
    public static final String VERSION_STATUS_ACTIVE = "ACTIVE";

    /**
     * 爬取状态
     */
    public static final String CRAWL_STATUS_PENDING = "PENDING";
    public static final String CRAWL_STATUS_PROCESSING = "PROCESSING";
    public static final String CRAWL_STATUS_COMPLETED = "COMPLETED";

    /**
     * 配置状态
     */
    public static final String CONFIG_ENABLED = "Y";

    /**
     * 版本号前缀
     */
    public static final String VERSION_PREFIX = "V";

    /**
     * 版本号分隔符
     */
    public static final String VERSION_SEPARATOR = "_";

    /**
     * 私有构造函数，防止实例化
     */
    private CrawlerConstants() {
        throw new IllegalStateException("常量类不能被实例化");
    }

    /**
     * 生效类型-长期有效
     */
    public static Integer EFFECTIVE_TYPE_1 = 1;

    /**
     * 生效类型-固定期限
     */
    public static Integer EFFECTIVE_TYPE_2 = 2;
}
