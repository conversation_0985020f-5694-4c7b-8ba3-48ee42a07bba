import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yunqu.crawler.XtyCrawlerServiceApplication;
import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.crawler.manager.CrawlerVersionManager;
import com.yunqu.crawler.mapper.XtyCrawlerTaskMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * @ClassName XtyCrawlerTaskTest
 * <AUTHOR> Copy This Tag)
 * @Description TODO 描述文件用途
 * @Since create in 2025/7/31 17:45
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@SpringBootTest(classes = XtyCrawlerServiceApplication.class)
@ActiveProfiles("dev")
public class XtyCrawlerTaskTest {


    @Autowired
    private XtyCrawlerTaskMapper xtyCrawlerTaskMapper;

    @Autowired
    private CrawlerVersionManager crawlerVersionManager;


    @Test
    public void test() {
        XtyCrawlerTask xtyCrawlerTask = xtyCrawlerTaskMapper.selectOne(Wrappers.lambdaQuery(XtyCrawlerTask.class).last("limit 1"));
        System.out.println(xtyCrawlerTask);
        xtyCrawlerTask.setCrawlerBeginTime(new Date());
        xtyCrawlerTask.setCrawlerEndTime(new Date());
        xtyCrawlerTaskMapper.updateById(xtyCrawlerTask);
    }

    @Test
    public void test1() {
        boolean b = crawlerVersionManager.existsCurrentDayVersionConfig();
        System.out.println(b);
    }


}
