package com.yunqu.crawler.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.base.CacheConstants;
import com.yunqu.crawler.base.Constants;
import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.domain.vo.XtyTariffProvinceVo;
import com.yunqu.crawler.enums.OperatorEnum;
import com.yunqu.crawler.enums.RunErrorEnum;
import com.yunqu.crawler.mapper.XtyCrawlerTaskMapper;
import com.yunqu.crawler.mapper.XtyTariffCrawlRecordMapper;
import com.yunqu.crawler.service.ICrawlerCallbackService;
import com.yunqu.crawler.service.ICrawlerTariffService;
import com.yunqu.crawler.service.IXtyTariffProvinceService;
import com.yunqu.emergency.common.core.exception.ServiceException;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.redis.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 联通资费抓取服务实现
 * 负责联通资费数据的抓取、回调文件解析、数据入库等业务。
 * 主要流程：
 * 1. 通过 crawler 方法发起抓取任务。
 * 2. 回调 callback 方法处理抓取结果文件，解析并入库。
 * 3. 解析过程中，统一处理省份、通用字段、异常日志。
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
//@Service(Constants.OPERATOR_UNICOM + ICrawlerTariffService.BASE_NAME)
public class UnicomCrawlerTariffServiceImpl implements ICrawlerTariffService, ICrawlerCallbackService {

    protected static final String LOG_PREFIX = "[UNICOM]"; // 联通日志前缀

    // === 依赖注入 ===
    protected final XtyCrawlerTaskMapper crawlerTaskMapper;
    protected final IXtyTariffProvinceService tariffProvinceService;
    protected final XtyTariffCrawlRecordMapper crawlerRecordMapper;

    // === 配置参数 ===
    @Value("${crawler.local-server}")
    protected String localUrl;
    @Value("${crawler.base-url}")
    protected String crawlerBaseUrl;
    @Value("${crawler.unicom-provinces}")
    protected String provinces;
    @Value("${crawler.unicom-provinces-name}")
    protected String provincesName;
    @Value("${file.tmp-path}")
    protected String localFilePath;

    // === JSON字段常量 ===
    protected static final String FIELD_TARIFF_DETAIL_LIST = "tariffDetailInfoList";
    protected static final String FIELD_THREE_DETAIL_DATE = "threeDetailDate";
    protected static final String FIELD_RECORD_NUM = "recordNum";
    protected static final String FIELD_NAME_THIRD = "nameThird";
    protected static final String FIELD_MAIN_FEE = "mainFee";
    protected static final String FIELD_DOWN_LINE_DATE = "downLineDate";
    protected static final String FIELD_ONLINE_DATE = "onlineDate";
    protected static final String FIELD_EFFECTIVE_PERIOD = "effectivePeriod";
    protected static final String FIELD_SUIT_AREA = "suitArea";
    protected static final String FIELD_SALE_CHANNEL = "saleChannel";
    protected static final String FIELD_IN_NET_PERIOD = "inNetPeriod";
    protected static final String FIELD_UNSUBSCRIBE_METHOD = "unsubscribeMethod";
    protected static final String FIELD_NON_CONTRACT = "nonContract";
    protected static final String FIELD_SERVICE_CONTENT = "serviceContent";

    /**
     * 发起联通资费数据抓取任务
     *
     * @param dateId 日期ID
     */
    @Override
    public void crawler(int dateId, String versionNo) {
        log.info("{} [TASK-START] 开始资费数据抓取任务, dateId: {}", LOG_PREFIX, dateId);

        if (StringUtils.isBlank(provinces)) {
            log.error("{} [TASK-FAIL] 资费爬虫省份配置为空", LOG_PREFIX);
            return;
        }

        String[] provinceArray = StringUtils.split(provinces, ",");
        String[] provinceNameArray = StringUtils.split(provincesName, ",");
        log.info("{} [TASK-INFO] 需要抓取的省份数量: {}", LOG_PREFIX, provinceArray.length);

        for (int i = 0; i < provinceArray.length; i++) {
            String province = provinceArray[i];
            String provinceName = provinceNameArray[i];
            log.info("{} [TASK-PROCESS] 开始创建资费爬虫任务, 省份: {}", LOG_PREFIX, province);

            try {
                XtyCrawlerTask crawlerTask = new XtyCrawlerTask();
                crawlerTask.setDateId(dateId);
                crawlerTask.setProvinceCode(province);
                crawlerTask.setProvinceName(provinceName);
                crawlerTask.setStatus(0);
                crawlerTask.setEntType(3);
                crawlerTask.setOperatorCode(OperatorEnum.UNICOM.getAlias());
                crawlerTask.setOperatorName(OperatorEnum.UNICOM.getName());
                crawlerTask.setVersionNo(versionNo);
                crawlerTaskMapper.insert(crawlerTask);
                log.info("{} [TASK-SUCCESS] 成功创建资费爬虫任务, taskId: {}, 省份: {}", LOG_PREFIX, crawlerTask.getId(), province);
            } catch (Exception e) {
                log.error("{} [TASK-FAIL] 创建资费爬虫任务失败, 省份: {}, 错误: {}", LOG_PREFIX, province, e.getMessage(), e);
            }
        }

        log.info("{} [TASK-END] 完成资费数据抓取任务创建, dateId: {}", LOG_PREFIX, dateId);
    }

    /**
     * 执行请求爬虫
     *
     * @param task 爬虫任务
     */
    @Async
    @Override
    public void excueteRequestCrawler(XtyCrawlerTask task) {
        log.info("{} [CRAWLER-START] 开始执行资费爬虫请求, taskId: {}, 省份: {}", LOG_PREFIX, task.getId(), task.getProvinceCode());

        String operatorCode = task.getOperatorCode();
        String callback = localUrl + "/callback/" + operatorCode + "/" + task.getId();

        Map<String, String> map = new HashMap<>();
        map.put("callback_url", callback);
        map.put("provinces", task.getProvinceCode());
        map.put("crawler_type", OperatorEnum.UNICOM.getAlias());

        try {
            log.info("{} [CRAWLER-PROCESS] 发送爬虫请求, taskId: {}, url: {}, 参数: {}", LOG_PREFIX, task.getId(),
                    crawlerBaseUrl + "/api/v1/crawl", map);
            requestToCrawler(crawlerBaseUrl + "/api/v1/crawl", JSONUtil.toJsonStr(map));
            log.info("{} [CRAWLER-SUCCESS] 成功发送爬虫请求, taskId: {}", LOG_PREFIX, task.getId());
        } catch (Exception e) {
            log.error("{} [CRAWLER-FAIL] 发送爬虫请求失败, taskId: {}, 错误: {}", LOG_PREFIX, task.getId(), e.getMessage(), e);
        }
    }

    /**
     * 联通资费回调文件处理主入口
     * 1. 解压回调文件
     * 2. 解析每个json文件，分发到明细/三网数据处理
     * 3. 统一异常和目录清理
     *
     * @param taskId 任务id
     * @param file   回调文件
     */
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class, ServiceException.class})
    @Override
    public void callback(Long taskId, MultipartFile file, String crawlerUrl) {
        log.info("{} [CALLBACK-START] 收到资费爬虫回调, taskId: {}, 文件名: {}", LOG_PREFIX, taskId, file.getOriginalFilename());
        long startTime = System.currentTimeMillis();

        XtyCrawlerTask xtyCrawlerTask = crawlerTaskMapper.selectById(taskId);
        String tempDirPath = localFilePath + File.separator + taskId;
        try {
            JSONObject provinceInfo = RedisUtils.getCacheMapValue(CacheConstants.UNICOM_PROVINCE_INFO_DICT_KEY, xtyCrawlerTask.getProvinceCode());
            String provinceCode = provinceInfo.getStr("provinceCode");
            String provinceName = provinceInfo.getStr("provinceName");
            String taskProvinceName = provinceInfo.getStr("name");
            String crawlerCallbackFileName = file.getOriginalFilename();
            String zipFileFolderPath = localFilePath + File.separator + xtyCrawlerTask.getVersionNo() + File.separator + OperatorEnum.UNICOM.getAlias();
            File zipFileFolder = new File(zipFileFolderPath);
            if (!zipFileFolder.exists()) zipFileFolder.mkdirs();
            String zipFilePath = zipFileFolderPath + File.separator + crawlerCallbackFileName;
            File zipFile = FileUtil.file(zipFilePath);
            file.transferTo(zipFile);
            File folder = new File(tempDirPath);
            ICrawlerCallbackService.unzipFile(zipFile, folder, System.currentTimeMillis());

            String versionNo = xtyCrawlerTask.getVersionNo();
            JSONObject CLASSIC_DICT = JSONUtil.parseObj(Constants.UNICOM_TARIFF_CLASSIC);

            try {
                if (!folder.isDirectory()) {
                    log.error("{} [FILE-ERROR] 解压目录不是文件夹: {}", LOG_PREFIX, tempDirPath);
                    return;
                }
                File[] files = folder.listFiles();
                if (files == null) {
                    log.warn("{} [FILE-WARN] 解压目录下无文件: {}", LOG_PREFIX, tempDirPath);
                    return;
                }
                /*if (files.length < 2) {
                    xtyCrawlerTask.setStatus(0);
                    crawlerTaskMapper.updateById(xtyCrawlerTask);
                    log.error("{} [FILE-ERROR] 解压目录下文件数量小于2: {}", LOG_PREFIX, tempDirPath);
                    return;
                }*/
                log.info("{} [FILE-START] 开始处理解压后的文件, 文件数量: {}", LOG_PREFIX, files.length);
                Map<String, Integer> map = processJsonFile(files, CLASSIC_DICT, taskId, xtyCrawlerTask, crawlerCallbackFileName, crawlerUrl, versionNo, provinceCode, provinceName, taskProvinceName);

                xtyCrawlerTask.setStatus(2);
                xtyCrawlerTask.setLocalProvinceCount(map.get("localProvinceCount"));
                xtyCrawlerTask.setGroupCount(map.get("groupCount"));
                crawlerTaskMapper.updateById(xtyCrawlerTask);
                log.info("{} [CALLBACK-SUCCESS] 完成资费爬虫回调处理, taskId: {}, 耗时: {}ms", LOG_PREFIX, taskId,
                        System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.error("{} [CALLBACK-FAIL] 回调处理异常, taskId: {}, 错误: {}", LOG_PREFIX, taskId, e.getMessage(), e);
                throw e;
            } finally {
                ICrawlerCallbackService.deleteDirectory(folder);
                log.info("{} [CALLBACK-CLEANUP] 清理临时文件完成, 路径: {}", LOG_PREFIX, tempDirPath);
            }
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        } finally {
            ICrawlerCallbackService.deleteDirectory(tempDirPath);
        }

    }

    /**
     * 处理单个json文件，解耦主流程
     */
    protected Map<String, Integer> processJsonFile(File[] jsonFiles, JSONObject CLASSIC_DICT, Long taskId, XtyCrawlerTask xtyCrawlerTask,
                                                 String crawlerCallbackFileName, String crawlerUrl, String versionNo, String provinceCode, String provinceName, String taskProvinceName) {

        int localProvinceCount = 0;
        int groupCount = 0;
        List<String> contantsKeys = new ArrayList<>();
        for (File jsonFile : jsonFiles) {
            List<XtyTariffCrawlRecord> records = new ArrayList<>();
            String crawlerDataBelongFileName = jsonFile.getName();
            log.info("{} [FILE-START] 开始处理JSON文件: {}", LOG_PREFIX, crawlerDataBelongFileName);

            int lastDotIdx = StringUtils.lastIndexOf(crawlerDataBelongFileName, ".");
            if (lastDotIdx <= 0) {
                log.warn("{} [FILE-WARN] 文件名格式异常: {}", LOG_PREFIX, crawlerDataBelongFileName);
                throw new RuntimeException(crawlerDataBelongFileName + " 文件格式异常");
            }
            String fileName = crawlerDataBelongFileName.substring(0, lastDotIdx);
            String[] fileNames = fileName.split("_");
            if (fileNames.length < 2) {
                log.warn("{} [FILE-WARN] 文件名分割后长度异常: {}", LOG_PREFIX, fileName);
                throw new RuntimeException(crawlerDataBelongFileName + " 文件名分割后长度异常");
            }
            String crawlerType = fileNames[fileNames.length - 1];

            if (StringUtils.equals("全国资费", crawlerType)) {
                crawlerType = "全网资费";
            } else {
                crawlerType = xtyCrawlerTask.getProvinceName() + "资费";
            }


            String content = readFileContent(jsonFile);
            if (StringUtils.isBlank(content)) {
                log.warn("{} [FILE-WARN] 文件内容为空: {}", LOG_PREFIX, jsonFile.getAbsolutePath());
                throw new RuntimeException(crawlerDataBelongFileName + " 文件内容为空");
            }
            JSONObject entries;
            try {
                entries = JSONUtil.parseObj(content);
            } catch (Exception e) {
                log.error("{} [FILE-FAIL] 解析json文件内容失败: {}, 错误: {}", LOG_PREFIX, jsonFile.getAbsolutePath(), e.getMessage(),
                        e);
                throw e;
            }
            log.debug("{} [DATA-START] 开始处理资费数据, 文件: {}, 省份: {}", LOG_PREFIX, crawlerDataBelongFileName,
                    taskProvinceName);
            for (String key : entries.keySet()) {
                try {
                    List<XtyTariffCrawlRecord> recordList = processClassicType(key, entries, CLASSIC_DICT, taskId, xtyCrawlerTask.getDateId(),
                            taskProvinceName, crawlerType, crawlerCallbackFileName,
                            crawlerDataBelongFileName, crawlerUrl, versionNo, provinceCode, provinceName);
                    for (XtyTariffCrawlRecord record : recordList) {
                        record.setProvinceCode(provinceCode);
                        record.setProvinceName(provinceName);
                        record.setCrawlerProvinceName(taskProvinceName);
                        String tariffName = StringUtils.trimToEmpty(record.getName());
                        String tariffNo = StringUtils.trimToEmpty(record.getTariffNo());
                        String contantsKey = tariffName + tariffNo;
                        if (!contantsKeys.contains(contantsKey)) {
                            contantsKeys.add(contantsKey);
                            records.add(record);
                        }
                    }
                    //records.addAll(recordList);
                } catch (Exception e) {
                    log.error("{} [DATA-FAIL] 处理资费类型数据失败, key: {}, 错误: {}", LOG_PREFIX, key, e.getMessage(), e);
                    throw e;
                }
            }
            if (StringUtils.equals("全网资费", crawlerType)) {
                groupCount += records.size();
            } else {
                localProvinceCount += records.size();
            }
            log.info("{} [FILE-SUCCESS] 完成JSON文件处理: {}", LOG_PREFIX, crawlerDataBelongFileName);
            crawlerRecordMapper.insertBatch(records);
        }

        return Map.of("localProvinceCount", localProvinceCount, "groupCount", groupCount);
    }

    /**
     * 处理经典类型数据
     */
    protected List<XtyTariffCrawlRecord> processClassicType(String key, JSONObject entries, JSONObject CLASSIC_DICT,
                                                          Long taskId, Integer dateId, String crawlerProvinceName,
                                                          String crawlerType, String crawlerCallbackFileName,
                                                          String crawlerDataBelongFileName, String crawlerUrl, String versionNo, String provinceCode, String provinceName) {
        String[] classicType = StringUtils.split(key, "_");
        if (classicType == null || classicType.length < 2) {
            log.warn("{} [DATA-WARN] key格式异常: {}", LOG_PREFIX, key);
            return List.of();
        }

        String classicTypeOne = classicType[0];
        String classicTypeTwo = classicType[1];

        JSONObject valueData = entries.getJSONObject(key);
        if (valueData == null) {
            log.warn("{} [DATA-WARN] entries缺少key: {}", LOG_PREFIX, key);
            return List.of();
        }

        log.debug("{} [DATA-PROCESS] 处理资费类型数据: classicTypeOne={}, classicTypeTwo={}", LOG_PREFIX, classicTypeOne,
                classicTypeTwo);
        if (valueData.containsKey(FIELD_TARIFF_DETAIL_LIST)) {
            JSONArray tariffDetailInfoList = valueData.getJSONArray(FIELD_TARIFF_DETAIL_LIST);
            return analysisTariffDetailInfoList(tariffDetailInfoList, taskId, dateId,
                    crawlerProvinceName, crawlerType, crawlerCallbackFileName,
                    crawlerDataBelongFileName, classicTypeOne, classicTypeTwo, crawlerUrl, versionNo, provinceCode, provinceName);
        } else if (valueData.containsKey(FIELD_THREE_DETAIL_DATE)) {
            JSONArray threeDetailDate = valueData.getJSONArray(FIELD_THREE_DETAIL_DATE);
            return analysisThreeDetailData(threeDetailDate, taskId, dateId,
                    crawlerProvinceName, crawlerType, crawlerCallbackFileName,
                    crawlerDataBelongFileName, classicTypeOne, classicTypeTwo, crawlerUrl, versionNo, provinceCode, provinceName);
        }
        return List.of();
    }

    /**
     * 统一省份解析和赋值
     *
     * @param record   目标记录
     * @param tariffNo 资费编号
     * @param taskId   任务ID
     * @param dateId   日期ID
     * @param rawJson  原始json
     * @param operator 运营商
     * @return true=成功，false=失败
     */
    protected void parseAndSetProvince(XtyTariffCrawlRecord record, String tariffNo, Long taskId, Integer dateId,
                                     String rawJson, OperatorEnum operator, String crawlerProvinceName, String crawlerType) {
        if (StringUtils.isBlank(tariffNo)) {
            log.error("{} [DATA-ERROR] 资费方案编码为空或长度不足: {}", LOG_PREFIX, tariffNo);
            recordErrorLog(operator, RunErrorEnum.EMPTY_TARIFF_NO, taskId, dateId, crawlerProvinceName, crawlerType,
                    rawJson);
            return;
        }

        try {
            String tariffProvinceCode = tariffNo.substring(2, 4);
            XtyTariffProvinceVo tariffProvince = tariffProvinceService.getByTariffProvinceCode(tariffProvinceCode);
            record.setTariffNo(tariffNo);
            record.setTariffProvinceCode(tariffProvinceCode);
            record.setTariffProvinceName(tariffProvince.getTariffProvinceName());
            record.setProvinceCode(tariffProvince.getProvinceCode());
            record.setProvinceName(tariffProvince.getProvinceName());
            log.debug("{} [DATA-SUCCESS] 省份解析成功, tariffNo: {}, 省份: {}", LOG_PREFIX, tariffNo,
                    tariffProvince.getProvinceName());
        } catch (Exception e) {
            log.error("{} [DATA-ERROR] 省份解析失败: {}, 错误: {}", LOG_PREFIX, tariffNo, e.getMessage(), e);
            recordErrorLog(operator, RunErrorEnum.ERROR_TARIFF_NO, taskId, dateId, crawlerProvinceName, crawlerType,
                    rawJson);
        }
    }

    /**
     * 统一通用字段赋值
     *
     * @param record 目标记录
     * @param data   json对象
     */
    protected void setCommonFields(XtyTariffCrawlRecord record, JSONObject data) {
        try {
            record.setOtherContent(StringUtils.trimToEmpty(data.getStr(FIELD_SERVICE_CONTENT)));
            record.setApplicablePeople(StringUtils.trimToEmpty(data.getStr(FIELD_SUIT_AREA)));
            record.setValidPeriod(StringUtils.trimToEmpty(data.getStr(FIELD_EFFECTIVE_PERIOD)));
            record.setChannel(StringUtils.trimToEmpty(data.getStr(FIELD_SALE_CHANNEL)));
            String onlineDate = StringUtils.trimToEmpty(data.getStr(FIELD_ONLINE_DATE));
            record.setOnlineDay(formatDateStr(onlineDate));
            String offlineDate = StringUtils.trimToEmpty(data.getStr(FIELD_DOWN_LINE_DATE));
            record.setOfflineDay(formatDateStr(offlineDate));
            record.setDuration(StringUtils.trimToEmpty(data.getStr(FIELD_IN_NET_PERIOD)));
            record.setUnsubscribe(StringUtils.trimToEmpty(data.getStr(FIELD_UNSUBSCRIBE_METHOD)));
            record.setResponsibility(StringUtils.trimToEmpty(data.getStr(FIELD_NON_CONTRACT)));
            log.debug("{} [DATA-SUCCESS] 设置通用字段完成, recordId: {}", LOG_PREFIX, record.getId());
        } catch (Exception e) {
            log.error("{} [DATA-FAIL] 设置通用字段失败, recordId: {}, 错误: {}", LOG_PREFIX, record.getId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 解析资费明细列表
     *
     * @param tariffDetailInfoList 明细数组
     * @param taskId               任务ID
     * @param dateId               日期ID
     */
    protected List<XtyTariffCrawlRecord> analysisTariffDetailInfoList(JSONArray tariffDetailInfoList, Long taskId, Integer dateId,
                                                                    String crawlerProvinceName, String crawlerType,
                                                                    String crawlerCallbackFileName, String crawlerDataBelongFileName, String classicTypeOne,
                                                                    String classicTypeTwo, String crawlerUrl, String versionNo, String provinceCode, String provinceName) {
        log.info("{} [DATA-START] 开始解析资费明细列表, 数据条数: {}", LOG_PREFIX, tariffDetailInfoList.size());
        int successCount = 0;
        int failCount = 0;
        List<XtyTariffCrawlRecord> records = new ArrayList<>();
        for (int i = 0; i < tariffDetailInfoList.size(); i++) {
            try {
                JSONObject tariffDetailInfo = tariffDetailInfoList.getJSONObject(i);
                JSONObject packageinfo = tariffDetailInfo.getJSONObject("packageinfo");
                if(packageinfo==null) {
                    continue;
                }
                JSONObject insideData = packageinfo.getJSONObject("insideData");
                XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
                record.setExtendedFields(JSONUtil.toJsonStr(tariffDetailInfo));
                record.setTaskId(taskId);
                record.setDateId(dateId);
                record.setName(tariffDetailInfo.getStr("taocanName"));
                String tariffNo = StringUtils.trimToEmpty(packageinfo.getStr("recordNum"));
                // 省份解析
                parseAndSetProvince(record, tariffNo, taskId, dateId, JSONUtil.toJsonStr(tariffDetailInfo),
                        OperatorEnum.UNICOM, crawlerProvinceName, crawlerType);
                record.setTariffNo(tariffNo);
                record.setEntCode(OperatorEnum.UNICOM.getCode());
                record.setEntName(OperatorEnum.UNICOM.getName());
                record.setFees(StringUtils.trimToEmpty(tariffDetailInfo.getStr("files")));
                record.setFeesUnit(StringUtils.trimToEmpty(tariffDetailInfo.getStr("zifeiType")));
                // 语音/流量/短信/增值/定向/权益
                record.setCall(StringUtils.trimToEmpty(insideData.getStr("inVoicetime")));
                record.setData(StringUtils.trimToEmpty(insideData.getStr("inFlowgn")));
                record.setDataUnit(StringUtils.trimToEmpty(packageinfo.getStr("inFreeCardUnit")));
                record.setSms(StringUtils.trimToEmpty(insideData.getStr("inSms")));
                record.setIncrementBusiness(StringUtils.trimToEmpty(insideData.getStr("inIncrementbusiness")));
                record.setOrientTraffic(StringUtils.trimToEmpty(insideData.getStr("inDirectedflow")));
                record.setOrientTrafficUnit(StringUtils.trimToEmpty(packageinfo.getStr("inDirectedflowUnit")));
                record.setRights(StringUtils.trimToEmpty(packageinfo.getStr("addPrivilege")));
                record.setCrawlerProvinceName(crawlerProvinceName);
                record.setCrawlerType(crawlerType);
                record.setCrawlerDataBelongFileName(crawlerDataBelongFileName);
                record.setCrawlerCallbackFileName(crawlerCallbackFileName);
                record.setClassicTypeOne(classicTypeOne);
                record.setClassicTypeTwo(classicTypeTwo);
                record.setTariffType(StringUtils.trimToEmpty(packageinfo.getStr("phoneType")));
                record.setOutFeeContent(StringUtils.trimToEmpty(insideData.getStr("overUseFee")));
                // 通用字段
                setCommonFields(record, packageinfo);
                record.setCrawlUrl(crawlerUrl);
                record.setVersionNo(versionNo);
                record.setProvinceName(provinceName);
                record.setProvinceCode(provinceCode);
                records.add(record);
                successCount++;
                log.debug("{} [DATA-SUCCESS] 成功处理资费明细数据, index: {}, tariffNo: {}", LOG_PREFIX, i, tariffNo);
            } catch (Exception e) {
                failCount++;
                log.error("{} [DATA-FAIL] 处理资费明细数据失败, index: {}, 错误: {}", LOG_PREFIX, i, e.getMessage(), e);
                throw e;
            }
        }

        log.info("{} [DATA-SUCCESS] 完成资费明细列表解析, 成功: {}, 失败: {}", LOG_PREFIX, successCount, failCount);
        return records;
    }

    /**
     * 解析三网数据（固话、宽带、叠加包、新业务、营销活动）
     *
     * @param threeDetailDate 三网数据数组
     * @param taskId          任务ID
     * @param dateId          日期ID
     */
    protected List<XtyTariffCrawlRecord> analysisThreeDetailData(JSONArray threeDetailDate, Long taskId, Integer dateId,
                                                               String crawlerProvinceName, String crawlerType, String crawlerCallbackFileName,
                                                               String crawlerDataBelongFileName, String classicTypeOne, String classicTypeTwo, String crawlerUrl, String versionNo, String provinceCode, String provinceName) {
        log.info("{} [DATA-START] 开始解析三网数据, 数据条数: {}", LOG_PREFIX, threeDetailDate.size());
        int successCount = 0;
        int failCount = 0;
        List<XtyTariffCrawlRecord> records = new ArrayList<>();
        for (int i = 0; i < threeDetailDate.size(); i++) {
            try {
                JSONObject detailData = threeDetailDate.getJSONObject(i);
                // 固话
                if (detailData.getJSONObject("fix") != null) {
                    records.add(analysisThreeDetailDataForFix(detailData, taskId, dateId, crawlerProvinceName, crawlerType,
                            crawlerCallbackFileName, crawlerDataBelongFileName, classicTypeOne, classicTypeTwo,
                            crawlerUrl, versionNo, provinceCode, provinceName));
                } else if (detailData.getJSONObject("broad") != null) {
                    // 宽带
                    records.add(analysisThreeDetailDataForBroad(detailData, taskId, dateId, crawlerProvinceName, crawlerType,
                            crawlerCallbackFileName, crawlerDataBelongFileName, classicTypeOne, classicTypeTwo,
                            crawlerUrl, versionNo, provinceCode, provinceName));
                } else if (detailData.getJSONObject("linkFourDetailDTO") != null) {
                    // 叠加包
                    records.add(analysisThreeDetailDataForStacking(detailData, taskId, dateId, crawlerProvinceName, crawlerType,
                            crawlerCallbackFileName, crawlerDataBelongFileName, classicTypeOne, classicTypeTwo,
                            crawlerUrl, versionNo, provinceCode, provinceName));
                } else if (detailData.getJSONObject("linkFiveDetailDTO") != null) {
                    // 新业务
                    records.add(analysisThreeDetailDataForNewBusiness(detailData, taskId, dateId, crawlerProvinceName, crawlerType,
                            crawlerCallbackFileName, crawlerDataBelongFileName, classicTypeOne, classicTypeTwo,
                            crawlerUrl, versionNo, provinceCode, provinceName));
                } else if (detailData.getJSONObject("linkSixDetailDTO") != null) {
                    // 营销活动
                    records.add(analysisThreeDetailDataForMarketingActivity(detailData, taskId, dateId, crawlerProvinceName,
                            crawlerType, crawlerCallbackFileName, crawlerDataBelongFileName, classicTypeOne,
                            classicTypeTwo, crawlerUrl, versionNo, provinceCode, provinceName));
                } else if (detailData.getJSONObject("pic") != null) {
                    // 营销活动
                    records.add(analysisThreeDetailDataForMarketingPic(detailData, taskId, dateId, crawlerProvinceName, crawlerType,
                            crawlerCallbackFileName, crawlerDataBelongFileName, classicTypeOne, classicTypeTwo,
                            crawlerUrl, versionNo, provinceCode, provinceName));
                }
                successCount++;
            } catch (Exception e) {
                failCount++;
                log.error("{} [DATA-FAIL] 处理三网数据失败, index: {}, 错误: {}", LOG_PREFIX, i, e.getMessage(), e);
                throw e;
            }
        }

        log.info("{} [DATA-SUCCESS] 完成三网数据解析, 成功: {}, 失败: {}", LOG_PREFIX, successCount, failCount);
        return records;
    }

    /**
     * 个人套餐-固话
     *
     * @param detailData 三网数据
     * @param taskId     任务ID
     * @param dateId     日期ID
     */
    protected XtyTariffCrawlRecord analysisThreeDetailDataForFix(JSONObject detailData, Long taskId, Integer dateId,
                                                               String crawlerProvinceName, String crawlerType, String crawlerCallbackFileName,
                                                               String crawlerDataBelongFileName, String classicTypeOne, String classicTypeTwo, String crawlerUrl, String versionNo, String provinceCode, String provinceName) {
        XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();

        record.setExtendedFields(JSONUtil.toJsonStr(detailData));
        record.setTaskId(taskId);
        record.setDateId(dateId);
        record.setName(StringUtils.trimToEmpty(detailData.getStr(FIELD_NAME_THIRD)));
        String tariffNo = StringUtils.trimToEmpty(detailData.getStr(FIELD_RECORD_NUM));
        parseAndSetProvince(record, tariffNo, taskId, dateId, JSONUtil.toJsonStr(detailData), OperatorEnum.UNICOM,
                crawlerProvinceName, crawlerType);
        record.setEntCode(OperatorEnum.UNICOM.getCode());
        record.setEntName(OperatorEnum.UNICOM.getName());
        record.setFees(StringUtils.trimToEmpty(detailData.getStr(FIELD_MAIN_FEE)));
        JSONObject fix = detailData.getJSONObject("fix");
        record.setFeesUnit(StringUtils.trimToEmpty(fix.getStr("feeUnit")));
        record.setCall(StringUtils.trimToEmpty(fix.getStr("feeVoice")));
        setCommonFields(record, detailData);
        record.setCrawlerProvinceName(crawlerProvinceName);
        record.setCrawlerType(crawlerType);
        record.setCrawlerDataBelongFileName(crawlerDataBelongFileName);
        record.setCrawlerCallbackFileName(crawlerCallbackFileName);
        record.setClassicTypeOne(classicTypeOne);
        record.setClassicTypeTwo(classicTypeTwo);

        String phoneType = StringUtils.trimToEmpty(detailData.getStr("phoneType"));
        String activeType = StringUtils.trimToEmpty(detailData.getStr("activeType"));
        String tariffType = StringUtils.isNotEmpty(phoneType) ? phoneType : activeType;
        record.setTariffType(StringUtils.trimToEmpty(tariffType));

        record.setCrawlUrl(crawlerUrl);
        record.setVersionNo(versionNo);
        record.setProvinceCode(provinceCode);
        record.setProvinceName(provinceName);

        return record;
    }

    /**
     * 个人套餐-宽带
     */
    protected XtyTariffCrawlRecord analysisThreeDetailDataForBroad(JSONObject detailData, Long taskId, Integer dateId,
                                                                 String crawlerProvinceName, String crawlerType, String crawlerCallbackFileName,
                                                                 String crawlerDataBelongFileName, String classicTypeOne, String classicTypeTwo, String crawlerUrl, String versionNo, String provinceCode, String provinceName) {
        XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
        record.setExtendedFields(JSONUtil.toJsonStr(detailData));
        record.setTaskId(taskId);
        record.setDateId(dateId);
        record.setName(StringUtils.trimToEmpty(detailData.getStr(FIELD_NAME_THIRD)));
        String tariffNo = StringUtils.trimToEmpty(detailData.getStr(FIELD_RECORD_NUM));
        parseAndSetProvince(record, tariffNo, taskId, dateId, JSONUtil.toJsonStr(detailData), OperatorEnum.UNICOM,
                crawlerProvinceName, crawlerType);
        record.setEntCode(OperatorEnum.UNICOM.getCode());
        record.setEntName(OperatorEnum.UNICOM.getName());
        record.setFees(StringUtils.trimToEmpty(detailData.getStr(FIELD_MAIN_FEE)));
        JSONObject broad = detailData.getJSONObject("broad");
        record.setFeesUnit(StringUtils.trimToEmpty(broad.getStr("mainFeeUnit")));
        record.setCall(StringUtils.trimToEmpty(broad.getStr("netCountryVoice")));
        record.setOrientTraffic(StringUtils.trimToEmpty(broad.getStr("directFlow")));
        record.setOrientTrafficUnit(StringUtils.trimToEmpty(broad.getStr("directFlowUnit")));
        record.setData(StringUtils.trimToEmpty(broad.getStr("netCountryFlow")));
        record.setDataUnit(StringUtils.trimToEmpty(broad.getStr("netCountryFlowUnit")));
        record.setBandwidth(StringUtils.trimToEmpty(broad.getStr("downSpeed")));
        setCommonFields(record, detailData);
        record.setCrawlerProvinceName(crawlerProvinceName);
        record.setCrawlerType(crawlerType);
        record.setCrawlerDataBelongFileName(crawlerDataBelongFileName);
        record.setCrawlerCallbackFileName(crawlerCallbackFileName);
        record.setClassicTypeOne(classicTypeOne);
        record.setClassicTypeTwo(classicTypeTwo);
        record.setOutFeeContent(StringUtils.trimToEmpty(detailData.getStr("overUseFee")));
        record.setOtherFeeContent(StringUtils.trimToEmpty(detailData.getStr("elseFeeNew")));

        String phoneType = StringUtils.trimToEmpty(detailData.getStr("phoneType"));
        String activeType = StringUtils.trimToEmpty(detailData.getStr("activeType"));
        String tariffType = StringUtils.isNotEmpty(phoneType) ? phoneType : activeType;
        record.setTariffType(StringUtils.trimToEmpty(tariffType));

        record.setCrawlUrl(crawlerUrl);
        record.setVersionNo(versionNo);
        record.setProvinceCode(provinceCode);
        record.setProvinceName(provinceName);

        return record;
    }

    /**
     * 个人套餐-叠加包
     */
    protected XtyTariffCrawlRecord analysisThreeDetailDataForStacking(JSONObject detailData, Long taskId, Integer dateId,
                                                                    String crawlerProvinceName, String crawlerType, String crawlerCallbackFileName,
                                                                    String crawlerDataBelongFileName, String classicTypeOne, String classicTypeTwo, String crawlerUrl, String versionNo, String provinceCode, String provinceName) {
        log.info("{} [DATA-START] detailData:{}", LOG_PREFIX, detailData);
        XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
        record.setExtendedFields(JSONUtil.toJsonStr(detailData));
        JSONObject linkFourDetailDTO = detailData.getJSONObject("linkFourDetailDTO");
        record.setTaskId(taskId);
        record.setDateId(dateId);
        record.setName(StringUtils.trimToEmpty(linkFourDetailDTO.getStr("addPlusName")));
        String tariffNo = StringUtils.trimToEmpty(detailData.getStr(FIELD_RECORD_NUM));
        parseAndSetProvince(record, tariffNo, taskId, dateId, JSONUtil.toJsonStr(detailData), OperatorEnum.UNICOM,
                crawlerProvinceName, crawlerType);
        record.setEntCode(OperatorEnum.UNICOM.getCode());
        record.setEntName(OperatorEnum.UNICOM.getName());
        record.setFees(StringUtils.trimToEmpty(detailData.getStr(FIELD_MAIN_FEE)));
        String feeUnit = StringUtils.trimToEmpty(linkFourDetailDTO.getStr("addPlusUnit"));
        if (StringUtils.equals("1", feeUnit)) {
            feeUnit = "月费";
        }
        record.setFeesUnit(feeUnit);
        record.setOrientTraffic(StringUtils.trimToEmpty(linkFourDetailDTO.getStr("directFlow")));
        record.setOrientTrafficUnit(StringUtils.trimToEmpty(linkFourDetailDTO.getStr("directFlowUnit")));
        record.setCall(StringUtils.trimToEmpty(linkFourDetailDTO.getStr("addPlusGnVoice")));
        record.setData(StringUtils.trimToEmpty(linkFourDetailDTO.getStr("addPlusGnFlow")));
        record.setDataUnit(StringUtils.trimToEmpty(linkFourDetailDTO.getStr("netCountryFlowUnit")));
        record.setRights(StringUtils.trimToEmpty(linkFourDetailDTO.getStr("addPlusEquity")));
        record.setSms(StringUtils.trimToEmpty(linkFourDetailDTO.getStr("addPlusSms")));
        record.setBandwidth(StringUtils.trimToEmpty(linkFourDetailDTO.getStr("addPlusDaiKuan")));
        setCommonFields(record, detailData);
        record.setCrawlerProvinceName(crawlerProvinceName);
        record.setCrawlerType(crawlerType);
        record.setCrawlerDataBelongFileName(crawlerDataBelongFileName);
        record.setCrawlerCallbackFileName(crawlerCallbackFileName);
        record.setClassicTypeOne(classicTypeOne);
        record.setClassicTypeTwo(classicTypeTwo);

        String phoneType = StringUtils.trimToEmpty(detailData.getStr("phoneType"));
        String activeType = StringUtils.trimToEmpty(detailData.getStr("activeType"));
        String tariffType = StringUtils.isNotEmpty(phoneType) ? phoneType : activeType;
        record.setTariffType(StringUtils.trimToEmpty(tariffType));

        record.setCrawlUrl(crawlerUrl);
        record.setVersionNo(versionNo);
        record.setProvinceCode(provinceCode);
        record.setProvinceName(provinceName);
        return record;
    }

    /**
     * 个人套餐-新业务
     */
    protected XtyTariffCrawlRecord analysisThreeDetailDataForNewBusiness(JSONObject detailData, Long taskId, Integer dateId,
                                                                       String crawlerProvinceName, String crawlerType, String crawlerCallbackFileName,
                                                                       String crawlerDataBelongFileName, String classicTypeOne, String classicTypeTwo, String crawlerUrl, String versionNo, String provinceCode, String provinceName) {
        XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
        record.setExtendedFields(JSONUtil.toJsonStr(detailData));
        JSONObject linkFiveDetailDTO = detailData.getJSONObject("linkFiveDetailDTO");
        record.setTaskId(taskId);
        record.setDateId(dateId);
        record.setName(StringUtils.trimToEmpty(linkFiveDetailDTO.getStr("newProductName")));
        String tariffNo = StringUtils.trimToEmpty(detailData.getStr(FIELD_RECORD_NUM));
        parseAndSetProvince(record, tariffNo, taskId, dateId, JSONUtil.toJsonStr(detailData), OperatorEnum.UNICOM,
                crawlerProvinceName, crawlerType);
        record.setEntCode(OperatorEnum.UNICOM.getCode());
        record.setEntName(OperatorEnum.UNICOM.getName());
        record.setFees(detailData.getStr(FIELD_MAIN_FEE));
        String feeUnit = StringUtils.trimToEmpty(linkFiveDetailDTO.getStr("feeUnit"));
        if (StringUtils.equals("1", feeUnit)) {
            feeUnit = "月费";
        }
        record.setFeesUnit(feeUnit);
        setCommonFields(record, detailData);
        record.setCrawlerProvinceName(crawlerProvinceName);
        record.setCrawlerType(crawlerType);
        record.setCrawlerDataBelongFileName(crawlerDataBelongFileName);
        record.setCrawlerCallbackFileName(crawlerCallbackFileName);
        record.setClassicTypeOne(classicTypeOne);
        record.setClassicTypeTwo(classicTypeTwo);

        String phoneType = StringUtils.trimToEmpty(detailData.getStr("phoneType"));
        String activeType = StringUtils.trimToEmpty(detailData.getStr("activeType"));
        String tariffType = StringUtils.isNotEmpty(phoneType) ? phoneType : activeType;
        record.setTariffType(StringUtils.trimToEmpty(tariffType));

        record.setCrawlUrl(crawlerUrl);
        record.setVersionNo(versionNo);
        record.setProvinceCode(provinceCode);
        record.setProvinceName(provinceName);

        record.setOutFeeContent(StringUtils.trimToEmpty(detailData.getStr("overUseFee")));
        record.setOtherFeeContent(StringUtils.trimToEmpty(detailData.getStr("elseFeeNew")));

        return record;
    }

    /**
     * 个人套餐-营销活动
     */
    protected XtyTariffCrawlRecord analysisThreeDetailDataForMarketingActivity(JSONObject detailData, Long taskId, Integer dateId,
                                                                             String crawlerProvinceName, String crawlerType, String crawlerCallbackFileName,
                                                                             String crawlerDataBelongFileName, String classicTypeOne, String classicTypeTwo, String crawlerUrl, String versionNo, String provinceCode, String provinceName) {
        XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
        record.setExtendedFields(JSONUtil.toJsonStr(detailData));
        JSONObject linkSixDetailDTO = detailData.getJSONObject("linkSixDetailDTO");
        record.setTaskId(taskId);
        record.setDateId(dateId);
        String name = StringUtils.trimToEmpty(detailData.getStr(FIELD_NAME_THIRD));
        if(StringUtils.isBlank(name)) {
            name = StringUtils.trimToEmpty(detailData.getStr("nameActiveSale"));
        }
        record.setName(name);
        String tariffNo = StringUtils.trimToEmpty(detailData.getStr(FIELD_RECORD_NUM));
        parseAndSetProvince(record, tariffNo, taskId, dateId, JSONUtil.toJsonStr(detailData), OperatorEnum.UNICOM,
                crawlerProvinceName, crawlerType);
        record.setEntCode(OperatorEnum.UNICOM.getCode());
        record.setEntName(OperatorEnum.UNICOM.getName());
        String fees = StringUtils.trimToEmpty(linkSixDetailDTO.getStr("saleActiveFee"));
        // 验证fees是否为纯数字
        if (StringUtils.isNumeric(fees)) {
            record.setFees(fees);
            record.setFeesUnit(StringUtils.trimToEmpty(linkSixDetailDTO.getStr("mainFeeUnit")));
        }
        String serviceContent = StringUtils.trimToEmpty(detailData.getStr("serviceContent"));
        if(StringUtils.isBlank(serviceContent)) {
            serviceContent = StringUtils.trimToEmpty(linkSixDetailDTO.getStr("saleActiveContAndRule"));
        }
        record.setOtherContent(serviceContent);
        record.setApplicablePeople(StringUtils.trimToEmpty(linkSixDetailDTO.getStr("saleActiveArea")));
        record.setValidPeriod(StringUtils.trimToEmpty(detailData.getStr("effectivePeriod")));
        String saleChannel = StringUtils.trimToEmpty(detailData.getStr("saleChannel"));
        if(StringUtils.isBlank(saleChannel)) {
            saleChannel = StringUtils.trimToEmpty(linkSixDetailDTO.getStr("saleActiveSaleChannel"));
        }
        record.setChannel(saleChannel);
        record.setOnlineDay(StringUtils.trimToEmpty(detailData.getStr("onlineDate")));
        record.setOfflineDay(StringUtils.trimToEmpty(detailData.getStr("downLineDate")));
        record.setDuration(StringUtils.trimToEmpty(detailData.getStr("inNetPeriod")));
        record.setUnsubscribe(StringUtils.trimToEmpty(detailData.getStr("unsubscribeMethod")));
        record.setResponsibility(StringUtils.trimToEmpty(detailData.getStr("nonContract")));
        record.setCrawlerProvinceName(crawlerProvinceName);
        record.setCrawlerType(crawlerType);
        record.setCrawlerDataBelongFileName(crawlerDataBelongFileName);
        record.setCrawlerCallbackFileName(crawlerCallbackFileName);
        record.setClassicTypeOne(classicTypeOne);
        record.setClassicTypeTwo(classicTypeTwo);

        String feeVoice = linkSixDetailDTO.getStr("feeVoice");
        if(StringUtils.isNumeric(feeVoice)) {
            record.setCall(StringUtils.trimToEmpty(feeVoice) + "分钟");
        }

        String netCountryFlow = linkSixDetailDTO.getStr("netCountryFlow");
        String netCountryFlowUnit = linkSixDetailDTO.getStr("netCountryFlowUnit");
        if(StringUtils.isNumeric(netCountryFlow)) {
            record.setData(StringUtils.trimToEmpty(netCountryFlow));
            if(StringUtils.isNotBlank(netCountryFlowUnit)) {
                record.setDataUnit(netCountryFlowUnit);
            }
        }

        String msgColor = linkSixDetailDTO.getStr("msgColor");
        if(StringUtils.isNotBlank(msgColor)) {
            record.setSms(StringUtils.trimToEmpty(msgColor));
        }

        String directFlow = linkSixDetailDTO.getStr("directFlow");
        String directFlowUnit = linkSixDetailDTO.getStr("directFlowUnit");
        if(StringUtils.isNumeric(directFlow)) {
            record.setOrientTraffic(StringUtils.trimToEmpty(directFlow));
            if(StringUtils.isNotBlank(directFlowUnit)) {
                record.setOrientTrafficUnit(directFlowUnit);
            }
        }

        record.setIptv(StringUtils.trimToEmpty(linkSixDetailDTO.getStr("saleIPTV")));
        record.setOthers(StringUtils.trimToEmpty(linkSixDetailDTO.getStr("otherIntroduction")));
        String phoneType = StringUtils.trimToEmpty(detailData.getStr("phoneType"));
        String activeType = StringUtils.trimToEmpty(detailData.getStr("activeType"));
        String tariffType = StringUtils.isNotEmpty(phoneType) ? phoneType : activeType;
        record.setTariffType(StringUtils.trimToEmpty(tariffType));

        record.setCrawlUrl(crawlerUrl);
        record.setVersionNo(versionNo);
        record.setProvinceCode(provinceCode);
        record.setProvinceName(provinceName);
        return record;
    }

    /**
     * 营销活动
     */
    protected XtyTariffCrawlRecord analysisThreeDetailDataForMarketingPic(JSONObject detailData, Long taskId, Integer dateId,
                                                                        String crawlerProvinceName, String crawlerType, String crawlerCallbackFileName,
                                                                        String crawlerDataBelongFileName, String classicTypeOne, String classicTypeTwo, String crawlerUrl, String versionNo, String provinceCode, String provinceName) {
        XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
        record.setExtendedFields(JSONUtil.toJsonStr(detailData));
        JSONObject linkSixDetailDTO = detailData.getJSONObject("pic");
        record.setTaskId(taskId);
        record.setDateId(dateId);
        String name = StringUtils.trimToEmpty(detailData.getStr(FIELD_NAME_THIRD));
        if(StringUtils.isBlank(name)) {
            name = StringUtils.trimToEmpty(detailData.getStr("nameActiveSale"));
        }
        record.setName(name);
        String tariffNo = StringUtils.trimToEmpty(detailData.getStr(FIELD_RECORD_NUM));
        parseAndSetProvince(record, tariffNo, taskId, dateId, JSONUtil.toJsonStr(detailData), OperatorEnum.UNICOM,
                crawlerProvinceName, crawlerType);
        record.setEntCode(OperatorEnum.UNICOM.getCode());
        record.setEntName(OperatorEnum.UNICOM.getName());
        String fees = StringUtils.trimToEmpty(linkSixDetailDTO.getStr("mainFee"));
        // 验证fees是否为纯数字
        if (StringUtils.isNumeric(fees)) {
            record.setFees(fees);
            record.setFeesUnit(StringUtils.trimToEmpty(linkSixDetailDTO.getStr("mainFeeUnit")));
        }
        record.setOtherContent(StringUtils.trimToEmpty(detailData.getStr("serviceContent")));
        record.setApplicablePeople(StringUtils.trimToEmpty(linkSixDetailDTO.getStr("saleActiveArea")));
        record.setValidPeriod(StringUtils.trimToEmpty(detailData.getStr("effectivePeriod")));
        record.setChannel(StringUtils.trimToEmpty(detailData.getStr("saleChannel")));
        record.setOnlineDay(StringUtils.trimToEmpty(detailData.getStr("onlineDate")));
        record.setOfflineDay(StringUtils.trimToEmpty(detailData.getStr("downLineDate")));
        record.setDuration(StringUtils.trimToEmpty(detailData.getStr("inNetPeriod")));
        record.setUnsubscribe(StringUtils.trimToEmpty(detailData.getStr("unsubscribeMethod")));
        record.setResponsibility(StringUtils.trimToEmpty(detailData.getStr("nonContract")));
        record.setCrawlerProvinceName(crawlerProvinceName);
        record.setCrawlerType(crawlerType);
        record.setCrawlerDataBelongFileName(crawlerDataBelongFileName);
        record.setCrawlerCallbackFileName(crawlerCallbackFileName);
        record.setClassicTypeOne(classicTypeOne);
        record.setClassicTypeTwo(classicTypeTwo);
        String phoneType = StringUtils.trimToEmpty(detailData.getStr("phoneType"));
        String activeType = StringUtils.trimToEmpty(detailData.getStr("activeType"));
        String tariffType = StringUtils.isNotEmpty(phoneType) ? phoneType : activeType;
        record.setTariffType(StringUtils.trimToEmpty(tariffType));
        record.setCrawlUrl(crawlerUrl);
        record.setVersionNo(versionNo);
        record.setProvinceCode(provinceCode);
        record.setProvinceName(provinceName);
        return record;
    }
}
