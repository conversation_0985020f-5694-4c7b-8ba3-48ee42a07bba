package com.yunqu.crawler.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.yunqu.crawler.domain.XtyTariffCrawlRecordLib;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 信通院资费爬虫数据视图对象 xty_tariff_crawl_record_lib
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = XtyTariffCrawlRecordLib.class)
public class XtyTariffCrawlRecordLibVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 报送库资费ID
     */
    @ExcelProperty(value = "报送库资费ID")
    private String tariffRecordId;

    /**
     * 任务ID
     */
    @ExcelProperty(value = "任务ID")
    private Long taskId;

    /**
     * 省份编码
     */
    @ExcelProperty(value = "省份编码")
    private String provinceCode;

    /**
     * 省份名称
     */
    @ExcelProperty(value = "省份名称")
    private String provinceName;

    /**
     * 运营商编码
     */
    @ExcelProperty(value = "运营商编码")
    private String entCode;

    /**
     * 运营商名称
     */
    @ExcelProperty(value = "运营商名称")
    private String entName;

    /**
     * 资费名称
     */
    @ExcelProperty(value = "资费名称")
    private String name;

    /**
     * 资费方案编号
     */
    @ExcelProperty(value = "资费方案编号")
    private String tariffNo;

    /**
     * 资费标准
     */
    @ExcelProperty(value = "资费标准")
    private String fees;

    /**
     * 资费单位
     */
    @ExcelProperty(value = "资费单位")
    private String feesUnit;

    /**
     * 语音
     */
    @ExcelProperty(value = "语音")
    private String call;

    /**
     * 通用流量
     */
    @ExcelProperty(value = "通用流量")
    private String data;

    /**
     * 流量单位
     */
    @ExcelProperty(value = "流量单位")
    private String dataUnit;

    /**
     * 短彩信
     */
    @ExcelProperty(value = "短彩信")
    private String sms;

    /**
     * 定向流量
     */
    @ExcelProperty(value = "定向流量")
    private String orientTraffic;

    /**
     * 定向流量单位
     */
    @ExcelProperty(value = "定向流量单位")
    private String orientTrafficUnit;

    /**
     * IPTV
     */
    @ExcelProperty(value = "IPTV")
    private String iptv;

    /**
     * 带宽
     */
    @ExcelProperty(value = "带宽")
    private String bandwidth;

    /**
     * 权益
     */
    @ExcelProperty(value = "权益")
    private String rights;

    /**
     * 增值业务
     */
    @ExcelProperty(value = "增值业务")
    private String incrementBusiness;

    /**
     * 服务内容
     */
    @ExcelProperty(value = "服务内容")
    private String otherContent;

    /**
     * 上线日期
     */
    @ExcelProperty(value = "上线日期")
    private String onlineDay;

    /**
     * 下线日期
     */
    @ExcelProperty(value = "下线日期")
    private String offlineDay;

    /**
     * 资费属性
     */
    @ExcelProperty(value = "资费属性")
    private String tariffAttr;

    /**
     * 适用范围
     */
    @ExcelProperty(value = "适用范围")
    private String applicablePeople;

    /**
     * 适用地区
     */
    @ExcelProperty(value = "适用地区")
    private String applicableArea;

    /**
     * 有效期限
     */
    @ExcelProperty(value = "有效期限")
    private String validPeriod;

    /**
     * 销售渠道
     */
    @ExcelProperty(value = "销售渠道")
    private String channel;

    /**
     * 在网要求
     */
    @ExcelProperty(value = "在网要求")
    private String duration;

    /**
     * 退订方式
     */
    @ExcelProperty(value = "退订方式")
    private String unsubscribe;

    /**
     * 违约责任
     */
    @ExcelProperty(value = "违约责任")
    private String responsibility;

    /**
     * 其他说明
     */
    @ExcelProperty(value = "其他说明")
    private String others;

    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段")
    private String extendedFields;

    /**
     * 爬取数据日期ID
     */
    @ExcelProperty(value = "爬取数据日期ID")
    private Long dateId;

    /**
     * 爬取数据月ID
     */
    @ExcelProperty(value = "爬取数据月ID")
    private Long monthId;

    /**
     * 是否已报送 1是0否
     */
    @ExcelProperty(value = "是否已报送")
    private Integer reported;

    /**
     * 版本号
     */
    @ExcelProperty(value = "版本号")
    private String versionNo;

    /**
     * 资费省份编码
     */
    @ExcelProperty(value = "资费省份编码")
    private String tariffProvinceCode;

    /**
     * 资费省名称
     */
    @ExcelProperty(value = "资费省名称")
    private String tariffProvinceName;

    /**
     * 爬取省份
     */
    @ExcelProperty(value = "爬取省份")
    private String crawlerProvinceName;

    /**
     * 爬取类型
     */
    @ExcelProperty(value = "爬取类型")
    private String crawlerType;

    /**
     * 一级分类
     */
    @ExcelProperty(value = "一级分类")
    private String classicTypeOne;

    /**
     * 儿子分类
     */
    @ExcelProperty(value = "儿子分类")
    private String classicTypeTwo;

    /**
     * 资费分类
     */
    @ExcelProperty(value = "资费分类")
    private String tariffType;

    /**
     * 备用字段
     */
    @ExcelProperty(value = "备用字段")
    private String bak1;

    /**
     * 备用字段
     */
    @ExcelProperty(value = "备用字段")
    private String bak2;

    /**
     * 备用字段
     */
    @ExcelProperty(value = "备用字段")
    private String bak3;

    /**
     * 备用字段
     */
    @ExcelProperty(value = "备用字段")
    private String bak4;

    /**
     * 备用字段
     */
    @ExcelProperty(value = "备用字段")
    private String bak5;

    /**
     * 备用字段
     */
    @ExcelProperty(value = "备用字段")
    private String bak6;

    /**
     * 备用字段
     */
    @ExcelProperty(value = "备用字段")
    private String bak7;

    /**
     * 备用字段
     */
    @ExcelProperty(value = "备用字段")
    private String bak8;

    /**
     * 备用字段
     */
    @ExcelProperty(value = "备用字段")
    private String bak9;

    /**
     * 备用字段
     */
    @ExcelProperty(value = "备用字段")
    private String bak10;

    /**
     * 备用字段
     */
    private String bak11;


    /**
     * 备用字段
     */
    private String bak12;

    /**
     * 备用字段
     */
    private String bak13;

    /**
     * 备用字段
     */
    private String bak14;

    /**
     * 备用字段
     */
    private String bak15;


}
