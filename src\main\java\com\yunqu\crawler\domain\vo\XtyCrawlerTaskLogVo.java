package com.yunqu.crawler.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.yunqu.crawler.domain.XtyCrawlerTaskLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 信通院爬虫执行异常日志信息视图对象 xty_crawler_task_log
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = XtyCrawlerTaskLog.class)
public class XtyCrawlerTaskLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 任务ID
     */
    @ExcelProperty(value = "任务ID")
    private Long taskId;

    /**
     * 异常编码
     */
    @ExcelProperty(value = "异常编码")
    private Integer errorCode;

    /**
     * 异常信息
     */
    @ExcelProperty(value = "异常信息")
    private String errorMsg;

    /**
     * 源数据
     */
    @ExcelProperty(value = "源数据")
    private String sourceData;

    /**
     * 日期ID
     */
    @ExcelProperty(value = "日期ID")
    private Integer dateId;

    /**
     * 运营商编码
     */
    @ExcelProperty(value = "运营商编码")
    private String entCode;

    /**
     * 运营商名称
     */
    @ExcelProperty(value = "运营商名称")
    private String entName;

    /**
     * 省份编码
     */
    @ExcelProperty(value = "省份编码")
    private String provinceCode;

    /**
     * 省份名称
     */
    @ExcelProperty(value = "省份名称")
    private String provinceName;

    /**
     * 爬取省份名称
     */
    private String crawlerProvinceName;

    /**
     * 爬取类型
     */
    private String crawlerType;


}
