# HTTP请求头优化实施记录

## 优化目标
统一抽象HTTP请求头配置，模拟真实浏览器行为，消除代码重复

## 当前问题
1. 每个HTTP请求方法都重复设置相同请求头
2. User-Agent使用测试工具标识"Apifox/1.0.0"，容易被识别
3. 缺少真实浏览器关键请求头：Accept-Language、Referer、Sec-Fetch-*等
4. boundary和Cookie值硬编码，缺少动态生成

## 实施方案
### 1. 创建HttpHeaderManager内部类
- 管理所有HTTP请求头常量
- 提供真实浏览器User-Agent池
- 实现动态boundary和sessionId生成
- 支持完整浏览器请求头模拟

### 2. 创建统一HTTP请求构建方法
- buildHttpRequest()方法统一处理请求构建
- 自动应用标准浏览器请求头
- 支持不同endpoint的URL构建

### 3. 重构现有HTTP请求方法
- getProvinceList()
- getTariffTypeList() 
- getAllPersonalTariffList()
- getOtherTariffList()
- getAllQGList()

## 真实浏览器请求头对比
基于提供的浏览器截图，需要添加：
- Accept-Language: zh-CN,zh;q=0.9
- Accept-Encoding: gzip, deflate, br
- Referer: https://m.client.10010.com
- Sec-Fetch-Dest: empty
- Sec-Fetch-Mode: cors
- Sec-Fetch-Site: same-origin
- Cache-Control: no-cache
- 真实Chrome User-Agent

## 实施状态
- [x] 分析现有代码结构
- [x] 设计优化方案
- [ ] 实现HttpHeaderManager
- [ ] 创建统一请求构建方法
- [ ] 重构现有HTTP请求方法
- [ ] 测试验证
