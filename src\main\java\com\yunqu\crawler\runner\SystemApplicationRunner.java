package com.yunqu.crawler.runner;

import com.yunqu.crawler.base.CacheConstants;
import com.yunqu.crawler.base.Constants;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.redis.utils.RedisUtils;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 系统启动初始化运行器
 *
 * 主要功能：
 * 1. 在系统启动完成后执行初始化操作
 * 2. 初始化运营商省份字典数据到Redis缓存
 * 3. 支持移动和联通两个运营商的省份数据初始化
 *
 * 初始化流程：
 * 1. 系统启动完成后自动触发run方法
 * 2. 依次执行移动和联通省份字典的初始化
 * 3. 将省份数据以key-value形式存储在Redis中
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/5/20
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SystemApplicationRunner implements ApplicationRunner {

    private static final String LOG_PREFIX = "[INIT]"; // 初始化日志前缀

    /**
     * 系统启动完成后的回调方法
     * 执行系统初始化操作，包括：
     * 1. 初始化移动运营商省份字典
     * 2. 初始化联通运营商省份字典
     *
     * @param args 应用程序启动参数
     * @throws Exception 初始化过程中的异常
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("{} [START] ========== 开始系统初始化 ==========", LOG_PREFIX);
        long startTime = System.currentTimeMillis();

        try {
            // 初始化移动运营商省份字典
            initMobileProvinceDict();

            // 初始化联通运营商省份字典
            initUnicomProvinceDict();

            // 初始化电信运营商省份字典
            initGdtProvinceDict();

            // 初始化电信运营商省份字典
            initTelecomProvinceDict();

            log.info("{} [SUCCESS] ========== 系统初始化完成, 耗时: {}ms ==========",
                    LOG_PREFIX, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("{} [FAIL] 系统初始化异常: {}", LOG_PREFIX, e.getMessage(), e);
            throw e;
        }
    }

    private void initGdtProvinceDict() {
        log.info("{} [GDT-START] 开始初始化GDT运营商省份字典", LOG_PREFIX);
        try {
            if (StringUtils.isBlank(Constants.GDT_PROVINCE_DICT_STR))
                return;
            JSONArray provinceDict = JSONUtil.parseArray(Constants.GDT_PROVINCE_DICT_STR);
            for (int i = 0; i < provinceDict.size(); i++) {
                JSONObject dict = provinceDict.getJSONObject(i);
                RedisUtils.setCacheMapValue(CacheConstants.GDT_PROVINCE_DICT_KEY, dict.getStr("code"),
                        dict.getStr("name"));
                RedisUtils.setCacheMapValue(CacheConstants.GDT_PROVINCE_CODE_DICT_KEY, dict.getStr("code"),
                        dict.getStr("provinceCode"));
                RedisUtils.setCacheMapValue(CacheConstants.GDT_PROVINCE_INFO_DICT_KEY, dict.getStr("code"), dict);
            }
        } catch (Exception e) {
            log.error("{} [GDT-ERROR] GDT运营商省份字典初始化异常: {}", LOG_PREFIX, e.getMessage(), e);
        }
    }

    private void initTelecomProvinceDict() {
        log.info("{} [TELECOM-START] 开始初始化电信运营商省份字典", LOG_PREFIX);
        try {
            if (StringUtils.isBlank(Constants.TELECOM_PROVINCE_DICT_STR))
                return;
            JSONArray provinceDict = JSONUtil.parseArray(Constants.TELECOM_PROVINCE_DICT_STR);
            for (int i = 0; i < provinceDict.size(); i++) {
                JSONObject dict = provinceDict.getJSONObject(i);
                RedisUtils.setCacheMapValue(CacheConstants.TELECOM_PROVINCE_DICT_KEY, dict.getStr("code"),
                        dict.getStr("name"));
                RedisUtils.setCacheMapValue(CacheConstants.TELECOM_PROVINCE_CODE_DICT_KEY, dict.getStr("code"),
                        dict.getStr("provinceCode"));
                RedisUtils.setCacheMapValue(CacheConstants.TELECOM_PROVINCE_INFO_DICT_KEY, dict.getStr("code"), dict);
                log.debug("{} [TELECOM-DATA] 电信省份数据写入成功: code={}, name={}", LOG_PREFIX, dict.getStr("code"),
                        dict.getStr("name"));
            }
        } catch (Exception e) {
            log.error("{} [TELECOM-ERROR] 电信运营商省份字典初始化异常: {}", LOG_PREFIX, e.getMessage(), e);
        }
    }

    /**
     * 初始化移动运营商省份字典
     * 将省份数据以id-text形式存储在Redis中
     * 数据来源：Constants.MOBILE_PROVINCE_DICT_STR
     */
    private void initMobileProvinceDict() {
        log.info("{} [MOBILE-START] 开始初始化移动运营商省份字典", LOG_PREFIX);
        long startTime = System.currentTimeMillis();
        int count = 0;

        try {
            String mobileProvinceDictStr = Constants.MOBILE_PROVINCE_DICT_STR;
            JSONArray provinceDict = JSONUtil.parseArray(mobileProvinceDictStr);

            for (int i = 0; i < provinceDict.size(); i++) {
                try {
                    JSONObject dict = provinceDict.getJSONObject(i);
                    String id = dict.getStr("id");
                    String text = dict.getStr("text");
                    String provinceCode = dict.getStr("provinceCode");
                    RedisUtils.setCacheMapValue(CacheConstants.MOBILE_PROVINCE_DICT_KEY, id, text);
                    RedisUtils.setCacheMapValue(CacheConstants.MOBILE_PROVINCE_CODE_DICT_KEY, id, provinceCode);
                    RedisUtils.setCacheMapValue(CacheConstants.MOBILE_PROVINCE_INFO_DICT_KEY, id, dict);
                    count++;
                    log.debug("{} [MOBILE-DATA] 移动省份数据写入成功: id={}, text={}", LOG_PREFIX, id, text);
                } catch (Exception e) {
                    log.error("{} [MOBILE-ERROR] 移动省份数据写入失败, index: {}, 错误: {}", LOG_PREFIX, i, e.getMessage());
                }
            }

            log.info("{} [MOBILE-SUCCESS] 移动运营商省份字典初始化完成, 写入数据: {}, 耗时: {}ms",
                    LOG_PREFIX, count, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("{} [MOBILE-FAIL] 移动运营商省份字典初始化异常: {}", LOG_PREFIX, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 初始化联通运营商省份字典
     * 将省份数据以code-name形式存储在Redis中
     * 数据来源：Constants.UNICOM_PROVINCE_DICT_STR
     */
    private void initUnicomProvinceDict() {
        log.info("{} [UNICOM-START] 开始初始化联通运营商省份字典", LOG_PREFIX);
        long startTime = System.currentTimeMillis();
        int count = 0;

        try {
            String unicomProvinceDictStr = Constants.UNICOM_PROVINCE_DICT_STR;
            JSONArray provinceDict = JSONUtil.parseArray(unicomProvinceDictStr);

            for (int i = 0; i < provinceDict.size(); i++) {
                try {
                    JSONObject dict = provinceDict.getJSONObject(i);
                    String code = dict.getStr("code");
                    String name = dict.getStr("name");
                    String provinceCode = dict.getStr("provinceCode");
                    RedisUtils.setCacheMapValue(CacheConstants.UNICOM_PROVINCE_DICT_KEY, code, name);
                    RedisUtils.setCacheMapValue(CacheConstants.UNICOM_PROVINCE_CODE_DICT_KEY, code, provinceCode);
                    RedisUtils.setCacheMapValue(CacheConstants.UNICOM_PROVINCE_INFO_DICT_KEY, code, dict);
                    count++;
                    log.debug("{} [UNICOM-DATA] 联通省份数据写入成功: code={}, name={}", LOG_PREFIX, code, name);
                } catch (Exception e) {
                    log.error("{} [UNICOM-ERROR] 联通省份数据写入失败, index: {}, 错误: {}", LOG_PREFIX, i, e.getMessage());
                }
            }

            log.info("{} [UNICOM-SUCCESS] 联通运营商省份字典初始化完成, 写入数据: {}, 耗时: {}ms",
                    LOG_PREFIX, count, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("{} [UNICOM-FAIL] 联通运营商省份字典初始化异常: {}", LOG_PREFIX, e.getMessage(), e);
            throw e;
        }
    }
}
