package com.yunqu.crawler.service.impl.telecom;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.base.StringAmountUnitParser;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.util.ParseDateUtil;
import com.yunqu.emergency.common.core.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 青海电信资费数据处理器
 * </p>
 *
 * @ClassName QhTelecomRecordAnalysisHandler
 * <AUTHOR> Copy This Tag)
 * @Description 青海电信资费数据解析处理
 * @Since create in 2025/5/28 14:08
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
public class QhTelecomRecordAnalysisHandler implements IRecordAnalysisHandler {
    @Override
    public List<XtyTariffCrawlRecord> analysis(String content) {
        JSONObject entries = JSONUtil.parseObj(content);
        List<XtyTariffCrawlRecord> results = new ArrayList<>();
        JSONArray jsonArray = entries.getJSONArray("data");
        if(jsonArray==null || jsonArray.isEmpty()) return results;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            if(object==null) continue;
            JSONArray data = object.getJSONArray("data");
            results.addAll(analysisJson(data));
        }
        return results;
    }


    private List<XtyTariffCrawlRecord> analysisJson(JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.size() == 0) {
            return List.of();
        }

        List<XtyTariffCrawlRecord> records = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
            record.setExtendedFields(JSONUtil.toJsonStr(object));
            record.setName(StringUtils.trimToEmpty(object.getStr("offerName")));
            record.setTariffNo(StringUtils.trimToEmpty(object.getStr("regisNumber")));

            String feeStandard = StringUtils.trimToEmpty(object.getStr("feeStandard"));
            if (StringUtils.isNotBlank(feeStandard)) {
                StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(feeStandard);
                if (parse != null) {
                    record.setFees(StringUtils.trimToEmpty(parse.getAmount()));
                    record.setFeesUnit(StringUtils.trimToEmpty(parse.getUnit()));
                }
            }
            record.setCall(StringUtils.trimToEmpty(object.getStr("servVoice")));
            String servTyFlow = StringUtils.trimToEmpty(object.getStr("servTyFlow"));
            if (StringUtils.isNotBlank(servTyFlow)) {
                StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(servTyFlow);
                if (parse != null) {
                    record.setData(StringUtils.trimToEmpty(parse.getAmount()));
                    record.setDataUnit(StringUtils.trimToEmpty(parse.getUnit()));
                }
            }
            record.setSms(StringUtils.trimToEmpty(object.getStr("servSms")));
            String servTargetFlow = StringUtils.trimToEmpty(object.getStr("servTargetFlow"));
            if (StringUtils.isNotBlank(servTargetFlow)) {
                StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(servTargetFlow);
                if (parse != null) {
                    record.setOrientTraffic(StringUtils.trimToEmpty(parse.getAmount()));
                    record.setOrientTrafficUnit(StringUtils.trimToEmpty(parse.getUnit()));
                }
            }
            record.setIptv(StringUtils.trimToEmpty(object.getStr("servIptv")));
            record.setBandwidth(StringUtils.trimToEmpty(object.getStr("servAsdl")));
            record.setRights(StringUtils.trimToEmpty(object.getStr("servRight")));
            record.setOtherContent(StringUtils.trimToEmpty(object.getStr("other_content")));

            String upDownTime = StringUtils.trimToEmpty(object.getStr("upDownTime"));
            if (StringUtils.isNotBlank(upDownTime)) {
                String[] timeArr = StringUtils.split(upDownTime, "-");
                String onlineDay = StringUtils.trimToEmpty(timeArr[0]);
                if (StringUtils.isNotBlank(onlineDay)) {
                    record.setOnlineDay(ParseDateUtil.formatDateStr(onlineDay));
                }
                String offlineDay = StringUtils.trimToEmpty(timeArr[1]);
                if (StringUtils.isNotBlank(offlineDay)) {
                    record.setOfflineDay(ParseDateUtil.formatDateStr(offlineDay));
                }
            }

            record.setApplicablePeople(StringUtils.trimToEmpty(object.getStr("feeScope")));
            record.setValidPeriod(StringUtils.trimToEmpty(object.getStr("effectiveDate")));
            record.setChannel(StringUtils.trimToEmpty(object.getStr("saleChannel")));
            record.setDuration(StringUtils.trimToEmpty(object.getStr("netDuration")));
            record.setUnsubscribe(StringUtils.trimToEmpty(object.getStr("unsubStyle")));
            record.setResponsibility(StringUtils.trimToEmpty(object.getStr("responsibility")));
            record.setOthers(StringUtils.trimToEmpty(object.getStr("others")));
            records.add(record);
        }
        return records;
    }
}
