--- # 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************************************************************************************************************************************
          username: root
          password: yunqu168
        # 从库数据源
        slave:
          lazy: true
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************************************************************************************************************************************
          username: root
          password: yunqu168
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 30000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 多久检查一次连接的活性
        keepaliveTime: 30000

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring.data:
  redis:
    # 地址
    host: ************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 1
    # redis 密码必须配置
    password: yunqu168
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl.enabled: false

# redisson 配置
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${ruoyi.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50


# 日志配置
logging:
  level:
    com.yunqu.crawler: debug
    com.yunqu.crawler.controller: debug
    com.yunqu.crawler.task: debug
    com.yunqu.crawler.server: debug
    com.yunqu.crawler.server.impl: debug
    org.springframework: warn
    org.mybatis.spring.mapper: error
    org.apache.fury: warn
  config: classpath:logback-plus.xml

file:
  # 文件上传路径，默认为当前项目根目录
  tmp-path: D:\work\fileserver

crawler:
  # 爬虫配置
  local-server: http://************:8088
  base-url: http://************:8888
  unicom-req-base-url: https://m.client.10010.com
  run-task-lock-key: excueteCrawlerTask
  operator: telecom,mobile,unicom,broadnet
  overflow-operator:
  server-name: node20
  telecom-provinces-name: 北京
  telecom-provinces: 100
  mobile-provinces-name: 北京
  mobile-provinces: 100
  unicom-provinces-name: 北京
  unicom-provinces: 100
  gdt-provinces-name: 北京
  gdt-provinces: 100

