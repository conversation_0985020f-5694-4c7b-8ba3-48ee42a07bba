package com.yunqu.crawler.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * <p>
 * 资费规则检查工具类
 * </p>
 *
 * @ClassName RuleCheckUtils
 * @Description 资费规则检查工具类，提供各种规则检查方法
 * @Since create in 2024
 * @Version v1.0
 * @Company 广州云趣信息科技有限公司
 */
@Slf4j
public class RuleCheckUtils {

    // 预编译的正则表达式模式缓存
    private static final Map<String, Pattern> REGEX_PATTERN_CACHE = new ConcurrentHashMap<>();

    /**
     * 获取预编译的正则表达式模式
     * @param regex 正则表达式
     * @return 编译后的Pattern对象
     */
    public static Pattern getCompiledPattern(String regex) {
        return REGEX_PATTERN_CACHE.computeIfAbsent(regex, Pattern::compile);
    }

    /**
     * 1. 资费名称重复检查
     * @param tariffId 资费ID
     * @param tariffName 资费名称
     * @param reporter 报告者
     * @return 是否存在问题
     */
    /*public static boolean checkNameDuplicate(String tariffId, String tariffName, String reporter) {
        log.debug("开始检查资费名称重复，资费ID: {}, 资费名称: {}, 报告者: {}", tariffId, tariffName, reporter);

        if (StringUtils.isBlank(tariffName) || StringUtils.isBlank(reporter)) {
            log.debug("资费名称或报告者为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        try {
            // 构建SQL查询，检查是否存在相同NAME和REPORTER的其他记录
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT COUNT(1) FROM ").append(Constants.getBusiSchema()).append(".xty_tariff_record ");
            sqlBuilder.append("WHERE NAME = ? AND REPORTER = ? AND ID != ? AND STATUS = '1'");

            log.debug("执行资费名称重复检查SQL: {}, 参数: [{}, {}, {}]",
                    sqlBuilder.toString(), tariffName, reporter, tariffId);

            // 执行查询
            EasyQuery query = QueryFactory.getWriteQuery();
            int count = query.queryForInt(sqlBuilder.toString(), new Object[]{tariffName, reporter, tariffId});

            log.debug("资费名称重复检查结果，资费ID: {}, 重复数量: {}", tariffId, count);

            // 如果存在相同NAME和REPORTER的其他记录，则返回true表示存在重复
            return count > 0;
        } catch (Exception e) {
            log.error("检查资费名称重复时发生错误: {}, 资费ID: {}", e.getMessage(), tariffId, e);
            // 发生异常时，保守返回false
            return false;
        }
    }*/

    /**
     * 2. 资费标准缺失检查
     * @param tariffId 资费ID
     * @param type2 二级分类
     * @param fees 资费标准
     * @return 是否存在问题
     */
    public static boolean checkFeesMissing(String tariffId, String type2, String fees) {
        log.debug("开始检查资费标准缺失，资费ID: {}, 二级分类: {}, 资费标准: {}", tariffId, type2, fees);

        boolean result = ("1".equals(type2) || "2".equals(type2)) && StringUtils.isBlank(fees);

        if (result) {
            log.debug("资费标准缺失检查结果: 存在问题，资费ID: {}, 二级分类: {}", tariffId, type2);
        } else {
            log.debug("资费标准缺失检查结果: 无问题，资费ID: {}", tariffId);
        }

        return result;
    }

    /**
     * 3. 服务内容缺失检查
     * @param tariffId 资费ID
     * @param contentFields 要检查的字段内容数组
     * @param configFields 配置的字段数组
     * @return 是否存在问题
     */
    public static boolean checkContentMissing(String tariffId, String[] contentFields, String[] configFields) {
        log.debug("开始检查服务内容缺失，资费ID: {}", tariffId);

        // 如果没有配置，使用默认字段
        String[] fields = configFields;
        if (fields == null || fields.length == 0) {
            log.debug("使用默认字段列表检查服务内容缺失，资费ID: {}", tariffId);
            // 默认字段索引: 0=CALL_NUM, 1=DATA_NUM, 2=SMS_NUM, 3=ORIENT_TRAFFIC, 4=IPTV, 5=BANDWIDTH, 6=RIGHTS, 7=OTHER_CONTENT
            return contentFields == null || contentFields.length == 0 ||
                   Arrays.stream(contentFields).allMatch(StringUtils::isBlank);
        } else {
            log.debug("使用配置字段列表检查服务内容缺失，资费ID: {}, 字段列表长度: {}", tariffId, fields.length);
            // 检查所有字段是否都为空
            boolean allEmpty = contentFields == null || contentFields.length == 0 ||
                               contentFields.length < fields.length ||
                               Arrays.stream(contentFields).allMatch(StringUtils::isBlank);

            if (allEmpty) {
                log.debug("服务内容缺失检查结果: 存在问题，资费ID: {}, 所有字段均为空", tariffId);
            } else {
                log.debug("服务内容缺失检查结果: 无问题，资费ID: {}", tariffId);
            }

            return allEmpty;
        }
    }

    /**
     * 4. 适用范围填写不规范检查
     * @param tariffId 资费ID
     * @param applicablePeople 适用范围
     * @param regexPatterns 配置的正则表达式数组
     * @return 是否存在问题
     */
    public static boolean checkApplicablePeopleIrregular(String tariffId, String applicablePeople, String[] regexPatterns) {
        log.debug("开始检查适用范围填写不规范，资费ID: {}", tariffId);

        if (StringUtils.isBlank(applicablePeople)) {
            log.debug("适用范围为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        // 如果没有配置，使用默认正则表达式
        String[] patterns = regexPatterns;
        if (patterns == null || patterns.length == 0) {
            patterns = new String[]{
                "指定.*用户", "指定.*客户", "特定.*用户", "特定.*客户", "维系", "邀约", "受邀", "特邀",
                "打标", "目标客户", "目标用户", "重点用户", "重点客户", "精准营销"
            };
            log.debug("使用默认正则表达式列表检查适用范围，资费ID: {}", tariffId);
        } else {
            log.debug("使用配置正则表达式列表检查适用范围，资费ID: {}, 正则表达式列表长度: {}", tariffId, patterns.length);
        }

        // 使用正则表达式匹配不规范的适用范围描述
        for (String pattern : patterns) {
            try {
                if (getCompiledPattern(pattern).matcher(applicablePeople).find()) {
                    log.debug("适用范围填写不规范，资费ID: {}, 适用范围: {}, 匹配正则: {}", tariffId, applicablePeople, pattern);
                    return true;
                }
            } catch (Exception e) {
                log.error("正则表达式匹配失败: {}, 资费ID: {}, 正则表达式: {}", e.getMessage(), tariffId, pattern, e);
            }
        }

        log.debug("适用范围填写不规范检查结果: 无问题，资费ID: {}", tariffId);
        return false;
    }

    /**
     * 5. 有效期限填写不规范检查
     * @param validPeriod 有效期限
     * @param irregularKeywords 配置的关键词数组
     * @return 是否存在问题
     */
    public static boolean checkValidPeriodIrregular(String validPeriod, String[] irregularKeywords) {
        if (StringUtils.isBlank(validPeriod)) {
            return false;
        }

        // 如果没有配置，使用默认关键词
        String[] keywords = irregularKeywords;
        if (keywords == null || keywords.length == 0) {
            keywords = new String[]{"长期有效", "2099", "不限", "无限"};
        }

        // 检查是否包含不规范关键词
        for (String keyword : keywords) {
            if (validPeriod.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 6. 销售渠道填写不规范检查
     * @param channel 销售渠道
     * @param regexPatterns 配置的正则表达式数组
     * @return 是否存在问题
     */
    public static boolean checkChannelIrregular(String channel, String[] regexPatterns) {
        if (StringUtils.isBlank(channel)) {
            return false;
        }

        // 如果没有配置，使用默认正则表达式
        String[] patterns = regexPatterns;
        if (patterns == null || patterns.length == 0) {
            patterns = new String[]{
                "指定.*渠道", "指定.*受理", "指定.*办理", "指定营业厅", "特定.*渠道", "部分.*渠道"
            };
        }

        // 使用正则表达式匹配不规范的渠道描述
        for (String pattern : patterns) {
            try {
                if (getCompiledPattern(pattern).matcher(channel).find()) {
                    return true;
                }
            } catch (Exception e) {
                // 忽略无效的正则表达式
            }
        }

        return false;
    }

    /**
     * 7. 免费业务附加在网期限检查
     * @param type2 二级分类
     * @param fees 资费标准
     * @param duration 在网要求
     * @param validKeywords 配置的有效关键词数组
     * @return 是否存在问题
     */
    public static boolean checkFreeTariffWithDuration(String type2, String fees, String duration, String[] validKeywords) {
        // 条件1: 二级分类为套餐
        boolean isPackage = "1".equals(type2);

        // 条件2: 资费标准为0元
        boolean isFree = StringUtils.isNotBlank(fees) && "0".equals(fees);

        // 条件3: "在网要求"字段不是以下内容
        boolean hasDuration = true;

        if (StringUtils.isNotBlank(duration)) {
            // 如果没有配置，使用默认关键词
            String[] keywords = validKeywords;
            if (keywords == null || keywords.length == 0) {
                keywords = new String[]{"无", "无要求", "无合约期", "无在网要求", "-", "0", "0个月", "不限制", "不涉及"};
            }

            for (String keyword : keywords) {
                if (duration.equals(keyword)) {
                    hasDuration = false;
                    break;
                }
            }
        }

        return isPackage && isFree && hasDuration;
    }

    /**
     * 8. 退订方式填写不规范检查
     * @param unsubscribe 退订方式
     * @param offlineKeywords 线下渠道关键词数组
     * @param onlineKeywords 线上渠道关键词数组
     * @return 是否存在问题
     */
    public static boolean checkUnsubscribeIrregular(String unsubscribe, String[] offlineKeywords, String[] onlineKeywords) {
        if (StringUtils.isBlank(unsubscribe)) {
            return false;
        }

        // 如果包含"退还"，则跳过检查
        if (unsubscribe.contains("退还")) {
            return false;
        }

        // 如果没有配置线下渠道关键词，使用默认关键词
        String[] offlineWords = offlineKeywords;
        if (offlineWords == null || offlineWords.length == 0) {
            offlineWords = new String[]{"10086", "10010", "10000", "10099", "营业厅", "线下", "门店", "热线", "客服"};
        }

        // 如果没有配置线上渠道关键词，使用默认关键词
        String[] onlineWords = onlineKeywords;
        if (onlineWords == null || onlineWords.length == 0) {
            onlineWords = new String[]{"客户端", "公众号", "小程序", "发送", "app", "APP", "线上", "短信", "手厅", "网厅"};
        }

        // 检查是否包含线下渠道
        boolean hasOffline = false;
        for (String keyword : offlineWords) {
            if (unsubscribe.contains(keyword)) {
                hasOffline = true;
                break;
            }
        }

        // 检查是否包含线上渠道
        boolean hasOnline = false;
        for (String keyword : onlineWords) {
            if (unsubscribe.contains(keyword)) {
                hasOnline = true;
                break;
            }
        }

        // 如果同时不包含线下和线上渠道，则为不规范
        return !hasOffline || !hasOnline;
    }

    /**
     * 9. 违约责任填写不规范检查
     * @param tariffId 资费ID
     * @param responsibility 违约责任
     * @param regexPatterns 配置的正则表达式数组
     * @return 是否存在问题
     */
    public static boolean checkResponsibilityIrregular(String tariffId, String responsibility, String[] regexPatterns) {
        log.debug("开始检查违约责任填写不规范，资费ID: {}", tariffId);

        if (StringUtils.isBlank(responsibility)) {
            log.debug("违约责任为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        // 如果没有配置，使用默认正则表达式
        String[] patterns = regexPatterns;
        if (patterns == null || patterns.length == 0) {
            patterns = new String[]{
                "详询100\\d{2}", // 匹配"详询"后跟100开头的4位数字，如"详询10086"
                "按约定赔付"
            };
            log.debug("使用默认正则表达式列表检查违约责任，资费ID: {}", tariffId);
        } else {
            log.debug("使用配置正则表达式列表检查违约责任，资费ID: {}, 正则表达式列表长度: {}", tariffId, patterns.length);
        }

        // 使用正则表达式匹配不规范的违约责任描述
        for (String pattern : patterns) {
            try {
                if (getCompiledPattern(pattern).matcher(responsibility).find()) {
                    log.debug("违约责任填写不规范，资费ID: {}, 违约责任: {}, 匹配正则: {}", tariffId, responsibility, pattern);
                    return true;
                }
            } catch (Exception e) {
                log.error("正则表达式匹配失败: {}, 资费ID: {}, 正则表达式: {}", e.getMessage(), tariffId, pattern, e);
            }
        }

        log.debug("违约责任填写不规范检查结果: 无问题，资费ID: {}", tariffId);
        return false;
    }

    /**
     * 10. 下线时间填写不规范检查
     * @param offlineDay 下线时间
     * @param irregularKeywords 配置的关键词数组
     * @return 是否存在问题
     */
    public static boolean checkOfflineDayIrregular(String offlineDay, String[] irregularKeywords) {
        if (StringUtils.isBlank(offlineDay)) {
            return true;
        }

        // 如果没有配置，使用默认关键词
        String[] keywords = irregularKeywords;
        if (keywords == null || keywords.length == 0) {
            keywords = new String[]{"2099", "4999"};
        }

        // 检查是否包含不规范关键词
        for (String keyword : keywords) {
            if (offlineDay.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 11. 资费方案内容冗长检查
     * @param contentFields 要检查的内容字段数组
     * @return 是否存在问题
     */
    public static boolean checkContentTooLong(String[] contentFields) {
        // 合并所有内容
        StringBuilder contentBuilder = new StringBuilder();
        if (contentFields != null) {
            for (String content : contentFields) {
                if (StringUtils.isNotBlank(content)) {
                    contentBuilder.append(content);
                }
            }
        }

        String content = contentBuilder.toString();

        // 检查内容长度是否超过500字
        return content.length() > 500;
    }

    /**
     * 12. 资费方案内容重复或矛盾检查
     * @param tariffId 资费ID
     * @param otherContent 服务内容
     * @param others 其他说明
     * @param checkFieldNames 要检查的字段中文名称数组
     * @return 是否存在问题
     */
    public static boolean checkContentConflict(String tariffId, String otherContent, String others, String[] checkFieldNames) {
        log.debug("开始检查资费方案内容重复，资费ID: {}", tariffId);

        // 如果服务内容和其他说明都为空，则不存在问题
        if (StringUtils.isBlank(otherContent) && StringUtils.isBlank(others)) {
            log.debug("服务内容和其他说明均为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        // 合并服务内容和其他说明
        StringBuilder contentBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(otherContent)) contentBuilder.append(otherContent);
        if (StringUtils.isNotBlank(others)) contentBuilder.append(others);

        String content = contentBuilder.toString();

        if (StringUtils.isBlank(content)) {
            log.debug("合并后的内容为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        // 如果没有配置，使用默认字段中文名称
        String[] fieldNames = checkFieldNames;
        if (fieldNames == null || fieldNames.length == 0) {
            fieldNames = new String[]{
                "资费标准", "语音", "短信", "流量", "适用范围", "有效期限",
                "销售渠道", "在网要求", "退订方式", "违约责任"
            };
            log.debug("使用默认字段中文名称列表检查内容重复，资费ID: {}", tariffId);
        } else {
            log.debug("使用配置字段中文名称列表检查内容重复，资费ID: {}, 字段列表长度: {}", tariffId, fieldNames.length);
        }

        // 检查内容中是否包含了指定的字段内容
        for (String fieldName : fieldNames) {
            if (content.contains(fieldName)) {
                log.debug("内容中包含字段名称，资费ID: {}, 字段名称: {}", tariffId, fieldName);
                return true;
            }
        }

        log.debug("资费方案内容重复检查结果: 无问题，资费ID: {}", tariffId);
        return false;
    }

    /**
     * 13. 流量表述不规范检查
     * @param tariffId 资费ID
     * @param fieldValues 要检查的字段值数组
     * @param irregularKeywords 配置的关键词数组
     * @return 是否存在问题
     */
    public static boolean checkTrafficIrregular(String tariffId, String[] fieldValues, String[] irregularKeywords) {
        log.debug("开始检查流量表述不规范，资费ID: {}", tariffId);

        if (fieldValues == null || fieldValues.length == 0) {
            log.debug("字段值为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        // 如果没有配置，使用默认关键词
        String[] keywords = irregularKeywords;
        if (keywords == null || keywords.length == 0) {
            keywords = new String[]{"省内流量", "本地流量", "基站流量", "不限量", "限速"};
            log.debug("使用默认关键词列表检查流量表述，资费ID: {}", tariffId);
        } else {
            log.debug("使用配置关键词列表检查流量表述，资费ID: {}, 关键词列表长度: {}", tariffId, keywords.length);
        }

        // 检查字段值中是否包含不规范的流量表述
        for (String value : fieldValues) {
            if (StringUtils.isNotBlank(value)) {
                for (String keyword : keywords) {
                    if (value.contains(keyword)) {
                        log.debug("发现不规范流量表述，资费ID: {}, 关键词: {}", tariffId, keyword);
                        return true;
                    }
                }
            }
        }

        log.debug("流量表述不规范检查结果: 无问题，资费ID: {}", tariffId);
        return false;
    }

    /**
     * 14. 加装包附加限制性条件检查
     * @param type2 二级分类
     * @param duration 在网要求
     * @param validKeywords 配置的有效关键词数组
     * @return 是否存在问题
     */
    public static boolean checkAddonWithRestriction(String type2, String duration, String[] validKeywords) {
        // 条件1: 二级分类为加装包
        boolean isAddon = "2".equals(type2);

        if (!isAddon) {
            return false;
        }

        // 条件2: "在网要求"字段不是以下内容
        boolean hasRestriction = true;

        if (StringUtils.isNotBlank(duration)) {
            // 如果没有配置，使用默认关键词
            String[] keywords = validKeywords;
            if (keywords == null || keywords.length == 0) {
                keywords = new String[]{"无", "无要求", "无合约期", "无在网要求", "-", "0", "0个月", "不限制", "不涉及"};
            }

            for (String keyword : keywords) {
                if (duration.equals(keyword)) {
                    hasRestriction = false;
                    break;
                }
            }
        }

        return isAddon && hasRestriction;
    }

    /**
     * 15. 营销活动附加限制性条件检查
     * @param type2 二级分类
     * @param duration 在网要求
     * @param validKeywords 配置的有效关键词数组
     * @return 是否存在问题
     */
    public static boolean checkMarketingWithRestriction(String type2, String duration, String[] validKeywords) {
        // 条件1: 二级分类为营销活动
        boolean isMarketing = "3".equals(type2);

        if (!isMarketing) {
            return false;
        }

        // 条件2: "在网要求"字段不是以下内容
        boolean hasRestriction = true;

        if (StringUtils.isNotBlank(duration)) {
            // 如果没有配置，使用默认关键词
            String[] keywords = validKeywords;
            if (keywords == null || keywords.length == 0) {
                keywords = new String[]{"无", "无要求", "无合约期", "无在网要求", "-", "0", "0个月", "不限制", "不涉及"};
            }

            for (String keyword : keywords) {
                if (duration.equals(keyword)) {
                    hasRestriction = false;
                    break;
                }
            }
        }

        return isMarketing && hasRestriction;
    }
}
