package com.yunqu.crawler.service.impl.telecom;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.util.ParseDateUtil;
import com.yunqu.emergency.common.core.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 福建电信流量包解析器
 */
public class FjTelecomRecordfAnalysisHandler implements IRecordAnalysisHandler {

    /**
     * 解析文件内容，获取资费信息
     *
     * @param content
     * @return
     */
    @Override
    public List<XtyTariffCrawlRecord> analysis(String content) {
        JSONObject entries = JSONUtil.parseObj(content);
        JSONArray jsonArray = entries.getJSONArray("body");
        if (jsonArray == null || jsonArray.isEmpty()) return List.of();
        List<XtyTariffCrawlRecord> ressult = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            String tariffType = StringUtils.trimToEmpty(object.getStr("name"));
            JSONArray tariffbean = object.getJSONArray("tariffbean2");
            if (tariffbean == null || tariffbean.isEmpty()) continue;
            for (int i1 = 0; i1 < tariffbean.size(); i1++) {
                JSONObject object1 = tariffbean.getJSONObject(i1);
                JSONArray jsonArray1 = object1.getJSONArray("offerBeans");
                if (jsonArray1 == null || jsonArray1.isEmpty()) continue;
                ressult.addAll(analysisJson(jsonArray1,tariffType));
            }
        }
        return ressult;
    }

    /**
     * 解析json数据
     *
     * @param jsonArray
     * @return
     */
    private List<XtyTariffCrawlRecord> analysisJson(JSONArray jsonArray, String tariffType) {
        if (jsonArray == null || jsonArray.size() == 0) {
            return List.of();
        }

        List<XtyTariffCrawlRecord> records = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
            record.setExtendedFields(JSONUtil.toJsonStr(object));
            record.setName(StringUtils.trimToEmpty(object.getStr("name")));
            record.setTariffNo(StringUtils.trimToEmpty(object.getStr("offer_report_no")));
            record.setFees(StringUtils.trimToEmpty(object.getStr("fees")));
            record.setFeesUnit(StringUtils.trimToEmpty(object.getStr("fees_unit")));
            record.setCall(StringUtils.trimToEmpty(object.getStr("call")));
            record.setData(StringUtils.trimToEmpty(object.getStr("data")));
            record.setDataUnit(StringUtils.trimToEmpty(object.getStr("data_unit")));
            record.setSms(StringUtils.trimToEmpty(object.getStr("sms")));
            record.setOrientTraffic(StringUtils.trimToEmpty(object.getStr("orient_traffic")));
            record.setOrientTrafficUnit(StringUtils.trimToEmpty(object.getStr("orient_traffic_unit")));
            record.setIptv(StringUtils.trimToEmpty(object.getStr("iptv")));
            record.setBandwidth(StringUtils.trimToEmpty(object.getStr("band_width")));
            record.setRights(StringUtils.trimToEmpty(object.getStr("rights")));
            record.setOtherContent(StringUtils.trimToEmpty(object.getStr("other_content")));
            String onlineDay = StringUtils.trimToEmpty(object.getStr("online_day"));
            record.setOnlineDay(ParseDateUtil.formatDateStr(onlineDay));
            String offlineDay = StringUtils.trimToEmpty(object.getStr("offline_day"));
            record.setOfflineDay(ParseDateUtil.formatDateStr(offlineDay));
            record.setApplicablePeople(StringUtils.trimToEmpty(object.getStr("applicable_people")));
            record.setValidPeriod(StringUtils.trimToEmpty(object.getStr("valid_period")));
            record.setChannel(StringUtils.trimToEmpty(object.getStr("channel")));
            record.setDuration(StringUtils.trimToEmpty(object.getStr("duration")));
            record.setUnsubscribe(StringUtils.trimToEmpty(object.getStr("unsubscribe")));
            record.setResponsibility(StringUtils.trimToEmpty(object.getStr("responsibility")));
            record.setOthers(StringUtils.trimToEmpty(object.getStr("others")));
            record.setTariffType(tariffType);
            records.add(record);
        }

        return records;
    }

}
