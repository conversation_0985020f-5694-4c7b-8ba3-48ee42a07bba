package com.yunqu.crawler.enums;

import com.yunqu.crawler.base.Constants;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运营商枚举
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum OperatorEnum {

    /**
     * 中国电信
     */
    TELECOM("1", "电信", Constants.OPERATOR_TELECOM),

    /**
     * 中国移动
     */
    MOBILE("2", "移动", Constants.OPERATOR_MOBILE),

    /**
     * 中国联通
     */
    UNICOM("3", "联通", Constants.OPERATOR_UNICOM),

    /**
     * 中国广电
     */
    GDT("5", "广电", Constants.OPERATOR_GDT);

    /**
     * 运营商代码
     */
    private String code;


    /**
     * 运营商名称
     */
    private final String name;

    /**
     * 运营商代码
     */
    private final String alias;

    /**
     * 根据代码获取运营商枚举
     *
     * @param code 运营商代码
     * @return 运营商枚举
     */
    public static OperatorEnum getByCode(String code) {
        for (OperatorEnum operator : values()) {
            if (operator.getCode().equals(code)) {
                return operator;
            }
        }
        throw new IllegalArgumentException("未知的运营商代码: " + code);
    }

    /**
     * 根据名称获取运营商枚举
     *
     * @param name 运营商名称
     * @return 运营商枚举
     */
    public static OperatorEnum getByName(String name) {
        for (OperatorEnum operator : values()) {
            if (operator.getName().equals(name)) {
                return operator;
            }
        }
        throw new IllegalArgumentException("未知的运营商名称: " + name);
    }
}
