# Elasticsearch 版本号管理操作指南

## 概述

本文档提供了通过HTTP API操作Elasticsearch，实现版本号数组管理的完整解决方案。主要功能包括：

1. 删除`version_nos`数组中的指定版本号
2. 更新`version_no`字段为剩余版本号中的最大值
3. 同步删除`date_ids`数组中对应的数据
4. 当`version_nos`仅有一个值且为待删除值时，删除整条记录

## 核心操作方案

### 方案一：Update By Query API（推荐）

#### 1. 查询待处理数据

```bash
# 查询包含特定版本号的文档
POST /your_index/_search
{
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "version_nos": ["V20250614_1"]
          }
        }
      ]
    }
  },
  "size": 10
}
```

#### 2. 删除整条记录（当version_nos只有一个待删除的值）

```bash
# 删除version_nos数组只包含待删除版本号的文档
POST /your_index/_delete_by_query
{
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "version_nos": ["V20250614_1"]
          }
        },
        {
          "script": {
            "source": "doc['version_nos'].size() == 1"
          }
        }
      ]
    }
  }
}
```

#### 3. 更新版本号数组和相关字段

```bash
# 更新包含多个版本号的文档
POST /your_index/_update_by_query
{
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "version_nos": ["V20250614_1"]
          }
        },
        {
          "script": {
            "source": "doc['version_nos'].size() > 1"
          }
        }
      ]
    }
  },
  "script": {
    "source": """
      // 要删除的版本号
      String targetVersion = params.target_version;
      String targetDateId = params.target_date_id;
      
      // 删除version_nos中的指定版本号
      if (ctx._source.version_nos != null) {
        ctx._source.version_nos.removeIf(version -> version.equals(targetVersion));
        
        // 找到最大的版本号并更新version_no字段
        if (!ctx._source.version_nos.isEmpty()) {
          String maxVersion = ctx._source.version_nos.stream()
            .max(Comparator.naturalOrder())
            .orElse(null);
          if (maxVersion != null) {
            ctx._source.version_no = maxVersion;
          }
        }
      }
      
      // 删除date_ids中对应的日期ID
      if (ctx._source.date_ids != null && targetDateId != null) {
        ctx._source.date_ids.removeIf(dateId -> dateId.equals(targetDateId));
      }
    """,
    "params": {
      "target_version": "V20250614_1",
      "target_date_id": "20250614"
    }
  }
}
```

### 方案二：批量操作（适用于数据量较小的场景）

#### 1. 获取需要更新的文档ID

```bash
POST /your_index/_search
{
  "query": {
    "terms": {
      "version_nos": ["V20250614_1"]
    }
  },
  "_source": ["version_nos", "date_ids", "version_no"],
  "size": 1000
}
```

#### 2. 批量更新操作

```bash
POST /your_index/_bulk
{ "update": { "_id": "document_id_1" } }
{ "script": { "source": "ctx._source.version_nos.removeIf(v -> v.equals(params.target)); if (!ctx._source.version_nos.isEmpty()) { ctx._source.version_no = ctx._source.version_nos.stream().max(Comparator.naturalOrder()).orElse(null); } ctx._source.date_ids.removeIf(d -> d.equals(params.date_id));", "params": { "target": "V20250614_1", "date_id": "20250614" } } }
{ "update": { "_id": "document_id_2" } }
{ "script": { "source": "ctx._source.version_nos.removeIf(v -> v.equals(params.target)); if (!ctx._source.version_nos.isEmpty()) { ctx._source.version_no = ctx._source.version_nos.stream().max(Comparator.naturalOrder()).orElse(null); } ctx._source.date_ids.removeIf(d -> d.equals(params.date_id));", "params": { "target": "V20250614_1", "date_id": "20250614" } } }
```

## 完整操作流程

### 步骤1：数据备份（强烈推荐）

```bash
# 创建索引快照
PUT /_snapshot/backup_repo/version_cleanup_backup
{
  "indices": "your_index",
  "ignore_unavailable": true,
  "include_global_state": false
}
```

### 步骤2：验证待处理数据

```bash
# 统计包含目标版本号的文档数量
POST /your_index/_count
{
  "query": {
    "terms": {
      "version_nos": ["V20250614_1"]
    }
  }
}
```

### 步骤3：执行删除操作

```bash
# 先删除只有单个版本号的文档
POST /your_index/_delete_by_query
{
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "version_nos": ["V20250614_1"]
          }
        },
        {
          "script": {
            "source": "doc['version_nos'].size() == 1"
          }
        }
      ]
    }
  }
}
```

### 步骤4：更新多版本号文档

```bash
# 更新包含多个版本号的文档
POST /your_index/_update_by_query
{
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "version_nos": ["V20250614_1"]
          }
        },
        {
          "script": {
            "source": "doc['version_nos'].size() > 1"
          }
        }
      ]
    }
  },
  "script": {
    "source": """
      String targetVersion = params.target_version;
      String targetDateId = params.target_date_id;
      
      // 删除指定版本号
      ctx._source.version_nos.removeIf(version -> version.equals(targetVersion));
      
      // 更新version_no为最大版本号
      if (!ctx._source.version_nos.isEmpty()) {
        String maxVersion = ctx._source.version_nos.stream()
          .max(Comparator.naturalOrder())
          .orElse(null);
        if (maxVersion != null) {
          ctx._source.version_no = maxVersion;
        }
      }
      
      // 删除对应的date_id
      if (ctx._source.date_ids != null && targetDateId != null) {
        ctx._source.date_ids.removeIf(dateId -> dateId.equals(targetDateId));
      }
    """,
    "params": {
      "target_version": "V20250614_1",
      "target_date_id": "20250614"
    }
  }
}
```

### 步骤5：验证操作结果

```bash
# 验证目标版本号已被删除
POST /your_index/_search
{
  "query": {
    "terms": {
      "version_nos": ["V20250614_1"]
    }
  },
  "size": 0
}

# 检查version_no字段是否正确更新
POST /your_index/_search
{
  "query": {
    "bool": {
      "must_not": [
        {
          "term": {
            "version_no": "V20250614_1"
          }
        }
      ]
    }
  },
  "size": 10
}
```

## 高级脚本示例

### 智能版本号比较脚本

```javascript
// 用于比较版本号大小的Painless脚本
String compareVersions(String v1, String v2) {
  // 提取日期部分进行比较 (V20250614_1 -> 20250614)
  String date1 = v1.substring(1, 9);
  String date2 = v2.substring(1, 9);
  
  int dateCompare = date1.compareTo(date2);
  if (dateCompare != 0) {
    return dateCompare > 0 ? v1 : v2;
  }
  
  // 日期相同时比较序号
  String seq1 = v1.substring(10);
  String seq2 = v2.substring(10);
  
  return Integer.parseInt(seq1) > Integer.parseInt(seq2) ? v1 : v2;
}
```

### 批量处理多个版本号

```bash
POST /your_index/_update_by_query
{
  "query": {
    "terms": {
      "version_nos": ["V20250614_1", "V20250621_1"]
    }
  },
  "script": {
    "source": """
      List<String> targetVersions = params.target_versions;
      List<String> targetDateIds = params.target_date_ids;
      
      // 删除多个版本号
      for (String targetVersion : targetVersions) {
        ctx._source.version_nos.removeIf(version -> version.equals(targetVersion));
      }
      
      // 删除对应的日期ID
      for (String targetDateId : targetDateIds) {
        if (ctx._source.date_ids != null) {
          ctx._source.date_ids.removeIf(dateId -> dateId.equals(targetDateId));
        }
      }
      
      // 更新version_no为最大版本号
      if (!ctx._source.version_nos.isEmpty()) {
        String maxVersion = ctx._source.version_nos.stream()
          .max(Comparator.naturalOrder())
          .orElse(null);
        if (maxVersion != null) {
          ctx._source.version_no = maxVersion;
        }
      } else {
        // 如果没有剩余版本号，标记删除
        ctx.op = 'delete';
      }
    """,
    "params": {
      "target_versions": ["V20250614_1", "V20250621_1"],
      "target_date_ids": ["20250614", "20250621"]
    }
  }
}
```

## 性能优化建议

### 1. 分批处理大量数据

```bash
# 使用scroll API分批处理
POST /your_index/_update_by_query?scroll_size=1000&wait_for_completion=false
{
  "query": {
    "terms": {
      "version_nos": ["V20250614_1"]
    }
  },
  "script": {
    // ... 脚本内容
  }
}
```

### 2. 异步执行

```bash
# 异步执行更新操作
POST /your_index/_update_by_query?wait_for_completion=false
{
  // ... 查询和脚本内容
}

# 检查任务状态
GET /_tasks?detailed=true&actions=*update/byquery
```

### 3. 设置合适的刷新间隔

```bash
# 临时禁用刷新以提高性能
PUT /your_index/_settings
{
  "refresh_interval": "-1"
}

# 操作完成后恢复刷新
PUT /your_index/_settings
{
  "refresh_interval": "1s"
}
```

## 错误处理和回滚

### 回滚操作

```bash
# 从快照恢复数据
POST /_snapshot/backup_repo/version_cleanup_backup/_restore
{
  "indices": "your_index",
  "ignore_unavailable": true,
  "include_global_state": false
}
```

### 常见错误处理

1. **脚本执行错误**：检查Painless脚本语法
2. **内存不足**：减少batch_size或使用scroll
3. **版本冲突**：使用version_type=internal

## 安全注意事项

1. **备份数据**：执行任何批量操作前必须备份
2. **测试环境验证**：先在测试环境验证脚本
3. **分批执行**：避免一次性处理过多数据
4. **监控性能**：关注集群性能指标
5. **权限控制**：确保操作账户有足够权限

## 监控和日志

```bash
# 查看更新操作的详细信息
GET /_tasks?detailed=true&actions=*update/byquery

# 查看集群健康状态
GET /_cluster/health

# 查看索引统计信息
GET /your_index/_stats
```

## 实际使用示例

### 示例1：删除版本号 V20250614_1

假设你要删除版本号 `V20250614_1` 和对应的日期ID `20250614`：

```bash
# 1. 先查看影响的文档数量
POST /your_index/_count
{
  "query": {
    "terms": {
      "version_nos": ["V20250614_1"]
    }
  }
}

# 2. 删除只有单个版本号的文档
POST /your_index/_delete_by_query
{
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "version_nos": ["V20250614_1"]
          }
        },
        {
          "script": {
            "source": "doc['version_nos'].size() == 1"
          }
        }
      ]
    }
  }
}

# 3. 更新多版本号的文档
POST /your_index/_update_by_query
{
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "version_nos": ["V20250614_1"]
          }
        },
        {
          "script": {
            "source": "doc['version_nos'].size() > 1"
          }
        }
      ]
    }
  },
  "script": {
    "source": """
      ctx._source.version_nos.removeIf(v -> v.equals('V20250614_1'));
      ctx._source.date_ids.removeIf(d -> d.equals('20250614'));

      if (!ctx._source.version_nos.isEmpty()) {
        ctx._source.version_no = ctx._source.version_nos.stream()
          .max(Comparator.naturalOrder())
          .orElse(null);
      }
    """
  }
}
```

### 示例2：基于你提供的数据结构

根据你提供的JSON数据，完整的操作示例：

```bash
# 删除 V20250614_1 版本号
POST /your_index/_update_by_query
{
  "query": {
    "bool": {
      "must": [
        {
          "term": {
            "id": "82467157258244625635124"
          }
        }
      ]
    }
  },
  "script": {
    "source": """
      // 删除指定版本号
      ctx._source.version_nos.removeIf(v -> v.equals('V20250614_1'));

      // 删除对应的日期ID
      ctx._source.date_ids.removeIf(d -> d.equals('20250614'));

      // 如果没有剩余版本号，删除整条记录
      if (ctx._source.version_nos.isEmpty()) {
        ctx.op = 'delete';
      } else {
        // 更新version_no为最大版本号
        ctx._source.version_no = ctx._source.version_nos.stream()
          .max(Comparator.naturalOrder())
          .orElse(null);
      }
    """
  }
}
```

## 注意事项

1. **索引名称**：将 `your_index` 替换为实际的索引名称
2. **字段映射**：确保 `version_nos` 和 `date_ids` 字段类型为数组
3. **版本号格式**：脚本假设版本号格式为 `V{YYYYMMDD}_{序号}`
4. **性能考虑**：大量数据操作时建议分批执行
5. **数据备份**：执行前务必备份重要数据

## 故障排除

### 常见问题

1. **脚本编译错误**：检查Painless语法
2. **字段不存在**：确认字段名称和类型
3. **权限不足**：检查用户权限设置
4. **内存溢出**：减少批处理大小

### 调试技巧

```bash
# 使用 _explain API 调试查询
POST /your_index/_explain/document_id
{
  "query": {
    "terms": {
      "version_nos": ["V20250614_1"]
    }
  }
}

# 测试脚本执行
POST /your_index/_update/document_id
{
  "script": {
    "source": "ctx._source.test_field = 'script_executed'"
  }
}
```
