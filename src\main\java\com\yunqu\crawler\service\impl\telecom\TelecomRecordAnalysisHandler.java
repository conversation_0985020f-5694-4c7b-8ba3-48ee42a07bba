package com.yunqu.crawler.service.impl.telecom;

import java.util.List;

import com.yunqu.crawler.domain.XtyTariffCrawlRecord;

/**
 * <p>
 *
 * </p>
 *
 * @ClassName TelecomRecordAnalysisHandler
 * <AUTHOR> Copy This Tag)
 * @Description TODO 描述文件用途
 * @Since create in 2025/5/28 11:27
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
public class TelecomRecordAnalysisHandler {

    private static TelecomRecordAnalysisHandler instance;

    private TelecomRecordAnalysisHandler() {
    }

    public static TelecomRecordAnalysisHandler getInstance() {
        if (instance == null) {
            synchronized (TelecomRecordAnalysisHandler.class) {
                if (instance == null) {
                    instance = new TelecomRecordAnalysisHandler();
                }
            }
        }
        return instance;
    }

    public List<XtyTariffCrawlRecord> analysisCrawlerData(String content, String fileName, String provinceCode) {

        IRecordAnalysisHandler analysisHandler = getAnalysisHandler(provinceCode);
        if (analysisHandler != null)
            return analysisHandler.analysis(content);
        return List.of();
    }

    private IRecordAnalysisHandler getAnalysisHandler(String provinceCode) {
        return switch (provinceCode) {
            case "bj" ->
                // 北京
                new BjTelecomRecordAnalysisHandler();
            case "ah" ->
                // 安徽
                new AhTelecomRecordAnalysisHandler();
            case "cq" ->
                // 重庆
                new CqTelecomRecordAnalysisHandler();
            case "fj" ->
                // 福建
                new FjTelecomRecordfAnalysisHandler();
            case "gd" ->
                // 广东
                new GdTelecomRecordAnalysisHandler();
            case "gs" ->
                // 甘肃
                new GsTelecomRecordAnalysisHandler();
            case "gx" ->
                // 广西
                new GxTelecomRecordAnalysisHandler();
            case "gz" ->
                // 贵州
                new GzTelecomRecordAnalysisHandler();
            case "hb" ->
                // 湖北
                new HbTelecomRecordAnalysisHandler();
            case "hn" ->
                // 湖南
                new HnTelecomRecordAnalysisHandler();
            case "he" ->
                // 河北
                new HeTelecomRecordAnalysisHandler();
            case "ha" ->
                // 河南
                new HaTelecomRecordAnalysisHandler();
            case "hi" ->
                // 海南
                new HiTelecomRecordAnalysisHandler();
            case "hl" ->
                // 黑龙江
                new HlTelecomRecordAnalysisHandler();
            case "js" ->
                // 江苏
                new JsTelecomRecordAnalysisHandler();
            case "jx" ->
                // 江西
                new JxTelecomRecordAnalysisHandler();
            case "jl" ->
                // 江西
                new JlTelecomRecordAnalysisHandler();
            case "ln" ->
                // 辽宁
                new LnTelecomRecordAnalysisHandler();
            case "nm" ->
                // 内蒙古
                new NmTelecomRecordAnalysisHandler();
            case "nx" ->
                // 宁夏
                new NxTelecomRecordAnalysisHandler();
            case "qh" ->
                // 青海
                new QhTelecomRecordAnalysisHandler();
            case "sc" ->
                // 四川
                new ScTelecomRecordAnalysisHandler();
            case "sh" ->
                // 上海
                new ShTelecomRecordAnalysisHandler();
            case "sx" ->
                // 山西
                new SxTelecomRecordAnalysisHandler();
            case "sd" ->
                // 山东
                new SdTelecomRecordAnalysisHandler();
            case "sn" ->
                // 陕西
                new SnTelecomRecordAnalysisHandler();
            case "tj" ->
                // 天津
                new TjTelecomRecordAnalysisHandler();
            case "xj" ->
                // 新疆
                new XjTelecomRecordAnalysisHandler();
            case "xz" ->
                // 西藏
                new XzTelecomRecordAnalysisHandler();
            case "yn" ->
                // 云南
                new YnTelecomRecordAnalysisHandler();
            case "zj" ->
                // 浙江
                new ZjTelecomRecordAnalysisHandler();
            default -> null;
        };
    }

}
