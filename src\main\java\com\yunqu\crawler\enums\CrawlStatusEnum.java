package com.yunqu.crawler.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 爬取状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum CrawlStatusEnum {

    /**
     * 待处理
     */
    PENDING("PENDING", "待处理"),

    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成");

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据代码获取状态枚举
     *
     * @param code 状态代码
     * @return 状态枚举
     */
    public static CrawlStatusEnum getByCode(String code) {
        for (CrawlStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的爬取状态代码: " + code);
    }

    /**
     * 判断是否为指定状态
     *
     * @param code 状态代码
     * @return 是否匹配
     */
    public boolean is(String code) {
        return this.code.equals(code);
    }
}