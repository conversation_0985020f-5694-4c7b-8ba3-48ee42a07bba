package com.yunqu.crawler.domain.bo;

import com.yunqu.crawler.domain.XtyTariffUnicomRecord;
import com.yunqu.emergency.common.core.validate.EditGroup;
import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 信通院-中国联通公示资费数据业务对象 xty_tariff_unicom_record
 *
 * <AUTHOR>
 * @date 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = XtyTariffUnicomRecord.class, reverseConvertGenerate = false)
public class XtyTariffUnicomRecordBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 资费名称
     */
    private String tariffName;

    /**
     * 资费方案编号
     */
    private String tariffNo;

    /**
     * 资费标准
     */
    private String tariffFee;

    /**
     * 服务内容
     */
    private String serviceContent;

    /**
     * 适用范围
     */
    private String scope;

    /**
     * 有效期限
     */
    private String validPeriod;

    /**
     * 销售渠道
     */
    private String tariffChannel;

    /**
     * 上线日期
     */
    private String onlineDate;

    /**
     * 下线日期
     */
    private String offlineDate;

    /**
     * 在网要求
     */
    private String onlineRequirement;

    /**
     * 退订方式
     */
    private String unsubscribeMethod;

    /**
     * 违约责任
     */
    private String breachResponsibility;

    /**
     * 其他说明
     */
    private String others;

    /**
     * 活动收费标准
     */
    private String activityFeeStandard;

    /**
     * 活动内容及规则
     */
    private String activityContent;

    /**
     * 活动范围
     */
    private String activityScope;

    /**
     * 国内语音拨打分钟数
     */
    private String domesticVoiceCallMin;

    /**
     * 国内流量
     */
    private String domesticTraffic;

    /**
     * 增值业务
     */
    private String valueAddedService;

    /**
     * 权益
     */
    private String rightsInterests;

    /**
     * 定向流量
     */
    private String targetedTraffic;


}
