package com.yunqu.crawler.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.classify.Classifier;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

/**
 * HTTP请求重试异常分类器
 * 用于判断哪些异常应该进行重试，哪些不应该重试
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Component
public class HttpRetryClassifier implements Classifier<Throwable, Boolean> {

    /**
     * 分类异常是否可重试
     * 
     * @param throwable 异常对象
     * @return true表示可重试，false表示不可重试
     */
    @Override
    public Boolean classify(Throwable throwable) {
        if (throwable == null) {
            return false;
        }
        
        // 网络相关异常 - 可重试
        if (isNetworkException(throwable)) {
            log.debug("[RETRY-CLASSIFIER] 网络异常可重试: {}", throwable.getClass().getSimpleName());
            return true;
        }
        
        // 业务逻辑异常 - 不可重试
        if (isBusinessException(throwable)) {
            log.debug("[RETRY-CLASSIFIER] 业务异常不可重试: {}", throwable.getClass().getSimpleName());
            return false;
        }
        
        // 其他运行时异常 - 根据具体情况判断
        if (throwable instanceof RuntimeException) {
            String message = throwable.getMessage();
            if (message != null) {
                // 包含网络相关关键词的运行时异常可重试
                if (containsNetworkKeywords(message)) {
                    log.debug("[RETRY-CLASSIFIER] 包含网络关键词的运行时异常可重试: {}", message);
                    return true;
                }
            }
        }
        
        // 默认不重试
        log.debug("[RETRY-CLASSIFIER] 默认不重试异常: {}", throwable.getClass().getSimpleName());
        return false;
    }
    
    /**
     * 判断是否为网络相关异常
     */
    private boolean isNetworkException(Throwable throwable) {
        return throwable instanceof IOException ||
               throwable instanceof SocketTimeoutException ||
               throwable instanceof ConnectException ||
               throwable instanceof UnknownHostException ||
               throwable.getClass().getSimpleName().contains("Timeout") ||
               throwable.getClass().getSimpleName().contains("Connection");
    }
    
    /**
     * 判断是否为业务逻辑异常
     */
    private boolean isBusinessException(Throwable throwable) {
        return throwable instanceof IllegalArgumentException ||
               throwable instanceof IllegalStateException ||
               throwable instanceof NullPointerException ||
               throwable.getClass().getSimpleName().contains("Business") ||
               throwable.getClass().getSimpleName().contains("Service");
    }
    
    /**
     * 判断异常消息是否包含网络相关关键词
     */
    private boolean containsNetworkKeywords(String message) {
        if (message == null) {
            return false;
        }
        
        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("timeout") ||
               lowerMessage.contains("connection") ||
               lowerMessage.contains("network") ||
               lowerMessage.contains("socket") ||
               lowerMessage.contains("host") ||
               lowerMessage.contains("refused") ||
               lowerMessage.contains("reset");
    }
}
