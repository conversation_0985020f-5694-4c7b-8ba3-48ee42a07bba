package com.yunqu.crawler.mapper;

import com.yunqu.crawler.domain.XtyCrawlerVersionConfig;
import com.yunqu.crawler.domain.vo.XtyCrawlerVersionConfigVo;
import com.yunqu.emergency.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Mapper;

/**
 * 爬虫版本生成配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Mapper
public interface XtyCrawlerVersionConfigMapper extends BaseMapperPlus<XtyCrawlerVersionConfig, XtyCrawlerVersionConfigVo> {

}
