<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.yunqu.crawler</groupId>
  <artifactId>xty-crawler-service</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <name>xty-crawler-service</name>
  <description>xty-crawler-service</description>
  <properties>
    <spring-boot-admin.version>3.4.1</spring-boot-admin.version>
    <dynamic-ds.version>4.3.1</dynamic-ds.version>
    <easyexcel.version>4.0.3</easyexcel.version>
    <aws.sdk.version>2.28.22</aws.sdk.version>
    <aws.crt.version>0.31.3</aws.crt.version>
    <hutool.version>5.8.35</hutool.version>
    <spring-boot.version>3.3.9</spring-boot.version>
    <p6spy.version>3.9.1</p6spy.version>
    <flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>
    <snailjob.version>1.3.0</snailjob.version>
    <springdoc.version>2.8.3</springdoc.version>
    <mapstruct-plus.version>1.4.6</mapstruct-plus.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <fastjson.version>1.2.83</fastjson.version>
    <anyline.version>8.7.2-20250101</anyline.version>
    <therapi-javadoc.version>0.15.0</therapi-javadoc.version>
    <lock4j.version>2.2.7</lock4j.version>
    <warm-flow.version>1.6.6</warm-flow.version>
    <sms4j.version>3.3.3</sms4j.version>
    <mybatis.version>3.5.16</mybatis.version>
    <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
    <java.version>21</java.version>
    <bouncycastle.version>1.76</bouncycastle.version>
    <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
    <mybatis-plus.version>3.5.10</mybatis-plus.version>
    <knife4j.version>4.5.0</knife4j.version>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <revision>5.3.0</revision>
    <lombok.version>1.18.36</lombok.version>
    <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
    <mapstruct-plus.lombok.version>0.2.0</mapstruct-plus.lombok.version>
    <ip2region.version>2.7.0</ip2region.version>
    <satoken.version>1.39.0</satoken.version>
    <redisson.version>3.43.0</redisson.version>
    <justauth.version>1.16.7</justauth.version>
    <maven-war-plugin.version>3.2.2</maven-war-plugin.version>
    <velocity.version>2.3</velocity.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-bom</artifactId>
        <version>${hutool.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.dromara.warm</groupId>
        <artifactId>warm-flow-mybatis-plus-sb3-starter</artifactId>
        <version>${warm-flow.version}</version>
      </dependency>
      <dependency>
        <groupId>org.dromara.warm</groupId>
        <artifactId>warm-flow-plugin-ui-sb-web</artifactId>
        <version>${warm-flow.version}</version>
      </dependency>
      <dependency>
        <groupId>me.zhyd.oauth</groupId>
        <artifactId>JustAuth</artifactId>
        <version>${justauth.version}</version>
      </dependency>
      <dependency>
        <groupId>com.yunqu.emergency</groupId>
        <artifactId>de-common-bom</artifactId>
        <version>5.3.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.github.therapi</groupId>
        <artifactId>therapi-runtime-javadoc</artifactId>
        <version>${therapi-javadoc.version}</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>easyexcel</artifactId>
        <version>${easyexcel.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity-engine-core</artifactId>
        <version>${velocity.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-spring-boot3-starter</artifactId>
        <version>${satoken.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-jwt</artifactId>
        <version>${satoken.version}</version>
        <exclusions>
          <exclusion>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-core</artifactId>
        <version>${satoken.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
        <version>${dynamic-ds.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis</artifactId>
        <version>${mybatis.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-jsqlparser</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-annotation</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>p6spy</groupId>
        <artifactId>p6spy</artifactId>
        <version>${p6spy.version}</version>
      </dependency>
      <dependency>
        <groupId>software.amazon.awssdk</groupId>
        <artifactId>s3</artifactId>
        <version>${aws.sdk.version}</version>
      </dependency>
      <dependency>
        <groupId>software.amazon.awssdk.crt</groupId>
        <artifactId>aws-crt</artifactId>
        <version>${aws.crt.version}</version>
      </dependency>
      <dependency>
        <groupId>software.amazon.awssdk</groupId>
        <artifactId>s3-transfer-manager</artifactId>
        <version>${aws.sdk.version}</version>
      </dependency>
      <dependency>
        <groupId>org.dromara.sms4j</groupId>
        <artifactId>sms4j-spring-boot-starter</artifactId>
        <version>${sms4j.version}</version>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-server</artifactId>
        <version>${spring-boot-admin.version}</version>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-client</artifactId>
        <version>${spring-boot-admin.version}</version>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson-spring-boot-starter</artifactId>
        <version>${redisson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
        <version>${lock4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aizuda</groupId>
        <artifactId>snail-job-client-starter</artifactId>
        <version>${snailjob.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aizuda</groupId>
        <artifactId>snail-job-client-job-core</artifactId>
        <version>${snailjob.version}</version>
      </dependency>
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk15to18</artifactId>
        <version>${bouncycastle.version}</version>
      </dependency>
      <dependency>
        <groupId>io.github.linpeilie</groupId>
        <artifactId>mapstruct-plus-spring-boot-starter</artifactId>
        <version>${mapstruct-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>org.lionsoul</groupId>
        <artifactId>ip2region</artifactId>
        <version>${ip2region.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>2.15.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        <version>${knife4j.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
    </dependency>
    <dependency>
      <groupId>com.yunqu.emergency</groupId>
      <artifactId>de-common-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.yunqu.emergency</groupId>
      <artifactId>de-common-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.yunqu.emergency</groupId>
      <artifactId>de-common-json</artifactId>
    </dependency>
    <dependency>
      <groupId>com.yunqu.emergency</groupId>
      <artifactId>de-common-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.yunqu.emergency</groupId>
      <artifactId>de-common-excel</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.retry</groupId>
      <artifactId>spring-retry</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aspects</artifactId>
    </dependency>
    <dependency>
      <groupId>com.yunqu.emergency</groupId>
      <artifactId>de-common-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.17.2</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <resources>
      <resource>
        <filtering>false</filtering>
        <directory>src/main/resources</directory>
      </resource>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
        <includes>
          <include>application*</include>
          <include>bootstrap*</include>
          <include>banner*</include>
        </includes>
      </resource>
    </resources>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler-plugin.version}</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <encoding>${project.build.sourceEncoding}</encoding>
          <annotationProcessorPaths>
            <path>
              <groupId>com.github.therapi</groupId>
              <artifactId>therapi-runtime-javadoc-scribe</artifactId>
              <version>${therapi-javadoc.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <groupId>org.springframework.boot</groupId>
              <artifactId>spring-boot-configuration-processor</artifactId>
              <version>${spring-boot.version}</version>
            </path>
            <path>
              <groupId>io.github.linpeilie</groupId>
              <artifactId>mapstruct-plus-processor</artifactId>
              <version>${mapstruct-plus.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>${mapstruct-plus.lombok.version}</version>
            </path>
          </annotationProcessorPaths>
          <compilerArgs>
            <arg>-parameters</arg>
          </compilerArgs>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${maven-surefire-plugin.version}</version>
        <configuration>
          <argLine>-Dfile.encoding=UTF-8</argLine>
          <groups>${profiles.active}</groups>
          <excludedGroups>exclude</excludedGroups>
          <skipTests>true</skipTests>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>${flatten-maven-plugin.version}</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <updatePomFile>true</updatePomFile>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-dependency-plugin</artifactId>
        <version>3.6.1</version>
        <executions>
          <execution>
            <id>analyze-dependencies</id>
            <phase>verify</phase>
            <goals>
              <goal>analyze</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <failOnWarning>false</failOnWarning>
          <outputXML>true</outputXML>
          <ignoredUnusedDeclaredDependencies>
            <ignoredUnusedDeclaredDependency>org.projectlombok:lombok</ignoredUnusedDeclaredDependency>
          </ignoredUnusedDeclaredDependencies>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${spring-boot.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${maven-jar-plugin.version}</version>
      </plugin>
      <plugin>
        <artifactId>maven-war-plugin</artifactId>
        <version>${maven-war-plugin.version}</version>
        <configuration>
          <failOnMissingWebXml>false</failOnMissingWebXml>
          <warName>${project.artifactId}</warName>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>dev</id>
      <properties>
        <profiles.active>dev</profiles.active>
        <logging.level>info</logging.level>
      </properties>
    </profile>
    <profile>
      <id>local</id>
      <properties>
        <profiles.active>local</profiles.active>
        <logging.level>info</logging.level>
      </properties>
    </profile>
    <profile>
      <id>sit</id>
      <properties>
        <profiles.active>sit</profiles.active>
        <logging.level>debug</logging.level>
      </properties>
    </profile>
    <profile>
      <id>uat</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <profiles.active>uat</profiles.active>
        <logging.level>debug</logging.level>
      </properties>
    </profile>
    <profile>
      <id>prod</id>
      <properties>
        <profiles.active>prod</profiles.active>
        <logging.level>warn</logging.level>
      </properties>
    </profile>
  </profiles>
</project>
