package com.yunqu.crawler.base;

/**
 * <p>
 *
 * </p>
 *
 * @ClassName CacheConstants
 * <AUTHOR> Copy This Tag)
 * @Description TODO 描述文件用途
 * @Since create in 2025/5/23 13:51
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
public interface CacheConstants {

    /**
     * 移动省份字典缓存key
     */
    String MOBILE_PROVINCE_DICT_KEY = "sys:crawler:mobile:province:dict";

    /**
     * 移动省份字典缓存key
     */
    String MOBILE_PROVINCE_CODE_DICT_KEY = "sys:crawler:mobile:province:code:dict";

    /**
     * 移动省份信息字典缓存key
     */
    String MOBILE_PROVINCE_INFO_DICT_KEY  = "sys:crawler:mobile:province:info:dict";

    /**
     * 联通省份字典缓存key
     */
    String UNICOM_PROVINCE_DICT_KEY = "sys:crawler:unicom:province:dict";

    /**
     * 联通省份字典缓存key
     */
    String UNICOM_PROVINCE_CODE_DICT_KEY = "sys:crawler:unicom:province:code:dict";

    /**
     * 联通省份信息字典缓存key
     */
    String UNICOM_PROVINCE_INFO_DICT_KEY  = "sys:crawler:unicom:province:info:dict";

    /**
     * 广电省份字典缓存key
     */
    String GDT_PROVINCE_DICT_KEY = "sys:crawler:gdt:province:dict";

    /**
     * 广电省份字典缓存key
     */
    String GDT_PROVINCE_CODE_DICT_KEY = "sys:crawler:gdt:province:code:dict";

    /**
     * 广电省份信息字典缓存key
     */
    String GDT_PROVINCE_INFO_DICT_KEY  = "sys:crawler:gdt:province:info:dict";

    /**
     * 电信省份字典缓存key
     */
    String TELECOM_PROVINCE_DICT_KEY = "sys:crawler:telecom:province:dict";

    /**
     * 电信省份字典缓存key
     */
    String TELECOM_PROVINCE_CODE_DICT_KEY = "sys:crawler:telecom:province:code:dict";

    /**
     * 电信省份信息字典缓存key
     */
    String TELECOM_PROVINCE_INFO_DICT_KEY  = "sys:crawler:telecom:province:info:dict";

}
