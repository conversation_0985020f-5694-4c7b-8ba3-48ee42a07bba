# =========================
# Maven
# =========================
target/
!.mvn/wrapper/maven-wrapper.jar

# =========================
# Gradle
# =========================
build/
.gradle/
!**/src/main/**/build/
!**/src/test/**/build/

# =========================
# Logs
# =========================
logs/
*.log
*.log.*

# =========================
# OS Files
# =========================
.DS_Store
Thumbs.db

# =========================
# IDEs and Editors
# =========================
# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# VS Code
.vscode/

# Eclipse
.classpath
.project
.settings/
.apt_generated
.factorypath
.springBeans
.sts4-cache

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# =========================
# Test output
# =========================
test-output/
surefire-reports/
jacoco.exec

# =========================
# Environment files
# =========================
.env
.env.*

# =========================
# Package managers
# =========================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# =========================
# Others
# =========================
*.swp
*.swo
*.bak
*.tmp
