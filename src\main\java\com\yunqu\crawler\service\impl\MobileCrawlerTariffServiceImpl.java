package com.yunqu.crawler.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.base.CacheConstants;
import com.yunqu.crawler.base.Constants;
import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.domain.vo.XtyTariffMobileRecordImportVo;
import com.yunqu.crawler.domain.vo.XtyTariffProvinceVo;
import com.yunqu.crawler.enums.OperatorEnum;
import com.yunqu.crawler.error.CrawlerException;
import com.yunqu.crawler.event.CrawlerTaskEvent;
import com.yunqu.crawler.mapper.XtyCrawlerTaskMapper;
import com.yunqu.crawler.mapper.XtyTariffCrawlRecordMapper;
import com.yunqu.crawler.service.ICrawlerCallbackService;
import com.yunqu.crawler.service.ICrawlerTariffService;
import com.yunqu.crawler.service.IXtyTariffProvinceService;
import com.yunqu.emergency.common.core.exception.ServiceException;
import com.yunqu.emergency.common.core.utils.SpringUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.excel.utils.ExcelUtil;
import com.yunqu.emergency.common.mybatis.annotation.Dynamic;
import com.yunqu.emergency.common.redis.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 移动资费抓取服务实现
 * 负责移动资费数据的抓取、回调文件解析、数据入库等业务。
 * 主要流程：
 * 1. 通过 crawler 方法发起抓取任务。
 * 2. 回调 callback 方法处理抓取结果文件，解析并入库。
 * 3. 解析过程中，统一处理省份、通用字段、异常日志。
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
@Service(Constants.OPERATOR_MOBILE + ICrawlerTariffService.BASE_NAME)
public class MobileCrawlerTariffServiceImpl implements ICrawlerTariffService, ICrawlerCallbackService {
    // 移动日志前缀
    private static final String LOG_PREFIX = "[MOBILE]";

    // === 依赖注入 ===
    private final XtyCrawlerTaskMapper crawlerTaskMapper;
    private final XtyTariffCrawlRecordMapper tariffCrawlRecordMapper;
    private final IXtyTariffProvinceService xtyTariffProvinceService;

    // === 配置参数 ===
    @Value("${crawler.local-server}")
    private String localUrl;
    @Value("${crawler.base-url}")
    private String crawlerBaseUrl;
    @Value("${crawler.mobile-provinces}")
    private String provinces;
    @Value("${crawler.mobile-provinces-name}")
    private String provincesName;
    @Value("${file.tmp-path}")
    private String localFilePath;

    // === Magic String 常量 ===
    private static final String UNIT_YUAN_MONTH = "元/月";
    private static final String UNIT_YUAN_YEAR = "元/年";
    private static final String UNIT_GB = "GB";
    private static final String UNIT_MB = "MB";
    private static final String UNIT_KB = "KB";
    private static final String UNIT_MIN = "分钟";
    private static final String FIELD_CALL = "通话";
    private static final String NATION_FIELD_CALL = "国内通话";
    private static final String FIELD_GENERAL_FLOW = "通用流量";
    private static final String NATION_FIELD_GENERAL_FLOW = "国内通用流量";
    private static final String FIELD_ORIENT_TRAFFIC = "定向流量";
    private static final String FIELD_BANDWIDTH = "宽带";
    private static final String FIELD_HD = "移动高清";
    private static final String FIELD_RIGHTS = "权益";
    private static final String FIELD_SMS = "短彩信";

    /**
     * 发起移动资费数据抓取任务
     *
     * @param dateId 日期ID
     */
    @Override
    public void crawler(int dateId, String versionNo) {
        log.info("{} [TASK-START] 开始资费数据抓取任务, dateId: {}", LOG_PREFIX, dateId);

        if (StringUtils.isBlank(provinces)) {
            log.error("{} [TASK-FAIL] 资费爬虫省份配置为空", LOG_PREFIX);
            return;
        }

        String[] provinceArray = StringUtils.split(provinces, ",");
        String[] provinceNameArray = StringUtils.split(provincesName, ",");
        log.info("{} [TASK-INFO] 需要抓取的省份数量: {}", LOG_PREFIX, provinceArray.length);

        for (int i = 0; i < provinceArray.length; i++) {
            String province = provinceArray[i];
            String provinceName = provinceNameArray[i];
            log.info("{} [TASK-PROCESS] 开始创建资费爬虫任务, 省份: {}", LOG_PREFIX, province);

            try {
                XtyCrawlerTask crawlerTask = new XtyCrawlerTask();
                crawlerTask.setDateId(dateId);
                crawlerTask.setProvinceCode(province);
                crawlerTask.setProvinceName(provinceName);
                crawlerTask.setStatus(0);
                crawlerTask.setEntType(2);
                crawlerTask.setOperatorCode(OperatorEnum.MOBILE.getAlias());
                crawlerTask.setOperatorName(OperatorEnum.MOBILE.getName());
                crawlerTask.setVersionNo(versionNo);
                crawlerTaskMapper.insert(crawlerTask);
                log.info("{} [TASK-SUCCESS] 成功创建资费爬虫任务, taskId: {}, 省份: {}", LOG_PREFIX, crawlerTask.getId(), province);
            } catch (Exception e) {
                log.error("{} [TASK-FAIL] 创建资费爬虫任务失败, 省份: {}, 错误: {}", LOG_PREFIX, province, e.getMessage(), e);
            }
        }

        log.info("{} [TASK-END] 完成资费数据抓取任务创建, dateId: {}", LOG_PREFIX, dateId);
    }

    /**
     * 执行请求爬虫
     *
     * @param task 爬虫任务
     */
    @Async
    @Override
    public void excueteRequestCrawler(XtyCrawlerTask task) {
        log.info("{} [CRAWLER-START] 开始执行资费爬虫请求, taskId: {}, 省份: {}", LOG_PREFIX, task.getId(), task.getProvinceCode());

        String operatorCode = task.getOperatorCode();
        String callback = localUrl + "/callback/" + operatorCode + "/" + task.getId();

        Map<String, String> map = new HashMap<>();
        map.put("callback_url", callback);
        map.put("provinces", task.getProvinceCode());
        map.put("crawler_type", OperatorEnum.MOBILE.getAlias());

        try {
            log.info("{} [CRAWLER-PROCESS] 发送爬虫请求, taskId: {}, url: {}, 参数: {}", LOG_PREFIX, task.getId(),
                    crawlerBaseUrl + "/api/v1/crawl", map);
            requestToCrawler(crawlerBaseUrl + "/api/v1/crawl", JSONUtil.toJsonStr(map));
            log.info("{} [CRAWLER-SUCCESS] 成功发送爬虫请求, taskId: {}", LOG_PREFIX, task.getId());
        } catch (Exception e) {
            log.error("{} [CRAWLER-FAIL] 发送爬虫请求失败, taskId: {}, 错误: {}", LOG_PREFIX, task.getId(), e.getMessage(), e);
        }
    }

    /**
     * 移动资费回调文件处理主入口
     * 1. 解压回调文件
     * 2. 解析每个excel文件，分发到明细处理
     * 3. 统一异常和目录清理
     *
     * @param taskId 任务id
     * @param file   回调文件
     */
    @Override
    public void callback(Long taskId, MultipartFile file, String crawlerUrl) {
        log.info("{} [CALLBACK-START] 收到资费爬虫回调, taskId: {}, 文件名: {}", LOG_PREFIX, taskId, file.getOriginalFilename());
        long startTime = System.currentTimeMillis();

        XtyCrawlerTask xtyCrawlerTask = crawlerTaskMapper.selectById(taskId);
        String tempDirPath = localFilePath + File.separator + taskId;
        try {
            SpringUtils.getAopProxy(this).excuete(taskId, xtyCrawlerTask.getVersionNo(), file, crawlerUrl, xtyCrawlerTask, tempDirPath, startTime);
        } catch(CrawlerException e) {
            log.error(e.getMessage(), e);
            xtyCrawlerTask.setStatus(0);
            xtyCrawlerTask.setMemo(e.getMessage());
            crawlerTaskMapper.updateById(xtyCrawlerTask);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        } finally {
            ICrawlerCallbackService.deleteDirectory(tempDirPath);
        }
    }

    @Transactional(rollbackFor = {Exception.class, RuntimeException.class, ServiceException.class})
    @Dynamic(version = "versionNo")
    protected void excuete(Long taskId, String versionNo, MultipartFile file, String crawlerUrl, XtyCrawlerTask xtyCrawlerTask, String tempDirPath, long startTime) throws IOException {
        JSONObject provinceInfo = RedisUtils.getCacheMapValue(CacheConstants.MOBILE_PROVINCE_INFO_DICT_KEY, xtyCrawlerTask.getProvinceCode());
        String provinceCode = provinceInfo.getStr("provinceCode");
        String provinceName = provinceInfo.getStr("provinceName");
        String taskProvinceName = provinceInfo.getStr("text");

        String crawlerCallbackFileName = file.getOriginalFilename();
        String zipFileFolderPath = localFilePath + File.separator + xtyCrawlerTask.getVersionNo() + File.separator + OperatorEnum.MOBILE.getAlias();
        File zipFileFolder = new File(zipFileFolderPath);
        if (!zipFileFolder.exists()) zipFileFolder.mkdirs();
        String zipFilePath = zipFileFolderPath + File.separator + crawlerCallbackFileName;
        File zipFile = FileUtil.file(zipFilePath);
        file.transferTo(zipFile);
        File folder = new File(tempDirPath);
        ICrawlerCallbackService.unzipFile(zipFile, folder, System.currentTimeMillis());

        Integer dateId = xtyCrawlerTask.getDateId();

        try {
            Map<String, Integer> map = Map.of("localProvinceCount", 0, "groupCount", 0);
            if (folder.isDirectory()) {
                File[] files = folder.listFiles();
                if (files != null) {
                    if (files.length < 2) {
                        xtyCrawlerTask.setStatus(0);
                        crawlerTaskMapper.updateById(xtyCrawlerTask);
                        log.info("{} [TASK-UPDATE] 状态更新成功, taskId: {}", LOG_PREFIX, xtyCrawlerTask.getId());
                        return;
                    }
                    log.info("{} [FILE-START] 开始处理解压后的文件, 文件数量: {}", LOG_PREFIX, files.length);
                    map = processExcelFile(files, taskId, dateId, crawlerCallbackFileName, crawlerUrl, versionNo,
                            provinceName, provinceCode, taskProvinceName);
                } else {
                    log.warn("{} [FILE-WARN] 解压目录为空, 路径: {}", LOG_PREFIX, tempDirPath);
                }
            } else {
                log.error("{} [FILE-ERROR] 解压路径不是目录: {}", LOG_PREFIX, tempDirPath);
            }
            Integer localProvinceCount = map.get("localProvinceCount");
            Integer groupCount = map.get("groupCount");
            if(localProvinceCount == 0 || groupCount == 0) {
                log.warn("{} [FILE-WARN] 本省资费或全网资费爬取异常, 忽略更新任务状态, 运营商：{}{}, localProvinceCount:{}, groupCount:{}", LOG_PREFIX, provincesName, OperatorEnum.MOBILE.getName(), localProvinceCount, groupCount);
                throw new CrawlerException("本省资费或全网资费爬取异常");
            }
            xtyCrawlerTask.setStatus(2);
            xtyCrawlerTask.setLocalProvinceCount(map.get("localProvinceCount"));
            xtyCrawlerTask.setGroupCount(map.get("groupCount"));
            xtyCrawlerTask.setCrawlerEndTime(new Date());
            crawlerTaskMapper.updateById(xtyCrawlerTask);
            log.info("{} [CALLBACK-SUCCESS] 完成资费爬虫回调处理, taskId: {}, 耗时: {}ms", LOG_PREFIX, taskId,
                    System.currentTimeMillis() - startTime);

            SpringUtils.publishEvent(new CrawlerTaskEvent(taskId));
        } catch(CrawlerException e) {
            log.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("{} [CALLBACK-FAIL] 回调处理异常, taskId: {}, 错误: {}", LOG_PREFIX, taskId, e.getMessage(), e);
            throw e;
        } finally {
            ICrawlerCallbackService.deleteDirectory(folder);
            log.info("{} [CALLBACK-CLEANUP] 清理临时文件完成, 路径: {}", LOG_PREFIX, tempDirPath);
        }
    }

    /**
     * 处理单个Excel文件
     */
    private Map<String, Integer> processExcelFile(File[] tariffFiles, Long taskId, Integer dateId,
                                                  String crawlerCallbackFileName, String crawlerUrl, String versionNo, String provinceName,
                                                  String provinceCode, String taskProvinceName) {
        int localProvinceCount = 0;
        int groupCount = 0;


        List<XtyTariffCrawlRecord> list = new ArrayList<>();
        for (File tariffFile : tariffFiles) {
            String crawlerDataBelongFileName = tariffFile.getName();
            log.info("{} [FILE-START] 开始处理Excel文件: {}", LOG_PREFIX, crawlerDataBelongFileName);

            String fileName = crawlerDataBelongFileName.substring(0,
                    StringUtils.lastIndexOf(crawlerDataBelongFileName, "."));
            String[] fileNames = fileName.split("_");

            String crawlerType = "";
            if (fileNames.length == 6) {
                crawlerType = fileNames[4];
            } else {
                crawlerType = fileNames[3];
            }

            if (!StringUtils.equals("全网资费", crawlerType)) {
                crawlerType = provinceName + "资费";
            }

            try {
                List<XtyTariffMobileRecordImportVo> mobileCrawlerTariffData = parseExcelFile(tariffFile);
                if (mobileCrawlerTariffData == null || mobileCrawlerTariffData.isEmpty()) {
                    log.warn("{} [FILE-WARN] Excel文件解析后无数据: {}", LOG_PREFIX, tariffFile.getName());
                    return Map.of("localProvinceCount", localProvinceCount, "groupCount", groupCount);
                }

                log.info("{} [DATA-START] 开始处理资费数据, 文件: {}, 数据条数: {}", LOG_PREFIX, tariffFile.getName(),
                        mobileCrawlerTariffData.size());
                for (XtyTariffMobileRecordImportVo crawlerData : mobileCrawlerTariffData) {
                    try {
                        XtyTariffCrawlRecord record = analysisMobileTariffInfo(taskId, crawlerData, dateId, taskProvinceName, crawlerType,
                                crawlerCallbackFileName, crawlerDataBelongFileName, crawlerUrl, versionNo, provinceCode,
                                provinceName);
                        record.setProvinceCode(provinceCode);
                        record.setProvinceName(provinceName);
                        record.setCrawlerProvinceName(taskProvinceName);
                        list.add(record);
                    } catch (Exception e) {
                        log.error("{} [DATA-FAIL] 处理单条资费数据失败, 文件: {}, 数据: {}, 错误: {}",
                                LOG_PREFIX, tariffFile.getName(), crawlerData, e.getMessage(), e);
                        throw new RuntimeException(e);
                    }
                }
                int size = mobileCrawlerTariffData.size();

                if (!StringUtils.equals("全网资费", crawlerType)) {
                    localProvinceCount += size;
                } else {
                    groupCount += size;
                }
                log.info("{} [FILE-SUCCESS] 完成Excel文件处理: {}", LOG_PREFIX, crawlerDataBelongFileName);
            } catch (Exception e) {
                log.error("{} [FILE-FAIL] 处理Excel文件失败: {}, 错误: {}", LOG_PREFIX, tariffFile.getName(), e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }
        tariffCrawlRecordMapper.insertBatch(list);
        return Map.of("localProvinceCount", localProvinceCount, "groupCount", groupCount);
    }

    /**
     * 解析单个excel文件为List
     *
     * @param file excel文件
     * @return 解析结果
     */
    private List<XtyTariffMobileRecordImportVo> parseExcelFile(File file) {
        log.debug("{} [FILE-START] 开始解析Excel文件: {}", LOG_PREFIX, file.getName());
        try (InputStream inputStream = new FileInputStream(file)) {
            List<XtyTariffMobileRecordImportVo> result = ExcelUtil.importExcel(inputStream,
                    XtyTariffMobileRecordImportVo.class);
            log.debug("{} [FILE-SUCCESS] Excel文件解析完成: {}, 解析数据条数: {}", LOG_PREFIX, file.getName(), result.size());
            return result;
        } catch (IOException e) {
            log.error("{} [FILE-FAIL] 解析Excel文件失败: {}, 错误: {}", LOG_PREFIX, file.getName(), e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 解析单条资费数据并入库
     *
     * @param taskId              任务id
     * @param crawlerData         数据
     * @param dateId              日期id
     * @param crawlerProvinceName 爬取省份名称
     * @param crawlerType         爬取类型
     */
    private XtyTariffCrawlRecord analysisMobileTariffInfo(Long taskId, XtyTariffMobileRecordImportVo crawlerData, Integer dateId,
                                                          String crawlerProvinceName, String crawlerType, String crawlerCallbackFileName,
                                                          String crawlerDataBelongFileName, String crawlerUrl, String versionNo, String provinceCode,
                                                          String provinceName) {
        log.debug("{} [DATA-START] 开始解析移动资费数据, taskId: {}, 套餐名称: {}", LOG_PREFIX, taskId,
                crawlerData.getPackageCategory());

        XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
        record.setExtendedFields(JSONUtil.toJsonStr(crawlerData));
        record.setTaskId(taskId);
        record.setDateId(dateId);
        record.setEntCode(OperatorEnum.MOBILE.getCode());
        record.setEntName(OperatorEnum.MOBILE.getName());
        record.setName(crawlerData.getPackageCategory());
        record.setProvinceCode(provinceCode);
        record.setProvinceName(provinceName);

        // 省份解析
        String tariffNo = crawlerData.getTariffNo();
        if (StringUtils.isBlank(tariffNo)) {
            tariffNo = crawlerData.getTariffNoDefault();
        }
        if (StringUtils.isBlank(tariffNo)) {
            tariffNo = crawlerData.getTariffNoDefault2();
        }
        if (StringUtils.isNotBlank(tariffNo) && tariffNo.length() >= 4) {
            try {
                String tariffProvinceCode = tariffNo.substring(2, 4);
                XtyTariffProvinceVo tariffProvince = xtyTariffProvinceService
                        .getByTariffProvinceCode(tariffProvinceCode);
                record.setTariffProvinceCode(tariffProvinceCode);
                record.setTariffProvinceName(tariffProvince.getProvinceName());
                log.debug("{} [DATA-SUCCESS] 省份解析成功, tariffNo: {}, 省份: {}", LOG_PREFIX, tariffNo,
                        tariffProvince.getProvinceName());
            } catch (Exception e) {
                log.error("{} [DATA-FAIL] 省份解析失败, tariffNo: {}, 错误: {}", LOG_PREFIX, tariffNo, e.getMessage());
            }
        } else {
            log.warn("{} [DATA-WARN] 资费编号异常, tariffNo: {}", LOG_PREFIX, tariffNo);
        }

        record.setTariffNo(tariffNo);

        // 解析资费标准和单位
        parseFeeInfo(record, crawlerData.getTariffStandard());

        // 设置基本信息
        record.setApplicablePeople(crawlerData.getTariffScope());
        record.setApplicableArea(StringUtils.trimToEmpty(crawlerData.getApplicationArea()));
        record.setChannel(crawlerData.getTariffChannel());
        String onlineDate = StringUtils.trimToEmpty(crawlerData.getOnlineDate());
        record.setOnlineDay(formatDateStr(onlineDate));
        String offlineDate = StringUtils.trimToEmpty(crawlerData.getOfflineDate());
        record.setOfflineDay(formatDateStr(offlineDate));
        record.setValidPeriod(crawlerData.getValidPeriod());
        record.setDuration(crawlerData.getOnlineRequirement());
        record.setUnsubscribe(crawlerData.getUnsubscribeMethod());
        record.setResponsibility(crawlerData.getBreachResponsibility());
        String serviceContent = crawlerData.getServiceContent();
        if (StringUtils.isBlank(serviceContent)) {
            serviceContent = crawlerData.getOtherServiceContent();
        }
        record.setOtherContent(serviceContent);
        record.setOthers(crawlerData.getOthers());

        record.setOutFeeContent(crawlerData.getOutFeeContent());
        record.setOtherFeeContent(crawlerData.getOtherFeeContent());

        // 解析表格信息
        parseTableInfo(record, crawlerData.getTableInfo());

        // 设置爬虫相关信息
        record.setCrawlerProvinceName(crawlerProvinceName);
        record.setCrawlerType(crawlerType);
        record.setCrawlerCallbackFileName(crawlerCallbackFileName);
        record.setCrawlerDataBelongFileName(crawlerDataBelongFileName);
        record.setTariffType(crawlerData.getTariffType());
        record.setCrawlUrl(crawlerUrl);
        record.setVersionNo(versionNo);
        return record;
    }

    /**
     * 解析资费标准和单位
     */
    private void parseFeeInfo(XtyTariffCrawlRecord record, String feeData) {
        if (StringUtils.isNotBlank(feeData)) {
            String fees = "";
            String feeUnit = "";
            if (feeData.contains(UNIT_YUAN_MONTH)) {
                fees = feeData.substring(0, feeData.indexOf(UNIT_YUAN_MONTH));
                feeUnit = UNIT_YUAN_MONTH;
            } else if (feeData.contains(UNIT_YUAN_YEAR)) {
                fees = feeData.substring(0, feeData.indexOf(UNIT_YUAN_YEAR));
                feeUnit = UNIT_YUAN_YEAR;
            }
            record.setFees(fees);
            record.setFeesUnit(feeUnit);
            log.debug("{} [DATA-SUCCESS] 解析资费标准: {} {}", LOG_PREFIX, fees, feeUnit);
        }
    }

    /**
     * 解析表格信息
     */
    private void parseTableInfo(XtyTariffCrawlRecord record, JSONObject tableInfo) {
        if (tableInfo != null) {
            // 通话
            String callData = StringUtils.trimToEmpty(tableInfo.getStr(FIELD_CALL));
            if (StringUtils.isBlank(callData)) {
                callData = StringUtils.trimToEmpty(tableInfo.getStr(NATION_FIELD_CALL));
            }
            if (StringUtils.isNotBlank(callData) && callData.contains(UNIT_MIN)) {
                callData = callData.substring(0, callData.indexOf(UNIT_MIN));
                record.setCall(callData);
            }

            String internationalCallData = StringUtils.trimToEmpty(tableInfo.getStr("国际通话"));
            if (StringUtils.isNotBlank(internationalCallData)) {
                internationalCallData = internationalCallData.substring(0, internationalCallData.indexOf(UNIT_MIN));
                record.setInternationalCall(internationalCallData);
            }

            // 通用流量
            String flow = tableInfo.getStr(FIELD_GENERAL_FLOW);
            if (StringUtils.isBlank(flow)) {
                flow = tableInfo.getStr(NATION_FIELD_GENERAL_FLOW);
            }
            parseFlowData(record, flow, true);

            // 定向流量
            parseFlowData(record, tableInfo.getStr(FIELD_ORIENT_TRAFFIC), false);

            // 其他扩展字段
            record.setSms(StringUtils.trimToEmpty(tableInfo.getStr(FIELD_SMS)));
            record.setBandwidth(StringUtils.trimToEmpty(tableInfo.getStr(FIELD_BANDWIDTH)));
            record.setIptv(StringUtils.trimToEmpty(tableInfo.getStr(FIELD_HD)));
            record.setRights(StringUtils.trimToEmpty(tableInfo.getStr(FIELD_RIGHTS)));

            log.debug("{} [DATA-SUCCESS] 解析表格信息完成", LOG_PREFIX);
        }
    }

    /**
     * 解析流量数据
     */
    private void parseFlowData(XtyTariffCrawlRecord record, String flowData, boolean isGeneralFlow) {
        if (StringUtils.isNotBlank(flowData)) {
            String data = "";
            String dataUnit = "";
            if (flowData.contains(UNIT_GB)) {
                data = flowData.substring(0, flowData.indexOf(UNIT_GB));
                dataUnit = UNIT_GB;
            } else if (flowData.contains(UNIT_MB)) {
                data = flowData.substring(0, flowData.indexOf(UNIT_MB));
                dataUnit = UNIT_MB;
            } else if (flowData.contains(UNIT_KB)) {
                data = flowData.substring(0, flowData.indexOf(UNIT_KB));
                dataUnit = UNIT_KB;
            }

            if (isGeneralFlow) {
                record.setData(data);
                record.setDataUnit(dataUnit);
            } else {
                record.setOrientTraffic(data);
                record.setOrientTrafficUnit(dataUnit);
            }

            log.debug("{} [DATA-SUCCESS] 解析{}流量: {} {}", LOG_PREFIX, isGeneralFlow ? "通用" : "定向", data, dataUnit);
        }
    }
}
