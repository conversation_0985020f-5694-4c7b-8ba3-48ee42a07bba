#!/bin/sh
# ./run.sh start|stop|restart|status [env]
# Example: ./run.sh start dev
# Example: ./run.sh start prod
AppName=xty-crawler-service.jar

# 默认环境
DEFAULT_ENV="dev"
ACTIVE_ENV=${2:-$DEFAULT_ENV}

# JVM参数
JVM_OPTS="-Dname=$AppName -Duser.timezone=Asia/Shanghai -Xms2048m -Xmx4096m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:+UseZGC"
APP_HOME=`pwd`
LOG_PATH=$APP_HOME/logs
mkdir -p $LOG_PATH

if [ "$1" = "" ];
then
    echo -e "\033[0;31m 未输入操作名 \033[0m  \033[0;34m {start|stop|restart|status} [env] \033[0m"
    echo -e "示例: ./run.sh start dev"
    exit 1
fi

if [ "$AppName" = "" ];
then
    echo -e "\033[0;31m 未输入应用名 \033[0m"
    exit 1
fi

function check_java() {
    if type -p java; then
        echo "找到 java 可执行文件"
        _java=java
    elif [[ -n "$JAVA_HOME" ]] && [[ -x "$JAVA_HOME/bin/java" ]];  then
        echo "找到 JAVA_HOME 环境变量"
        _java="$JAVA_HOME/bin/java"
    else
        echo -e "\033[0;31m 未找到 Java 环境，请安装 JDK \033[0m"
        exit 1
    fi
}

function start()
{    
    if [ ! -f "$APP_HOME/$AppName" ]; then
        echo -e "\033[0;31m 错误: $AppName 文件不存在! \033[0m"
        exit 1
    fi
    
    PID=`ps -ef |grep java|grep $AppName|grep -v grep|awk '{print $2}'`

    if [ x"$PID" != x"" ]; then
        echo "$AppName is running..."
    else
        echo "Starting $AppName with profile: $ACTIVE_ENV ..."
        nohup java $JVM_OPTS -jar $AppName --spring.profiles.active=$ACTIVE_ENV > $LOG_PATH/startup.log 2>&1 &
        
        # 等待几秒检查是否成功启动
        sleep 3
        PID=`ps -ef |grep java|grep $AppName|grep -v grep|awk '{print $2}'`
        if [ x"$PID" != x"" ]; then
            echo -e "\033[0;32m Start $AppName success... PID: $PID \033[0m"
            echo "环境: $ACTIVE_ENV"
            echo "日志路径: $LOG_PATH/startup.log"
        else
            echo -e "\033[0;31m Start $AppName failed... Please check logs: $LOG_PATH/startup.log \033[0m"
        fi
    fi
}

function stop()
{
    echo "Stop $AppName"

    PID=""
    query(){
        PID=`ps -ef |grep java|grep $AppName|grep -v grep|awk '{print $2}'`
    }

    query
    if [ x"$PID" != x"" ]; then
        kill -TERM $PID
        echo "$AppName (pid:$PID) exiting..."
        while [ x"$PID" != x"" ]
        do
            sleep 1
            query
        done
        echo "$AppName exited."
    else
        echo "$AppName already stopped."
    fi
}

function restart()
{
    stop
    sleep 2
    start
}

function status()
{
    PID=`ps -ef |grep java|grep $AppName|grep -v grep|wc -l`
    if [ $PID != 0 ];then
        echo "$AppName is running..."
    else
        echo "$AppName is not running..."
    fi
}

case $1 in
    start)
    start;;
    stop)
    stop;;
    restart)
    restart;;
    status)
    status;;
    *)

esac
