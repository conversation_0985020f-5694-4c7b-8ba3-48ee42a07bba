package com.yunqu.crawler.service.impl.telecom;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.base.StringAmountUnitParser;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.util.ParseDateUtil;
import com.yunqu.emergency.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@Slf4j
public class BaseTelecomRecordAnalysisHandler implements IRecordAnalysisHandler {
    @Override
    public List<XtyTariffCrawlRecord> analysis(String content) {
        JSONObject entries = JSONUtil.parseObj(content);
        List<XtyTariffCrawlRecord> result = new ArrayList<>();

        for (String key : entries.keySet()) {
            String[] split = key.split("_");
            String crawlerProvinceName = split[0];
            String tariffType = split[1];
            JSONArray jsonArray = entries.getJSONArray(key);
            List<XtyTariffCrawlRecord> records = analysisContent(jsonArray);
            List<XtyTariffCrawlRecord> list = records.stream().map(o -> {
                o.setCrawlerProvinceName(crawlerProvinceName);
                o.setTariffType(tariffType);
                o.setClassicTypeOne(crawlerProvinceName);
                o.setClassicTypeTwo(tariffType);
                return o;
            }).toList();
            result.addAll(list);
        }
        return result;
    }


    private List<XtyTariffCrawlRecord> analysisContent(JSONArray array) {
        if (array == null || array.isEmpty()) return List.of();
        List<XtyTariffCrawlRecord> result = new ArrayList<>();
        for (int i = 0; i < array.size(); i++) {
            JSONObject object = array.getJSONObject(i);
            if (object == null) continue;
            XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
            record.setName(StringUtils.trimToEmpty(object.getStr("title")));
            record.setTariffNo(StringUtils.trimToEmpty(object.getStr("code")));
            record.setExtendedFields(JSONUtil.toJsonStr(object));
            JSONObject basicInfo = object.getJSONObject("basic_info");
            if (basicInfo != null) {
                String fees = StringUtils.trimToEmpty(basicInfo.getStr("资费标准"));
                if (StringUtils.isNotEmpty(fees)) {
                    StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(fees);
                    if (parse != null) {
                        record.setFees(parse.getAmount());
                        record.setFeesUnit(parse.getUnit());
                    }
                }

                record.setApplicablePeople(StringUtils.trimToEmpty(basicInfo.getStr("适用范围")));
                record.setChannel(StringUtils.trimToEmpty(basicInfo.getStr("销售渠道")));

                String upDownDay = StringUtils.trimToEmpty(basicInfo.getStr("上下线时间"));
                if (StringUtils.isNotEmpty(upDownDay)) {
                    if(StringUtils.contains(upDownDay, "至")) {
                        String[] split = upDownDay.split("至");
                        if (split.length == 2) {
                            record.setOnlineDay(ParseDateUtil.formatDateStr(split[0]));
                            record.setOfflineDay(ParseDateUtil.formatDateStr(split[1]));
                        }
                    } else if(StringUtils.contains(upDownDay, " 至 ")) {
                        String[] split = upDownDay.split(" 至 ");
                        if (split.length == 2) {
                            record.setOnlineDay(ParseDateUtil.formatDateStr(split[0]));
                            record.setOfflineDay(ParseDateUtil.formatDateStr(split[1]));
                        }
                    } else {
                        String[] split = upDownDay.split("-");
                        if (split.length == 2) {
                            record.setOnlineDay(ParseDateUtil.formatDateStr(split[0]));
                            record.setOfflineDay(ParseDateUtil.formatDateStr(split[1]));
                        }
                    }
                }

                record.setValidPeriod(StringUtils.trimToEmpty(basicInfo.getStr("有效期限")));
                record.setUnsubscribe(StringUtils.trimToEmpty(basicInfo.getStr("退订方式")));
                record.setDuration(StringUtils.trimToEmpty(basicInfo.getStr("在网要求")));
                record.setResponsibility(StringUtils.trimToEmpty(basicInfo.getStr("违约责任")));
            }

            JSONObject tableOut = object.getJSONObject("service_content");
            if (tableOut != null) {
                String callInfo = StringUtils.trimToEmpty(tableOut.getStr("语音"));
                if (StringUtils.isNotBlank(callInfo)) {
                    StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(callInfo);
                    if (parse != null) {
                        record.setCall(parse.getAmount());
                    }
                }

                String dataInfo = StringUtils.trimToEmpty(tableOut.getStr("通用流量"));
                if (StringUtils.isNotBlank(dataInfo)) {
                    StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(dataInfo);
                    if (parse != null) {
                        record.setData(parse.getAmount());
                        record.setDataUnit(parse.getUnit());
                    }
                }

                record.setBandwidth(StringUtils.trimToEmpty(tableOut.getStr("宽带")));
                record.setIptv(StringUtils.trimToEmpty(tableOut.getStr("IPTV")));

                String smsInfo = StringUtils.trimToEmpty(tableOut.getStr("短彩信"));
                if (StringUtils.isNotBlank(smsInfo)) {
                    StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(smsInfo);
                    if (parse != null) {
                        record.setSms(parse.getAmount());
                    }
                }

                // 定向流量处理
                String dxll = StringUtils.trimToEmpty(tableOut.getStr("定向流量"));
                if (StringUtils.isNotBlank(dxll)) {
                    StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(dxll);
                    if (parse != null) {
                        record.setOrientTraffic(parse.getAmount());
                        record.setOrientTrafficUnit(parse.getUnit());
                    }
                }

                record.setRights(StringUtils.trimToEmpty(tableOut.getStr("权益")));
            }
            record.setOtherContent(StringUtils.trimToEmpty(object.getStr("remark")));
            result.add(record);
        }
        return result;
    }
}
