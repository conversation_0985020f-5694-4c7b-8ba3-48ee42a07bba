package com.yunqu.crawler.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.yunqu.crawler.base.Constants;
import com.yunqu.crawler.domain.bo.XtyCrawlerVersionBo;
import com.yunqu.crawler.enums.OperatorEnum;
import com.yunqu.crawler.service.ICrawlerTariffService;
import com.yunqu.crawler.service.IXtyCrawlerVersionService;
import com.yunqu.crawler.util.TableUtils;
import com.yunqu.emergency.common.core.domain.R;
import com.yunqu.emergency.common.core.utils.DateUtils;
import com.yunqu.emergency.common.core.utils.SpringUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.redis.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <p>
 * 爬虫任务控制器
 * </p>
 *
 * @ClassName CrawlertTaskController
 * <AUTHOR> Copy This Tag)
 * @Description 爬虫任务控制器
 * @Since create in 2025/5/26 10:54
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@Slf4j
@RestController
@RequestMapping("/crawler/task")
public class CrawlertTaskController {

    private static final String LOG_PREFIX = "[TASK]"; // 任务日志前缀

    // 日期格式
    private static final String DATE_FORMAT = "yyyyMMdd";
    // 运营商名称常量
    private static final String OPERATOR_TELECOM_NAME = "电信";
    private static final String OPERATOR_MOBILE_NAME = "移动";
    private static final String OPERATOR_UNICOM_NAME = "联通";
    private static final String OPERATOR_GDT_NAME = "广电";

    private static final String[] CREATE_TASK_OPERATOR_CODES = {"1", "2", "3", "5"};

    @RequestMapping("/create")
    public R<Void> create(String operatorCode) {
        log.info("{} [MAIN-START] ========== 开始执行资费信息爬取主任务 ==========", LOG_PREFIX);
        long startTime = System.currentTimeMillis();

        String[] operatorCodes = null;

        if (StringUtils.isNotBlank(operatorCode)) {
            operatorCodes = StringUtils.split(operatorCode, ",");
        } else {
            operatorCodes = CREATE_TASK_OPERATOR_CODES;
        }

        try {
            // 获取当前日期ID
            Integer dateId = getCurrentDateId();
            log.info("{} [MAIN-INFO] 当前执行日期: {}", LOG_PREFIX, dateId);

            // 生成版本号
            String versionNo = generateVersion(dateId);

            // 按顺序执行各运营商的爬取任务
            executeOperatorCrawler(dateId, versionNo, operatorCodes);

            log.info("{} [MAIN-END] ========== 资费信息爬取主任务执行完成, 总耗时: {}ms ==========",
                    LOG_PREFIX, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("{} [MAIN-FAIL] 资费信息爬取主任务执行异常: {}", LOG_PREFIX, e.getMessage(), e);
        }

        return R.ok();

    }

    private String generateVersion(int dateId) {
        long indx = RedisUtils.incrAtomicValue("sys:crawler:version:" + dateId);
        return "V" + dateId + "_" + indx;
    }

    /**
     * 获取当前日期ID
     * 1-10号返回当月1号，11-20号返回当月11号，21号及以后返回当月21号
     */
    private Integer getCurrentDateId() {
        /*java.util.Date nowDate = DateUtils.getNowDate();
        int dayOfMonth = DateUtil.dayOfMonth(nowDate);
        java.util.Date targetDate;

        if (dayOfMonth >= 1 && dayOfMonth <= 10) {
            // 1-10号返回当月1号
            targetDate = DateUtil.beginOfMonth(nowDate);
        } else if (dayOfMonth >= 11 && dayOfMonth <= 20) {
            // 11-20号返回当月11号
            targetDate = DateUtil.parse(DateUtil.format(nowDate, "yyyyMM") + "11");
        } else {
            // 21号及以后返回当月21号
            targetDate = DateUtil.parse(DateUtil.format(nowDate, "yyyyMM") + "21");
        }*/

        String dateStr = DateUtil.format(new Date(), DATE_FORMAT);
        return Convert.toInt(dateStr);
    }

    /**
     * 按顺序执行各运营商的爬取任务
     */
    private void executeOperatorCrawler(Integer dateId, String versionNo, String[] operatorCodes) {
        log.info("{} [OPERATOR-START] 开始执行各运营商爬取任务, dateId: {}", LOG_PREFIX, dateId);
        long startTime = System.currentTimeMillis();
        int successCount = 0;
        int failCount = 0;

        long idx = RedisUtils.incrAtomicValue("sys:crawler:version:order");
        XtyCrawlerVersionBo version = new XtyCrawlerVersionBo();
        String crawlRecordTable = TableUtils.createCrawlRecordTable(versionNo);
        version.setVersionNo(versionNo);
        version.setIdx(idx);
        version.setBelongDateId(dateId);
        version.setVersionTableName(crawlRecordTable);
        IXtyCrawlerVersionService versionService = SpringUtils.getBean(IXtyCrawlerVersionService.class);
        versionService.insertByBo(version);

        for (String operatorCode : operatorCodes) {

            if (StringUtils.equals(operatorCode, OperatorEnum.TELECOM.getCode())) {
                // 1. 电信资费信息爬取
                if (executeSingleOperatorCrawler(OPERATOR_TELECOM_NAME,
                        () -> ICrawlerTariffService.crawler(dateId, Constants.OPERATOR_TELECOM, versionNo))) {
                    successCount++;
                } else {
                    failCount++;
                }
            }

            if (StringUtils.equals(operatorCode, OperatorEnum.MOBILE.getCode())) {
                // 2. 移动资费信息爬取
                if (executeSingleOperatorCrawler(OPERATOR_MOBILE_NAME,
                        () -> ICrawlerTariffService.crawler(dateId, Constants.OPERATOR_MOBILE, versionNo))) {
                    successCount++;
                } else {
                    failCount++;
                }
            }

            if (StringUtils.equals(operatorCode, OperatorEnum.UNICOM.getCode())) {
                // 3. 联通资费信息爬取
                if (executeSingleOperatorCrawler(OPERATOR_UNICOM_NAME,
                        () -> ICrawlerTariffService.crawler(dateId, Constants.OPERATOR_UNICOM, versionNo))) {
                    successCount++;
                } else {
                    failCount++;
                }
            }

            if (StringUtils.equals(operatorCode, OperatorEnum.GDT.getCode())) {
                // 4. 广电资费信息爬取
                if (executeSingleOperatorCrawler(OPERATOR_GDT_NAME,
                        () -> ICrawlerTariffService.crawler(dateId, Constants.OPERATOR_GDT, versionNo))) {
                    successCount++;
                } else {
                    failCount++;
                }
            }
        }

        log.info("{} [OPERATOR-END] 完成各运营商爬取任务, 成功: {}, 失败: {}, 总耗时: {}ms",
                LOG_PREFIX, successCount, failCount, System.currentTimeMillis() - startTime);
    }

    /**
     * 执行单个运营商的爬取任务
     * 包含异常处理和日志记录
     *
     * @return true=执行成功，false=执行失败
     */
    private boolean executeSingleOperatorCrawler(String operatorName, Runnable crawlerTask) {
        log.info("{} [OPERATOR-PROCESS] ---------- 开始爬取【{}】资费信息 ----------", LOG_PREFIX, operatorName);
        long startTime = System.currentTimeMillis();

        try {
            crawlerTask.run();
            log.info("{} [OPERATOR-SUCCESS] 【{}】资费信息爬取完成, 耗时: {}ms",
                    LOG_PREFIX, operatorName, System.currentTimeMillis() - startTime);
            return true;
        } catch (Exception e) {
            log.error("{} [OPERATOR-FAIL] 【{}】资费信息爬取失败: {}", LOG_PREFIX, operatorName, e.getMessage(), e);
            return false;
        }
    }
}
