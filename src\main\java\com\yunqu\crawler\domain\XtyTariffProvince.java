package com.yunqu.crawler.domain;

import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 资费省份配置信息对象 xty_tariff_province
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@TableName("xty_tariff_province")
public class XtyTariffProvince {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "ID")
    private Long id;

    /**
     * 省份代码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     *
     */
    private Long pId;

    /**
     * 省份简称
     */
    private String abbreviation;

    /**
     * 拼音
     */
    private String pinyin;

    /**
     * 序号
     */
    private Long number;

    /**
     * 资费专用省份编码
     */
    private String tariffProvinceCode;

    /**
     * 省份全称
     */
    private String provinceAllName;

    /**
     *
     */
    private String tariffProvinceName;


}
