package com.yunqu.crawler.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.RetryListener;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.HashMap;
import java.util.Map;

/**
 * 重试机制配置类
 * 为HTTP请求提供统一的重试策略配置
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Configuration
@EnableRetry
public class RetryConfig {

    /**
     * HTTP请求重试模板
     * 配置指数退避策略和可重试异常类型
     */
    @Bean("httpRetryTemplate")
    public RetryTemplate httpRetryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        
        // 配置重试策略
        Map<Class<? extends Throwable>, Boolean> retryableExceptions = new HashMap<>();
        retryableExceptions.put(IOException.class, true);
        retryableExceptions.put(SocketTimeoutException.class, true);
        retryableExceptions.put(ConnectException.class, true);
        retryableExceptions.put(RuntimeException.class, true); // 包含网络相关的运行时异常

        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy(3, retryableExceptions);
        
        retryTemplate.setRetryPolicy(retryPolicy);
        
        // 配置指数退避策略
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(1000); // 初始间隔1秒
        backOffPolicy.setMultiplier(2.0); // 每次重试间隔翻倍
        backOffPolicy.setMaxInterval(8000); // 最大间隔8秒
        retryTemplate.setBackOffPolicy(backOffPolicy);
        
        // 添加重试监听器
        retryTemplate.registerListener(new HttpRetryListener());
        
        return retryTemplate;
    }
    
    /**
     * HTTP重试监听器
     * 记录重试过程的日志信息
     */
    public static class HttpRetryListener implements RetryListener {
        
        @Override
        public <T, E extends Throwable> boolean open(RetryContext context, RetryCallback<T, E> callback) {
            // 重试开始时的日志
            String methodName = getMethodName(context);
            log.debug("[HTTP-RETRY] 开始重试方法: {}", methodName);
            return true;
        }
        
        @Override
        public <T, E extends Throwable> void close(RetryContext context, RetryCallback<T, E> callback, Throwable throwable) {
            String methodName = getMethodName(context);
            int retryCount = context.getRetryCount();
            
            if (throwable == null) {
                if (retryCount > 0) {
                    log.info("[HTTP-RETRY] 方法 {} 重试成功, 重试次数: {}", methodName, retryCount);
                }
            } else {
                log.warn("[HTTP-RETRY] 方法 {} 重试最终失败, 重试次数: {}, 异常: {}", 
                    methodName, retryCount, throwable.getMessage());
            }
        }
        
        @Override
        public <T, E extends Throwable> void onError(RetryContext context, RetryCallback<T, E> callback, Throwable throwable) {
            String methodName = getMethodName(context);
            int retryCount = context.getRetryCount();
            log.debug("[HTTP-RETRY] 方法 {} 第 {} 次重试失败: {}", methodName, retryCount, throwable.getMessage());
        }
        
        private String getMethodName(RetryContext context) {
            Object contextName = context.getAttribute("context.name");
            return contextName != null ? contextName.toString() : "unknown";
        }
    }
}
