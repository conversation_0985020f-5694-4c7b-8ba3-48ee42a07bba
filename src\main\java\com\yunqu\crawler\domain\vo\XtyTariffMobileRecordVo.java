package com.yunqu.crawler.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.yunqu.crawler.domain.XtyTariffMobileRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 信通院-中国移动公示资费数据视图对象 xty_tariff_mobile_record
 *
 * <AUTHOR>
 * @date 2025-05-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = XtyTariffMobileRecord.class)
public class XtyTariffMobileRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 任务id
     */
    @ExcelProperty(value = "任务id")
    private Long taskId;

    /**
     * 主套餐
     */
    @ExcelProperty(value = "主套餐")
    private String mainPackage;

    /**
     * 类别
     */
    @ExcelProperty(value = "类别")
    private String packageCategory;

    /**
     * 资费标准
     */
    @ExcelProperty(value = "资费标准")
    private String tariffStandard;

    /**
     * 资费方案编号
     */
    @ExcelProperty(value = "资费方案编号")
    private String tariffNo;

    /**
     * 资费类型
     */
    @ExcelProperty(value = "资费类型")
    private String tariffType;

    /**
     * 适用范围
     */
    @ExcelProperty(value = "适用范围")
    private String tariffScope;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String applicationArea;

    /**
     * 销售渠道
     */
    @ExcelProperty(value = "销售渠道")
    private String tariffChannel;

    /**
     * 上线日期
     */
    @ExcelProperty(value = "上线日期")
    private String onlineDate;

    /**
     * 下线日期
     */
    @ExcelProperty(value = "下线日期")
    private String offlineDate;

    /**
     * 有效期限
     */
    @ExcelProperty(value = "有效期限")
    private String validPeriod;

    /**
     * 在网要求
     */
    @ExcelProperty(value = "在网要求")
    private String onlineRequirement;

    /**
     * 退订方式
     */
    @ExcelProperty(value = "退订方式")
    private String unsubscribeMethod;

    /**
     * 违约责任
     */
    @ExcelProperty(value = "违约责任")
    private String breachResponsibility;

    /**
     * 服务内容
     */
    @ExcelProperty(value = "服务内容")
    private String serviceContent;

    /**
     * 其他说明
     */
    @ExcelProperty(value = "其他说明")
    private String others;

    /**
     * 资费来源
     */
    @ExcelProperty(value = "资费来源")
    private String tariffSource;

    /**
     * 定向流量
     */
    @ExcelProperty(value = "定向流量")
    private String targetedTraffic;

    /**
     * 宽带
     */
    @ExcelProperty(value = "宽带")
    private String broadband;

    /**
     * 移动高清
     */
    @ExcelProperty(value = "移动高清")
    private String mobileHd;

    /**
     * 通话
     */
    @ExcelProperty(value = "通话")
    private String call;

    /**
     * 通用流量
     */
    @ExcelProperty(value = "通用流量")
    private String generalFlows;

    /**
     * 权益
     */
    @ExcelProperty(value = "权益")
    private String rightsInterests;

    /**
     * 国际通话
     */
    @ExcelProperty(value = "国际通话")
    private String internationalCall;

    /**
     * 短彩信
     */
    @ExcelProperty(value = "短彩信")
    private String shortMms;

    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段")
    private String attr1;

    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段")
    private String attr2;

    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段")
    private String attr3;

    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段")
    private String attr4;

    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段")
    private String attr5;


}
