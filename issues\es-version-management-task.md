# ES版本号管理任务记录

## 任务背景

用户需要在Elasticsearch中实现版本号管理功能：
1. 删除`version_nos`数组中的指定版本号
2. 更新`version_no`字段为剩余版本号中的最大值  
3. 同步删除`date_ids`数组中对应的数据
4. 当`version_nos`仅有一个值且为待删除值时，删除整条记录

## 技术方案

采用Elasticsearch Update By Query API + Painless脚本实现：
- 原子性操作保证数据一致性
- 高性能批量处理
- 支持复杂的数组操作逻辑

## 实现要点

### 核心脚本逻辑
```javascript
// 删除指定版本号
ctx._source.version_nos.removeIf(v -> v.equals(targetVersion));

// 删除对应日期ID  
ctx._source.date_ids.removeIf(d -> d.equals(targetDateId));

// 更新version_no为最大版本号
if (!ctx._source.version_nos.isEmpty()) {
  ctx._source.version_no = ctx._source.version_nos.stream()
    .max(Comparator.naturalOrder())
    .orElse(null);
} else {
  ctx.op = 'delete'; // 删除整条记录
}
```

### 操作流程
1. 数据备份（创建快照）
2. 查询验证待处理数据
3. 执行删除单版本号文档
4. 更新多版本号文档
5. 验证操作结果

## 关键文件

- `docs/es-version-management.md` - 完整操作指南和HTTP请求示例

## 安全措施

1. 强制要求数据备份
2. 提供回滚方案
3. 分批处理避免性能问题
4. 详细的验证步骤

## 使用说明

用户需要：
1. 将文档中的`your_index`替换为实际索引名
2. 根据实际需求修改版本号和日期ID参数
3. 在测试环境先验证脚本
4. 执行前创建数据备份

## 扩展功能

文档还提供了：
- 批量删除多个版本号的方案
- 性能优化建议
- 异步执行方式
- 错误处理和故障排除指南
