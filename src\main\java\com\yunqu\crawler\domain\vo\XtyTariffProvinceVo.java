package com.yunqu.crawler.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.yunqu.crawler.domain.XtyTariffProvince;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 资费省份配置信息视图对象 xty_tariff_province
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = XtyTariffProvince.class)
public class XtyTariffProvinceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 省份代码
     */
    @ExcelProperty(value = "省份代码")
    private String provinceCode;

    /**
     * 省份名称
     */
    @ExcelProperty(value = "省份名称")
    private String provinceName;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long pId;

    /**
     * 省份简称
     */
    @ExcelProperty(value = "省份简称")
    private String abbreviation;

    /**
     * 拼音
     */
    @ExcelProperty(value = "拼音")
    private String pinyin;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Long number;

    /**
     * 资费专用省份编码
     */
    @ExcelProperty(value = "资费专用省份编码")
    private String tariffProvinceCode;

    /**
     * 省份全称
     */
    @ExcelProperty(value = "省份全称")
    private String provinceAllName;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String tariffProvinceName;


}
