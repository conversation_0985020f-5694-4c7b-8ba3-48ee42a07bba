package com.yunqu.crawler.domain.bo;

import com.yunqu.crawler.domain.XtyCrawlerTaskLog;
import com.yunqu.emergency.common.core.validate.EditGroup;
import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 信通院爬虫执行异常日志信息业务对象 xty_crawler_task_log
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = XtyCrawlerTaskLog.class, reverseConvertGenerate = false)
public class XtyCrawlerTaskLogBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 异常编码
     */
    private Integer errorCode;

    /**
     * 异常信息
     */
    private String errorMsg;

    /**
     * 源数据
     */
    private String sourceData;

    /**
     * 日期ID
     */
    private Integer dateId;

    /**
     * 运营商编码
     */
    private String entCode;

    /**
     * 运营商名称
     */
    private String entName;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 爬取省份名称
     */
    private String crawlerProvinceName;

    /**
     * 爬取类型
     */
    private String crawlerType;


}
