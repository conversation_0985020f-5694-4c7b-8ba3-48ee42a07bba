package com.yunqu.crawler.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.base.CacheConstants;
import com.yunqu.crawler.base.Constants;
import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.domain.vo.XtyTariffProvinceVo;
import com.yunqu.crawler.enums.OperatorEnum;
import com.yunqu.crawler.enums.RunErrorEnum;
import com.yunqu.crawler.error.CrawlerException;
import com.yunqu.crawler.event.CrawlerTaskEvent;
import com.yunqu.crawler.mapper.XtyCrawlerTaskMapper;
import com.yunqu.crawler.mapper.XtyTariffCrawlRecordMapper;
import com.yunqu.crawler.service.ICrawlerCallbackService;
import com.yunqu.crawler.service.ICrawlerTariffService;
import com.yunqu.crawler.service.IXtyTariffProvinceService;
import com.yunqu.emergency.common.core.exception.ServiceException;
import com.yunqu.emergency.common.core.utils.SpringUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.mybatis.annotation.Dynamic;
import com.yunqu.emergency.common.redis.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 广东电信资费爬虫服务实现类
 *
 * 主要功能：
 * 1. 处理广东电信资费数据的爬取回调
 * 2. 解析资费数据并存储到数据库
 * 3. 处理省份编码与名称的映射
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/5/16
 */
@RequiredArgsConstructor
@Slf4j
@Service(Constants.OPERATOR_GDT + ICrawlerTariffService.BASE_NAME)
public class BbroadnetCrawlerTariffServiceImpl implements ICrawlerTariffService, ICrawlerCallbackService {

    private static final String LOG_PREFIX = "[GDT]"; // 广东电信日志前缀

    // 常量定义
    private static final String FILE_NAME_SEPARATOR = "_";
    private static final String FILE_EXTENSION_SEPARATOR = ".";
    private static final int PROVINCE_CODE_START = 2;
    private static final int PROVINCE_CODE_END = 4;

    // JSON 字段常量
    private static final String FIELD_PACKAGE_NAME = "套餐名称";
    private static final String FIELD_BASIC_BUSINESS = "基础业务";
    private static final String FIELD_OTHER_BUSINESS = "其它业务";
    private static final String FIELD_DETAIL_INFO = "详细信息";

    // 详细信息字段
    private static final String FIELD_SERVICE_CONTENT = "服务内容";
    private static final String FIELD_SCOPE = "适用范围";
    private static final String FIELD_EFFECTIVE_DATE = "有效期限";
    private static final String FIELD_SALES_CHANNEL = "销售渠道";
    private static final String FIELD_ONLINE_DATE = "上线日期";
    private static final String FIELD_OFFLINE_DATE = "下线日期";
    private static final String FIELD_NET_REQUIREMENT = "在网要求";
    private static final String FIELD_RESPONSIBILITY = "违约责任";
    private static final String FIELD_UNSUBSCRIBE = "退订方式";

    // 基础业务字段前缀
    private static final String PREFIX_FEE = "费用";
    private static final String PREFIX_DOMESTIC_FLOW = "国内通用流量";
    private static final String PREFIX_DOMESTIC_CALL = "国内通话";
    private static final String PREFIX_DIRECTIONAL_FLOW = "定向流量";

    // 其他业务字段
    private static final String FIELD_TC = "套外资费";
    private static final String FIELD_RIGHTS = "权益";
    private static final String FIELD_BANDWIDTH = "带宽";
    private static final String FIELD_IPTV = "iptv";

    private final XtyCrawlerTaskMapper crawlerTaskMapper;
    private final IXtyTariffProvinceService tariffProvinceService;
    private final XtyTariffCrawlRecordMapper crawlerRecordMapper;

    @Value("${crawler.base-url}")
    private String crawlerBaseUrl;

    @Value("${crawler.gdt-provinces}")
    private String provinces;

    @Value("${crawler.gdt-provinces-name}")
    private String provincesName;

    @Value("${crawler.local-server}")
    private String localUrl;

    @Value("${file.tmp-path}")
    private String localFilePath;

    /**
     * 抓取数据入口方法
     *
     * @param dateId 日期ID
     */
    @Override
    public void crawler(int dateId, String versionNo) {
        log.info("{} [TASK-START] 开始资费数据抓取任务, dateId: {}", LOG_PREFIX, dateId);

        if (StringUtils.isBlank(provinces)) {
            log.error("{} [TASK-FAIL] 资费爬虫省份配置为空", LOG_PREFIX);
            return;
        }

        String[] provinceArray = StringUtils.split(provinces, ",");
        String[] provinceNameArray = StringUtils.split(provincesName, ",");
        log.info("{} [TASK-INFO] 需要抓取的省份数量: {}", LOG_PREFIX, provinceArray.length);
        for (int i = 0; i < provinceArray.length; i++) {
            String province = provinceArray[i];
            String provinceName = provinceNameArray[i];
            log.info("{} [TASK-PROCESS] 开始创建资费爬虫任务, 省份: {}", LOG_PREFIX, province);
            try {
                XtyCrawlerTask crawlerTask = new XtyCrawlerTask();
                crawlerTask.setDateId(dateId);
                crawlerTask.setProvinceCode(province);
                crawlerTask.setProvinceName(provinceName);
                crawlerTask.setStatus(0);
                crawlerTask.setEntType(5);
                crawlerTask.setOperatorCode(OperatorEnum.GDT.getAlias());
                crawlerTask.setOperatorName(OperatorEnum.GDT.getName());
                crawlerTask.setVersionNo(versionNo);
                crawlerTaskMapper.insert(crawlerTask);
                log.info("{} [TASK-SUCCESS] 成功创建资费爬虫任务, taskId: {}, 省份: {}", LOG_PREFIX, crawlerTask.getId(), province);
            } catch (Exception e) {
                log.error("{} [TASK-FAIL] 创建资费爬虫任务失败, 省份: {}, 错误: {}", LOG_PREFIX, province, e.getMessage(), e);
            }
        }

        log.info("{} [TASK-END] 完成资费数据抓取任务创建, dateId: {}", LOG_PREFIX, dateId);
    }

    /**
     * 执行请求爬虫
     *
     * @param task 爬虫任务
     */
    @Async
    @Override
    public void excueteRequestCrawler(XtyCrawlerTask task) {
        log.info("{} [CRAWLER-START] 开始执行资费爬虫请求, taskId: {}, 省份: {}", LOG_PREFIX, task.getId(), task.getProvinceCode());

        String operatorCode = task.getOperatorCode();
        String callback = localUrl + "/callback/" + operatorCode + "/" + task.getId();

        Map<String, String> map = new HashMap<>();
        map.put("callback_url", callback);
        map.put("provinces", task.getProvinceCode());
        map.put("crawler_type", OperatorEnum.GDT.getAlias());

        try {
            log.info("{} [CRAWLER-PROCESS] 发送爬虫请求, taskId: {}, url: {}, 参数: {}", LOG_PREFIX, task.getId(), crawlerBaseUrl + "/api/v1/crawl", map);
            requestToCrawler(crawlerBaseUrl + "/api/v1/crawl", JSONUtil.toJsonStr(map));
            log.info("{} [CRAWLER-SUCCESS] 成功发送爬虫请求, taskId: {}", LOG_PREFIX, task.getId());
        } catch (Exception e) {
            log.error("{} [CRAWLER-FAIL] 发送爬虫请求失败, taskId: {}, 错误: {}", LOG_PREFIX, task.getId(), e.getMessage(), e);
        }
    }

    /**
     * 处理爬虫回调数据
     * 主要步骤：
     * 1. 解压回调文件
     * 2. 遍历解压后的文件
     * 3. 解析每个文件的资费信息
     * 4. 更新任务状态
     *
     * @param taskId 任务ID
     * @param file   回调文件（ZIP格式）
     */
    @Override
    public void callback(Long taskId, MultipartFile file, String crawlerUrl) {
        String crawlerCallbackFileName = file.getOriginalFilename();
        log.info("{} [CALLBACK-START] 收到资费爬虫回调, taskId: {}, 文件名: {}", LOG_PREFIX, taskId, crawlerCallbackFileName);
        long startTime = System.currentTimeMillis();

        XtyCrawlerTask crawlerTask = crawlerTaskMapper.selectById(taskId);
        String tempDirPath = localFilePath + File.separator + taskId;
        try {
            SpringUtils.getAopProxy(this).excuete(taskId, crawlerTask.getVersionNo(), file, crawlerUrl, crawlerTask, crawlerCallbackFileName, tempDirPath, startTime);
        } catch (CrawlerException e) {
            crawlerTask.setStatus(0);
            crawlerTask.setMemo(e.getMessage());
            crawlerTaskMapper.updateById(crawlerTask);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        } finally {
            ICrawlerCallbackService.deleteDirectory(tempDirPath);
        }

    }

    @Transactional(rollbackFor = {Exception.class, RuntimeException.class, ServiceException.class, CrawlerException.class})
    @Dynamic(version = "versionNo")
    protected void excuete(Long taskId, String versionNo, MultipartFile file, String crawlerUrl, XtyCrawlerTask crawlerTask, String crawlerCallbackFileName, String tempDirPath, long startTime) throws IOException {
        String zipFileFolderPath = localFilePath + File.separator + crawlerTask.getVersionNo() + File.separator + OperatorEnum.GDT.getAlias();
        File zipFileFolder = new File(zipFileFolderPath);
        if (!zipFileFolder.exists()) zipFileFolder.mkdirs();
        String zipFilePath = zipFileFolderPath + File.separator + crawlerCallbackFileName;
        File zipFile = FileUtil.file(zipFilePath);
        file.transferTo(zipFile);
        File folder = new File(tempDirPath);
        ICrawlerCallbackService.unzipFile(zipFile, folder, System.currentTimeMillis());

        try {
            processUnzippedFiles(folder, crawlerTask, file.getOriginalFilename(), crawlerUrl, versionNo);
            log.info("{} [CALLBACK-SUCCESS] 完成资费爬虫回调处理, taskId: {}, 耗时: {}ms", LOG_PREFIX, taskId, System.currentTimeMillis() - startTime);
            SpringUtils.publishEvent(new CrawlerTaskEvent(taskId));
        } finally {
            ICrawlerCallbackService.deleteDirectory(folder);
            log.info("{} [CALLBACK-CLEANUP] 清理临时文件完成, 路径: {}", LOG_PREFIX, tempDirPath);
        }
    }

    /**
     * 处理解压后的文件
     */
    private void processUnzippedFiles(File folder, XtyCrawlerTask crawlerTask, String callbackFileName, String crawlerUrl, String versionNo) {
        if (!folder.isDirectory()) {
            log.warn("{} [FILE-WARN] 目标路径不是目录: {}", LOG_PREFIX, folder.getAbsolutePath());
            return;
        }

        File[] files = folder.listFiles();
        if (files == null) {
            log.warn("{} [FILE-WARN] 目录为空: {}", LOG_PREFIX, folder.getAbsolutePath());
            return;
        }

        if (files.length < 2) {
            crawlerTask.setStatus(0);
            crawlerTaskMapper.updateById(crawlerTask);
            log.info("{} [TASK-UPDATE] 状态更新成功, taskId: {}", LOG_PREFIX, crawlerTask.getId());
            return;
        }

        log.info("{} [FILE-START] 开始处理解压后的文件, 文件数量: {}", LOG_PREFIX, files.length);
        int successCount = 0;
        int failCount = 0;


        // String taskProvicneCode = crawlerTask.getProvinceCode();
        JSONObject provinceInfo = RedisUtils.getCacheMapValue(CacheConstants.GDT_PROVINCE_INFO_DICT_KEY, crawlerTask.getProvinceCode());
        String provinceCode = provinceInfo.getStr("provinceCode");
        String provinceName = provinceInfo.getStr("provinceName");
        String taskProvinceName = provinceInfo.getStr("name");
        int localProvinceCount = 0;
        int groupCount = 0;

        for (File tariffFile : files) {
            try {
                Map<String, Integer> map = processSingleTariffFile(tariffFile, crawlerTask, callbackFileName, crawlerUrl, versionNo, provinceCode, provinceName, taskProvinceName);
                localProvinceCount = localProvinceCount + map.get("localProvinceCount");
                groupCount = groupCount + map.get("groupCount");
                successCount++;
                log.debug("{} [FILE-SUCCESS] 成功处理资费文件: {}", LOG_PREFIX, tariffFile.getName());
            } catch (Exception e) {
                failCount++;
                log.error("{} [FILE-FAIL] 处理资费文件失败: {}, 错误: {}", LOG_PREFIX, tariffFile.getName(), e.getMessage(), e);
                throw new RuntimeException(e.getMessage(), e);
            }
        }

        if (localProvinceCount == 0 || groupCount == 0) {
            log.warn("{} [FILE-WARN] 本省资费或全网资费爬取异常, 忽略更新任务状态, 运营商：{}{}, localProvinceCount:{}, groupCount:{}", LOG_PREFIX, provincesName, OperatorEnum.GDT.getName(), localProvinceCount, groupCount);
            throw new CrawlerException("本省资费或全网资费爬取异常");
        }

        try {
            crawlerTask.setStatus(2);
            crawlerTask.setLocalProvinceCount(localProvinceCount);
            crawlerTask.setGroupCount(groupCount);
            crawlerTask.setCrawlerEndTime(new Date());
            crawlerTaskMapper.updateById(crawlerTask);
            log.info("{} [TASK-UPDATE] 更新任务状态完成, taskId: {}", LOG_PREFIX, crawlerTask.getId());
        } catch (Exception e) {
            log.error("{} [TASK-UPDATE-FAIL] 更新任务状态失败, taskId: {}, 错误: {}", LOG_PREFIX, crawlerTask.getId(), e.getMessage(), e);
            throw new RuntimeException(e.getMessage(), e);
        }

        log.info("{} [FILE-END] 完成文件处理, 成功: {}, 失败: {}", LOG_PREFIX, successCount, failCount);
    }

    /**
     * 处理单个资费文件
     */
    private Map<String, Integer> processSingleTariffFile(File tariffFile, XtyCrawlerTask crawlerTask, String callbackFileName, String crawlerUrl, String versionNo, String provinceCode, String provinceName, String taskProvinceName) {
        String fileName = tariffFile.getName();
        log.info("{} [FILE-START] 开始处理资费文件: {}", LOG_PREFIX, fileName);
        FileNameInfo fileNameInfo = parseFileName(fileName, provinceName, taskProvinceName);
        String content = readFileContent(tariffFile);
        if (StringUtils.isBlank(content)) {
            log.warn("{} [FILE-WARN] 文件内容为空: {}", LOG_PREFIX, fileName);
            return Map.of("localProvinceCount", 0, "groupCount", 0);
        }
        int count = 0;

        try {
            JSONObject entries = JSONUtil.parseObj(content);
            log.debug("{} [FILE-SUCCESS] 解析JSON内容成功, 文件: {}, 数据条数: {}", LOG_PREFIX, fileName, entries.size());
            for (String key : entries.keySet()) {
                try {
                    String[] classicType = StringUtils.split(key, FILE_NAME_SEPARATOR);
                    count = count + processClassicType(classicType, entries.getJSONArray(key), fileNameInfo, crawlerTask, callbackFileName, crawlerUrl, versionNo, provinceCode, provinceName);
                } catch (Exception e) {
                    log.error("{} [FILE-FAIL] 处理资费类型数据失败, key: {}, 错误: {}", LOG_PREFIX, key, e.getMessage(), e);
                    throw new RuntimeException(e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("{} [FILE-FAIL] 解析文件内容失败: {}, 错误: {}", LOG_PREFIX, fileName, e.getMessage(), e);
            throw e;
        }

        String crawlerType = fileNameInfo.crawlerType;
        int localProvinceCount = StringUtils.equals("全网资费", crawlerType) ? 0 : count;
        int groupCount = StringUtils.equals("全网资费", crawlerType) ? count : 0;
        return Map.of("localProvinceCount", localProvinceCount, "groupCount", groupCount);
    }

    /**
     * 处理经典类型数据
     */
    private int processClassicType(String[] classicType, JSONArray tariffDetailInfoList,
                                   FileNameInfo fileNameInfo, XtyCrawlerTask crawlerTask, String callbackFileName, String crawlerUrl, String versionNo, String provinceCode, String provinceName) {
        if (classicType.length < 2) {
            log.warn("{} [FILE-WARN] 无效的经典类型格式: {}", LOG_PREFIX, String.join(FILE_NAME_SEPARATOR, classicType));
            return 0;
        }

        String classicTypeOne = classicType[0];
        String classicTypeTwo = classicType[1];
        log.debug("{} [FILE-SUCCESS] 处理资费类型数据: classicTypeOne={}, classicTypeTwo={}", LOG_PREFIX, classicTypeOne, classicTypeTwo);

        int successCount = 0;

        for (int i = 0; i < tariffDetailInfoList.size(); i++) {
            try {
                JSONObject tariffDetailInfo = tariffDetailInfoList.getJSONObject(i);
                successCount = successCount + processBasicBusiness(tariffDetailInfo, fileNameInfo, crawlerTask,
                        callbackFileName, classicTypeOne, classicTypeTwo, crawlerUrl, versionNo, provinceCode, provinceName);
            } catch (Exception e) {
                log.error("{} [FILE-FAIL] 处理资费明细数据失败, index: {}, 错误: {}", LOG_PREFIX, i, e.getMessage(), e);
                throw new RuntimeException(e.getMessage(), e);
            }
        }

        log.info("{} [FILE-END] 完成资费类型数据处理, 类型: {}-{}, 成功: {}",
                LOG_PREFIX, classicTypeOne, classicTypeTwo, successCount);
        return successCount;
    }

    /**
     * 处理基础业务信息
     */
    private int processBasicBusiness(JSONObject tariffDetailInfo, FileNameInfo fileNameInfo,
                                     XtyCrawlerTask crawlerTask, String callbackFileName,
                                     String classicTypeOne, String classicTypeTwo, String crawlerUrl, String versionNo, String provinceCode, String provinceName) {
        JSONArray baseBusiArray = tariffDetailInfo.getJSONArray(FIELD_BASIC_BUSINESS);
        Object otherBusiObj = tariffDetailInfo.getObj(FIELD_OTHER_BUSINESS);
        JSONArray otherBusiArray = null;
        if (otherBusiObj instanceof JSONArray array) {
            otherBusiArray = array;
        }
        log.debug("{} [DATA-START] 处理基础业务信息, 套餐名称: {}", LOG_PREFIX, tariffDetailInfo.getStr(FIELD_PACKAGE_NAME));
        List<XtyTariffCrawlRecord> records = new ArrayList<>();
        for (int j = 0; j < baseBusiArray.size(); j++) {
            try {
                JSONObject baseBusi = baseBusiArray.getJSONObject(j);
                JSONObject otherBusi = null;
                if (otherBusiArray != null && !otherBusiArray.isEmpty()) {
                    otherBusi = otherBusiArray.getJSONObject(j);
                }
                XtyTariffCrawlRecord record = analysisTariffInfo(tariffDetailInfo, baseBusi, otherBusi,
                        fileNameInfo, callbackFileName, classicTypeOne, classicTypeTwo,
                        crawlerTask.getId(), crawlerTask.getDateId(), crawlerUrl, versionNo, provinceCode, provinceName);
                record.setTariffType(classicTypeOne);
                record.setExtendedFields(JSONUtil.toJsonStr(tariffDetailInfo));
                record.setProvinceCode(provinceCode);
                record.setProvinceName(fileNameInfo.provinceName);
                record.setCrawlerProvinceName(fileNameInfo.taskProvinceName);
                records.add(record);
            } catch (Exception e) {
                log.error("{} [DATA-FAIL] 处理基础业务数组数据失败, index: {}, 错误: {}", LOG_PREFIX, j, e.getMessage(), e);
                throw new RuntimeException(e.getMessage(), e);
            }
        }
        crawlerRecordMapper.insertBatch(records);
        return records.size();
        /*} else if (baseObject instanceof JSONObject baseBusiJson) {
            try {
                analysisTariffInfo(tariffDetailInfo, baseBusiJson,
                        fileNameInfo, callbackFileName, classicTypeOne, classicTypeTwo,
                        crawlerTask.getId(), crawlerTask.getDateId(), crawlerUrl, versionNo, provinceCode, provinceName);
            } catch (Exception e) {
                log.error("{} [DATA-FAIL] 处理基础业务对象数据失败, 错误: {}", LOG_PREFIX, e.getMessage(), e);
                throw new RuntimeException(e.getMessage(), e);
            }
            return 1;
        }*/
        //return 0;
    }

    /**
     * 解析资费信息并保存到数据库
     */
    private XtyTariffCrawlRecord analysisTariffInfo(JSONObject tariffDetailInfo, JSONObject baseBusiJson, JSONObject otherBusi,
                                                    FileNameInfo fileNameInfo, String callbackFileName,
                                                    String classicTypeOne, String classicTypeTwo, Long taskId, Integer dateId, String crawlerUrl, String versionNo, String provinceCode, String provinceName) {
        log.debug("{} [DATA-START] 开始解析资费信息, taskId: {}, 套餐名称: {}", LOG_PREFIX, taskId, tariffDetailInfo.getStr(FIELD_PACKAGE_NAME));

        XtyTariffCrawlRecord record = createBasicRecord(tariffDetailInfo, taskId, dateId);

        String tariffNo = StringUtils.trimToEmpty(baseBusiJson.getStr("编号"));
        parseAndSetProvince(record, tariffNo, taskId, dateId, JSONUtil.toJsonStr(tariffDetailInfo),
                OperatorEnum.GDT, fileNameInfo.provinceName, fileNameInfo.crawlerType);

        setOperatorInfo(record);
        parseDetailInfo(record, tariffDetailInfo.getJSONObject(FIELD_DETAIL_INFO));
        parseBasicBusinessInfo(record, baseBusiJson);
        parseOtherBusinessInfo(record, otherBusi);
        setCrawlerInfo(record, fileNameInfo, callbackFileName, classicTypeOne, classicTypeTwo);
        record.setCrawlUrl(crawlerUrl);
        record.setProvinceName(provinceName);
        record.setProvinceCode(provinceCode);

        try {
            record.setVersionNo(versionNo);
            // crawlerRecordMapper.insert(record);
            // log.debug("{} [DATA-SUCCESS] 资费数据入库成功, taskId: {}, tariffNo: {}", LOG_PREFIX, taskId, tariffNo);
        } catch (Exception e) {
            log.error("{} [DATA-FAIL] 资费数据入库失败, taskId: {}, tariffNo: {}, 错误: {}", LOG_PREFIX, taskId, tariffNo, e.getMessage(), e);
            throw e;
        }
        return record;
    }

    /**
     * 创建基础记录对象
     */
    private XtyTariffCrawlRecord createBasicRecord(JSONObject tariffDetailInfo, Long taskId, Integer dateId) {
        XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
        record.setTaskId(taskId);
        record.setDateId(dateId);
        record.setName(StringUtils.trimToEmpty(tariffDetailInfo.getStr(FIELD_PACKAGE_NAME)));
        return record;
    }

    /**
     * 设置运营商信息
     */
    private void setOperatorInfo(XtyTariffCrawlRecord record) {
        record.setEntCode(OperatorEnum.GDT.getCode());
        record.setEntName(OperatorEnum.GDT.getName());
    }

    /**
     * 解析详细信息
     */
    private void parseDetailInfo(XtyTariffCrawlRecord record, JSONObject detailInfo) {
        if (detailInfo == null) {
            log.debug("{} [DATA-WARN] 详细信息为空, recordId: {}", LOG_PREFIX, record.getId());
            return;
        }

        try {
            record.setOtherContent(StringUtils.trimToEmpty(detailInfo.getStr(FIELD_SERVICE_CONTENT)));
            record.setApplicablePeople(StringUtils.trimToEmpty(detailInfo.getStr(FIELD_SCOPE)));
            record.setValidPeriod(StringUtils.trimToEmpty(detailInfo.getStr(FIELD_EFFECTIVE_DATE)));
            record.setChannel(StringUtils.trimToEmpty(detailInfo.getStr(FIELD_SALES_CHANNEL)));
            String onlineDate = StringUtils.trimToEmpty(detailInfo.getStr(FIELD_ONLINE_DATE));
            record.setOfflineDay(formatDateStr(onlineDate));
            String offlineDate = StringUtils.trimToEmpty(detailInfo.getStr(FIELD_OFFLINE_DATE));
            record.setOfflineDay(formatDateStr(offlineDate));
            record.setDuration(StringUtils.trimToEmpty(detailInfo.getStr(FIELD_NET_REQUIREMENT)));
            record.setResponsibility(StringUtils.trimToEmpty(detailInfo.getStr(FIELD_RESPONSIBILITY)));
            record.setUnsubscribe(StringUtils.trimToEmpty(detailInfo.getStr(FIELD_UNSUBSCRIBE)));
            log.debug("{} [DATA-SUCCESS] 解析详细信息完成, recordId: {}", LOG_PREFIX, record.getId());
        } catch (Exception e) {
            log.error("{} [DATA-FAIL] 解析详细信息失败, recordId: {}, 错误: {}", LOG_PREFIX, record.getId(), e.getMessage(), e);
        }
    }

    /**
     * 解析基础业务信息
     */
    private void parseBasicBusinessInfo(XtyTariffCrawlRecord record, JSONObject baseBusiJson) {
        try {
            for (String baseBusiKey : baseBusiJson.keySet()) {
                if (StringUtils.isBlank(baseBusiKey)) {
                    continue;
                }

                String value = StringUtils.trimToEmpty(baseBusiJson.getStr(baseBusiKey));
                String unit = extractUnit(baseBusiKey);

                if (baseBusiKey.startsWith(PREFIX_FEE)) {
                    record.setFees(value);
                    record.setFeesUnit(unit);
                } else if (baseBusiKey.startsWith(PREFIX_DOMESTIC_FLOW)) {
                    record.setData(value);
                    record.setDataUnit(unit);
                } else if (baseBusiKey.startsWith(PREFIX_DOMESTIC_CALL)) {
                    record.setCall(value);
                } else if (baseBusiKey.startsWith(PREFIX_DIRECTIONAL_FLOW)) {
                    record.setOrientTraffic(value);
                    record.setOrientTrafficUnit(unit);
                }
            }
            log.debug("{} [DATA-SUCCESS] 解析基础业务信息完成, recordId: {}", LOG_PREFIX, record.getId());
        } catch (Exception e) {
            log.error("{} [DATA-FAIL] 解析基础业务信息失败, recordId: {}, 错误: {}", LOG_PREFIX, record.getId(), e.getMessage(), e);
        }
    }

    /**
     * 解析其他业务信息
     */
    private void parseOtherBusinessInfo(XtyTariffCrawlRecord record, JSONObject otherBusiJson) {
        if (otherBusiJson == null) {
            log.debug("{} [DATA-WARN] 其他业务信息为空, recordId: {}", LOG_PREFIX, record.getId());
            return;
        }

        try {
            record.setRights(StringUtils.trimToEmpty(otherBusiJson.getStr(FIELD_RIGHTS)));
            record.setBandwidth(StringUtils.trimToEmpty(otherBusiJson.getStr(FIELD_BANDWIDTH)));
            record.setIptv(StringUtils.trimToEmpty(otherBusiJson.getStr(FIELD_IPTV)));
            log.debug("{} [DATA-SUCCESS] 解析其他业务信息完成, recordId: {}", LOG_PREFIX, record.getId());
        } catch (Exception e) {
            log.error("{} [DATA-FAIL] 解析其他业务信息失败, recordId: {}, 错误: {}", LOG_PREFIX, record.getId(), e.getMessage(), e);
        }
    }

    /**
     * 设置爬虫相关信息
     */
    private void setCrawlerInfo(XtyTariffCrawlRecord record, FileNameInfo fileNameInfo,
                                String callbackFileName, String classicTypeOne, String classicTypeTwo) {
        record.setCrawlerProvinceName(fileNameInfo.provinceName);
        record.setCrawlerType(fileNameInfo.crawlerType);
        record.setCrawlerCallbackFileName(callbackFileName);
        record.setCrawlerDataBelongFileName(fileNameInfo.originalFileName);
        record.setClassicTypeOne(classicTypeOne);
        record.setClassicTypeTwo(classicTypeTwo);
    }

    /**
     * 从字符串中提取单位信息
     */
    private String extractUnit(String text) {
        if (StringUtils.isBlank(text)) {
            return "";
        }
        Pattern pattern = Pattern.compile("\\((.*?)\\)");
        Matcher matcher = pattern.matcher(text);
        return matcher.find() ? matcher.group(1) : "";
    }

    /**
     * 解析文件名信息
     */
    private FileNameInfo parseFileName(String fileName, String provinceName, String taskProvinceName) {
        log.debug("{} [FILE-START] 开始解析文件名: {}", LOG_PREFIX, fileName);
        FileNameInfo info = new FileNameInfo();
        info.originalFileName = fileName;
        info.provinceName = provinceName;
        info.taskProvinceName = taskProvinceName;
        try {
            String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf(FILE_EXTENSION_SEPARATOR));
            String[] parts = nameWithoutExt.split(FILE_NAME_SEPARATOR);

            if (parts.length > 1) {
                String crawlerType = parts[parts.length - 1];
                if (!StringUtils.equals("全网资费", crawlerType)) {
                    crawlerType = provinceName + "资费";
                }
                info.crawlerType = crawlerType;
            }
            log.debug("{} [FILE-SUCCESS] 文件名解析完成: provinceName={}, crawlerType={}", LOG_PREFIX, info.provinceName, info.crawlerType);
        } catch (Exception e) {
            log.error("{} [FILE-FAIL] 解析文件名失败: {}, 错误: {}", LOG_PREFIX, fileName, e.getMessage(), e);
        }

        return info;
    }

    /**
     * 统一省份解析和赋值
     */
    private void parseAndSetProvince(XtyTariffCrawlRecord record, String tariffNo, Long taskId, Integer dateId,
                                     String rawJson, OperatorEnum operator, String crawlerProvinceName, String crawlerType) {
        if (StringUtils.isBlank(tariffNo) || tariffNo.length() < PROVINCE_CODE_END) {
            log.error("{} [DATA-FAIL] 资费方案编码为空或长度不足: {}", LOG_PREFIX, tariffNo);
            recordErrorLog(operator, RunErrorEnum.EMPTY_TARIFF_NO, taskId, dateId, crawlerProvinceName, crawlerType,
                    rawJson);
            return;
        }

        try {
            String tariffProvinceCode = tariffNo.substring(PROVINCE_CODE_START, PROVINCE_CODE_END);
            XtyTariffProvinceVo tariffProvince = tariffProvinceService.getByTariffProvinceCode(tariffProvinceCode);

            record.setTariffNo(tariffNo);
            record.setTariffProvinceCode(tariffProvinceCode);
            record.setTariffProvinceName(tariffProvince.getTariffProvinceName());
            record.setProvinceCode(tariffProvince.getProvinceCode());
            record.setProvinceName(tariffProvince.getProvinceName());
            log.debug("{} [DATA-SUCCESS] 省份解析成功, tariffNo: {}, 省份: {}", LOG_PREFIX, tariffNo, tariffProvince.getProvinceName());
        } catch (Exception e) {
            log.error("{} [DATA-FAIL] 省份解析失败: {}, 错误: {}", LOG_PREFIX, tariffNo, e.getMessage(), e);
            recordErrorLog(operator, RunErrorEnum.ERROR_TARIFF_NO, taskId, dateId, crawlerProvinceName, crawlerType,
                    rawJson);
        }
    }

    /**
     * 文件名信息存储类
     */
    private static class FileNameInfo {
        String originalFileName;
        String provinceName;
        String crawlerType;
        String taskProvinceName;
    }
}
