package com.yunqu.crawler.service;

import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.emergency.common.core.exception.ServiceException;
import com.yunqu.emergency.common.core.utils.SpringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.hutool.http.HttpRequest;

/**
 * <p>
 *
 * </p>
 *
 * @ClassName ICrawlerTariffService
 * <AUTHOR> Copy This Tag)
 * @Description TODO 描述文件用途
 * @Since create in 2025/5/15 17:49
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
public interface ICrawlerTariffService {

    Logger log = LoggerFactory.getLogger(ICrawlerTariffService.class);

    String BASE_NAME = "CrawlerTariffService";

    /**
     * 统一的资费数据抓取入口
     * 根据运营商类型分发到对应的实现类处理
     *
     * @param dateId       日期ID
     * @param operatorType 运营商类型
     */
    static String crawler(int dateId, String operatorType, String versionNo) {
        String beanName = operatorType + BASE_NAME;
        log.info("[CRAWLER-START] 开始资费数据抓取任务, dateId: {}, operatorType: {}", dateId, operatorType);

        try {
            if (!SpringUtils.containsBean(beanName)) {
                log.error("[CRAWLER-ERROR] 无效的运营商类型: {}, beanName: {}", operatorType, beanName);
                throw new ServiceException("无效的运营商类型: " + operatorType);
            }

            ICrawlerTariffService instance = SpringUtils.getBean(beanName);
            instance.crawler(dateId, versionNo);

            /*
             * long idx = RedisUtils.incrAtomicValue("sys:crawler:version:order");
             * XtyCrawlerVersionBo version = new XtyCrawlerVersionBo();
             * version.setVersionNo(versionNo);
             * version.setIdx(idx);
             * IXtyCrawlerVersionService versionService =
             * SpringUtils.getBean(IXtyCrawlerVersionService.class);
             * versionService.insertByBo(version);
             */

            log.info("[CRAWLER-SUCCESS] 完成资费数据抓取任务, dateId: {}, operatorType: {}", dateId, operatorType);
        } catch (Exception e) {
            log.error("[CRAWLER-FAIL] 资费数据抓取任务异常, dateId: {}, operatorType: {}, 错误: {}",
                    dateId, operatorType, e.getMessage(), e);
            throw new ServiceException("资费数据抓取任务异常: " + e.getMessage());
        }
        return null;
    }

    /**
     * 抓取数据
     * 由各运营商实现类实现具体的抓取逻辑
     *
     * @param dateId 日期ID
     */
    void crawler(int dateId, String versionNo);

    /**
     * 执行爬虫请求
     * 由各运营商实现类实现具体的请求逻辑
     *
     * @param task 爬虫任务
     */
    void excueteRequestCrawler(XtyCrawlerTask task);

    /**
     * 发送HTTP请求到爬虫服务
     * 统一的HTTP请求处理，包含重试和错误处理
     *
     * @param url         请求地址
     * @param requestBody 请求体
     * @return 响应结果
     */
    default String requestToCrawler(String url, String requestBody) {
        log.info("[HTTP-START] 开始发送爬虫请求, url: {}, body: {}", url, requestBody);
        long startTime = System.currentTimeMillis();

        try {
            String responseResult = HttpRequest.post(url)
                    .setConnectionTimeout(3000)
                    .body(requestBody)
                    .execute()
                    .body();

            log.info("[HTTP-SUCCESS] 完成爬虫请求, url: {}, 耗时: {}ms", url, System.currentTimeMillis() - startTime);
            log.debug("[HTTP-RESPONSE] 爬虫响应内容: {}", responseResult);
            return responseResult;
        } catch (Exception e) {
            log.error("[HTTP-FAIL] 爬虫请求异常, url: {}, body: {}, 错误: {}", url, requestBody, e.getMessage(), e);
            throw new ServiceException("爬虫请求异常: " + e.getMessage());
        }
    }
}
