package com.yunqu.crawler.service;

import com.yunqu.crawler.domain.bo.XtyTariffCrawlRecordLibBo;
import com.yunqu.crawler.domain.vo.XtyTariffCrawlRecordLibVo;
import com.yunqu.emergency.common.mybatis.core.page.TableDataInfo;
import com.yunqu.emergency.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 信通院资费爬虫数据Service接口
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface IXtyTariffCrawlRecordLibService {

    /**
     * 查询信通院资费爬虫数据
     *
     * @param id 主键
     * @return 信通院资费爬虫数据
     */
    XtyTariffCrawlRecordLibVo queryById(Long id);

    /**
     * 分页查询信通院资费爬虫数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 信通院资费爬虫数据分页列表
     */
    TableDataInfo<XtyTariffCrawlRecordLibVo> queryPageList(XtyTariffCrawlRecordLibBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的信通院资费爬虫数据列表
     *
     * @param bo 查询条件
     * @return 信通院资费爬虫数据列表
     */
    List<XtyTariffCrawlRecordLibVo> queryList(XtyTariffCrawlRecordLibBo bo);

    /**
     * 新增信通院资费爬虫数据
     *
     * @param bo 信通院资费爬虫数据
     * @return 是否新增成功
     */
    Boolean insertByBo(XtyTariffCrawlRecordLibBo bo);

    /**
     * 修改信通院资费爬虫数据
     *
     * @param bo 信通院资费爬虫数据
     * @return 是否修改成功
     */
    Boolean updateByBo(XtyTariffCrawlRecordLibBo bo);

    /**
     * 校验并批量删除信通院资费爬虫数据信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
