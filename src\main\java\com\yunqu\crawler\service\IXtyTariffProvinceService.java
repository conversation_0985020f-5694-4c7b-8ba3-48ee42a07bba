package com.yunqu.crawler.service;

import com.yunqu.crawler.domain.bo.XtyTariffProvinceBo;
import com.yunqu.crawler.domain.vo.XtyTariffProvinceVo;
import com.yunqu.emergency.common.mybatis.core.page.TableDataInfo;
import com.yunqu.emergency.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 资费省份配置信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface IXtyTariffProvinceService {

    /**
     * 查询资费省份配置信息
     *
     * @param id 主键
     * @return 资费省份配置信息
     */
    XtyTariffProvinceVo queryById(Long id);

    /**
     * 分页查询资费省份配置信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 资费省份配置信息分页列表
     */
    TableDataInfo<XtyTariffProvinceVo> queryPageList(XtyTariffProvinceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的资费省份配置信息列表
     *
     * @param bo 查询条件
     * @return 资费省份配置信息列表
     */
    List<XtyTariffProvinceVo> queryList(XtyTariffProvinceBo bo);

    /**
     * 新增资费省份配置信息
     *
     * @param bo 资费省份配置信息
     * @return 是否新增成功
     */
    Boolean insertByBo(XtyTariffProvinceBo bo);

    /**
     * 修改资费省份配置信息
     *
     * @param bo 资费省份配置信息
     * @return 是否修改成功
     */
    Boolean updateByBo(XtyTariffProvinceBo bo);

    /**
     * 校验并批量删除资费省份配置信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据tariffProvinceCode查询资费省份配置信息
     *
     * @param tariffProvinceCode 资费省份编码
     * @return 资费省份配置信息
     */
    XtyTariffProvinceVo getByTariffProvinceCode(String tariffProvinceCode);
}
