package com.yunqu.crawler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunqu.crawler.domain.XtyCrawlerVersionConfig;
import com.yunqu.crawler.domain.bo.XtyCrawlerVersionConfigBo;
import com.yunqu.crawler.domain.vo.XtyCrawlerVersionConfigVo;
import com.yunqu.crawler.mapper.XtyCrawlerVersionConfigMapper;
import com.yunqu.crawler.service.IXtyCrawlerVersionConfigService;
import com.yunqu.emergency.common.core.utils.MapstructUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.mybatis.core.page.PageQuery;
import com.yunqu.emergency.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 爬虫版本生成配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@RequiredArgsConstructor
@Service
public class XtyCrawlerVersionConfigServiceImpl extends ServiceImpl<XtyCrawlerVersionConfigMapper, XtyCrawlerVersionConfig> implements IXtyCrawlerVersionConfigService {

    /**
     * 查询爬虫版本生成配置
     *
     * @param id 主键
     * @return 爬虫版本生成配置
     */
    @Override
    public XtyCrawlerVersionConfigVo queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询爬虫版本生成配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 爬虫版本生成配置分页列表
     */
    @Override
    public TableDataInfo<XtyCrawlerVersionConfigVo> queryPageList(XtyCrawlerVersionConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<XtyCrawlerVersionConfig> lqw = buildQueryWrapper(bo);
        Page<XtyCrawlerVersionConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的爬虫版本生成配置列表
     *
     * @param bo 查询条件
     * @return 爬虫版本生成配置列表
     */
    @Override
    public List<XtyCrawlerVersionConfigVo> queryList(XtyCrawlerVersionConfigBo bo) {
        LambdaQueryWrapper<XtyCrawlerVersionConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<XtyCrawlerVersionConfig> buildQueryWrapper(XtyCrawlerVersionConfigBo bo) {
        LambdaQueryWrapper<XtyCrawlerVersionConfig> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(XtyCrawlerVersionConfig::getId);
        lqw.like(StringUtils.isNotBlank(bo.getConfigName()), XtyCrawlerVersionConfig::getConfigName, bo.getConfigName());
        lqw.eq(StringUtils.isNotBlank(bo.getConfigType()), XtyCrawlerVersionConfig::getConfigType, bo.getConfigType());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleValue()), XtyCrawlerVersionConfig::getRuleValue, bo.getRuleValue());
        lqw.eq(StringUtils.isNotBlank(bo.getIsEnabled()), XtyCrawlerVersionConfig::getIsEnabled, bo.getIsEnabled());
        lqw.eq(bo.getPriorityOrder() != null, XtyCrawlerVersionConfig::getPriorityOrder, bo.getPriorityOrder());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), XtyCrawlerVersionConfig::getDescription, bo.getDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateUser()), XtyCrawlerVersionConfig::getCreateUser, bo.getCreateUser());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdateUser()), XtyCrawlerVersionConfig::getUpdateUser, bo.getUpdateUser());
        return lqw;
    }

    /**
     * 新增爬虫版本生成配置
     *
     * @param bo 爬虫版本生成配置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(XtyCrawlerVersionConfigBo bo) {
        XtyCrawlerVersionConfig add = MapstructUtils.convert(bo, XtyCrawlerVersionConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改爬虫版本生成配置
     *
     * @param bo 爬虫版本生成配置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(XtyCrawlerVersionConfigBo bo) {
        XtyCrawlerVersionConfig update = MapstructUtils.convert(bo, XtyCrawlerVersionConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(XtyCrawlerVersionConfig entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除爬虫版本生成配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
