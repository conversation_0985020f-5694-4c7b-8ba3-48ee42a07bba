package com.yunqu.crawler.service.impl.telecom;

import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.base.StringAmountUnitParser;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.util.ParseDateUtil;
import com.yunqu.emergency.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  吉林电信流量包解析器
 * </p>
 *
 * @ClassName JlTelecomRecordAnalysisHandler
 * <AUTHOR> Copy This Tag)
 * @Description 吉林电信流量包解析器，实现IRecordAnalysisHandler接口
 * @Since create in 2025/5/29 10:45
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@Slf4j
public class JlTelecomRecordAnalysisHandler implements IRecordAnalysisHandler {

    @Override
    public List<XtyTariffCrawlRecord> analysis(String content) {
        if (StringUtils.isBlank(content))
            return List.of();

        List<Element> elements = parseListBoxDivs(content);
        if (elements == null || elements.isEmpty())
            return List.of();
        List<XtyTariffCrawlRecord> result = new ArrayList<>();
        for (Element element : elements) {
            JSONObject json = parseTariffRecordToJson(element);
            XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
            record.setExtendedFields(JSONUtil.toJsonStr(json));
            JSONObject top = json.getJSONObject("top");
            if (top == null)
                continue;
            record.setName(StringUtils.trimToEmpty(top.getStr("title")));
            record.setTariffNo(StringUtils.trimToEmpty(top.getStr("planNumber")));
            JSONObject table = json.getJSONObject("table");
            if (table != null) {
                JSONObject basicInfo = table.getJSONObject("basicInfo");
                if (basicInfo != null) {
                    String fees = StringUtils.trimToEmpty(basicInfo.getStr("remark"));
                    if (StringUtils.isNotEmpty(fees)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(fees);
                        if (parse != null) {
                            record.setFees(parse.getAmount());
                            record.setFeesUnit(parse.getUnit());
                        }
                    }

                    record.setApplicablePeople(StringUtils.trimToEmpty(basicInfo.getStr("适用范围")));
                    record.setChannel(StringUtils.trimToEmpty(basicInfo.getStr("销售渠道")));

                    String upDownDay = StringUtils.trimToEmpty(basicInfo.getStr("上下线时间"));
                    if (StringUtils.isNotEmpty(upDownDay)) {
                        String[] split = upDownDay.split("至");
                        if (split.length == 2) {
                            record.setOnlineDay(ParseDateUtil.formatDateStr(split[0]));
                            record.setOfflineDay(ParseDateUtil.formatDateStr(split[1]));
                        }
                    }

                    record.setValidPeriod(StringUtils.trimToEmpty(basicInfo.getStr("有效期限")));
                    record.setUnsubscribe(StringUtils.trimToEmpty(basicInfo.getStr("退订方式")));
                    record.setDuration(StringUtils.trimToEmpty(basicInfo.getStr("在网要求")));
                    record.setResponsibility(StringUtils.trimToEmpty(basicInfo.getStr("违约责任")));
                }

                JSONObject tableOut = table.getJSONObject("tableOut");
                if (tableOut != null) {
                    String callInfo = StringUtils.trimToEmpty(tableOut.getStr("语音"));
                    if (StringUtils.isNotBlank(callInfo)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(callInfo);
                        if (parse != null) {
                            record.setCall(parse.getAmount());
                        }
                    }

                    String dataInfo = StringUtils.trimToEmpty(tableOut.getStr("通用流量"));
                    if (StringUtils.isNotBlank(dataInfo)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(dataInfo);
                        if (parse != null) {
                            record.setData(parse.getAmount());
                            record.setDataUnit(parse.getUnit());
                        }
                    }

                    record.setBandwidth(StringUtils.trimToEmpty(tableOut.getStr("宽带")));
                    record.setIptv(StringUtils.trimToEmpty(tableOut.getStr("IPTV")));

                    String smsInfo = StringUtils.trimToEmpty(tableOut.getStr("短彩信"));
                    if (StringUtils.isNotBlank(smsInfo)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(smsInfo);
                        if (parse != null) {
                            record.setSms(parse.getAmount());
                        }
                    }

                    // 定向流量处理
                    String dxll = StringUtils.trimToEmpty(tableOut.getStr("定向流量"));
                    if (StringUtils.isNotBlank(dxll)) {
                        StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(dxll);
                        if (parse != null) {
                            record.setOrientTraffic(parse.getAmount());
                            record.setOrientTrafficUnit(parse.getUnit());
                        }
                    }

                    record.setRights(StringUtils.trimToEmpty(tableOut.getStr("权益")));
                }

                JSONObject remark = table.getJSONObject("remark");
                if (remark != null) {
                    record.setOtherContent(StringUtils.trimToEmpty(remark.getStr("content")));
                }

                result.add(record);
            }
        }

        return result;
    }

    /**
     * Parse HTML content and extract div elements with class 'list-box' and their
     * child divs
     *
     * @param htmlContent the HTML content to parse
     * @return List of child div elements found within the list-box div
     */
    public List<Element> parseListBoxDivs(String htmlContent) {
        if (StringUtils.isEmpty(htmlContent)) {
            return new ArrayList<>();
        }

        try {
            // Clean script tags using regex first
            System.out.println(StringUtils.length(htmlContent));
            String cleanedHtml = htmlContent;
            // Remove single line script tags
            cleanedHtml = cleanedHtml.replaceAll("<script.*?</script>", "");
            // Remove multi-line script tags
            cleanedHtml = cleanedHtml.replaceAll("(?s)<script.*?</script>", "");
            // Remove self-closing script tags
            cleanedHtml = cleanedHtml.replaceAll("<script.*?/>", "");
            // Remove any remaining script tags
            cleanedHtml = cleanedHtml.replaceAll("<script.*?>", "");

            System.out.println(StringUtils.length(cleanedHtml));
            // System.out.println(cleanedHtml);
            // Parse the cleaned HTML
            Document doc = Jsoup.parse(cleanedHtml);

            // Find div.list-box within div.index-content
            Element indexContent = doc.selectFirst("div.index-content");
            if (indexContent != null) {
                Element listBoxDiv = indexContent.selectFirst("div.list-box");
                if (listBoxDiv != null) {
                    // 只选择list-box的直接子div
                    Elements tariffDivs = listBoxDiv.children();
                    // 过滤出包含资费信息的div（包含top或table类的div的父div）
                    return tariffDivs.stream()
                            .filter(div -> !div.select("> div.top, > div.table").isEmpty())
                            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return new ArrayList<>();
    }

    /**
     * Parse a single tariff record div and convert it to JSON maintaining original
     * structure
     *
     * @param element the div element to parse
     * @return JSONObject containing the parsed information
     */
    public JSONObject parseTariffRecordToJson(Element element) {
        JSONObject json = new JSONObject();

        try {
            // Top section
            Element topDiv = element.selectFirst("div.top");
            if (topDiv != null) {
                JSONObject top = new JSONObject();
                Element title = topDiv.selectFirst("p.title");
                Element planNumber = topDiv.selectFirst("p.small");
                if (title != null) {
                    top.put("title", title.text());
                }
                if (planNumber != null) {
                    top.put("planNumber", planNumber.text());
                }
                json.put("top", top);
            }

            // Table section
            Element tableDiv = element.selectFirst("div.table");
            if (tableDiv != null) {
                JSONObject table = new JSONObject();

                // Basic information section
                Element textDiv = tableDiv.selectFirst("div.text");
                if (textDiv != null) {
                    JSONObject basicInfo = new JSONObject();
                    Elements infoDivs = textDiv.select("div.clearfix > div");

                    // Process all info divs and merge them into basicInfo
                    for (Element infoDiv : infoDivs) {
                        Elements items = infoDiv.select("p");
                        for (Element item : items) {
                            String text = item.text();
                            if (text.contains("：")) {
                                String[] parts = text.split("：", 2);
                                basicInfo.put(parts[0].trim(), parts[1].trim());
                            }
                        }
                    }

                    table.put("basicInfo", basicInfo);
                }

                // Service content section
                Element serviceContent = tableDiv.selectFirst("p.s-title:contains(服务内容)");
                if (serviceContent != null) {
                    table.put("serviceTitle", serviceContent.text());
                }

                // Table out section
                Element tableOutDiv = tableDiv.selectFirst("div.table-out");
                if (tableOutDiv != null) {
                    JSONObject tableOut = new JSONObject();
                    Element tableElement = tableOutDiv.selectFirst("table");
                    if (tableElement != null) {
                        // 获取表头作为key
                        Elements headers = tableElement.select("tr:first-child th");
                        // 获取数据行
                        Elements dataRows = tableElement.select("tr:not(:first-child)");

                        if (!dataRows.isEmpty()) {
                            Elements dataCells = dataRows.get(0).select("td");
                            for (int i = 0; i < headers.size() && i < dataCells.size(); i++) {
                                String headerText = headers.get(i).text().trim();
                                String cellText = dataCells.get(i).text().trim();
                                if (StringUtils.isNotEmpty(headerText) && StringUtils.isNotEmpty(cellText)) {
                                    tableOut.put(headerText, cellText);
                                }
                            }
                        }
                    }
                    table.put("tableOut", tableOut);
                }

                // Remark section
                Element remarkDiv = tableDiv.selectFirst("div.remark");
                if (remarkDiv != null) {
                    JSONObject remark = new JSONObject();
                    Element p2 = remarkDiv.selectFirst("p.p2");
                    if (p2 != null) {
                        remark.put("content", p2.text());
                    }
                    table.put("remark", remark);
                }

                // More section
                Element moreDiv = tableDiv.selectFirst("div.more");
                if (moreDiv != null) {
                    JSONObject more = new JSONObject();
                    Element link = moreDiv.selectFirst("a");
                    if (link != null) {
                        more.put("link", link.attr("href"));
                        Element text = link.selectFirst("p");
                        if (text != null) {
                            more.put("text", text.text());
                        }
                    }
                    table.put("more", more);
                }

                json.put("table", table);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            json.put("error", "Failed to parse element: " + e.getMessage());
        }

        return json;
    }

    public static void main(String[] args) throws FileNotFoundException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\中国电信\\html\\ln\\辽宁资费_20250529_093203_7251_套餐.json";
        String read = IoUtil.read(new FileInputStream(filePath), StandardCharsets.UTF_8);
        HbTelecomRecordAnalysisHandler handler = new HbTelecomRecordAnalysisHandler();
        List<Element> elements = handler.parseListBoxDivs(read);

        JSONArray results = new JSONArray();
        for (Element element : elements) {
            JSONObject json = handler.parseTariffRecordToJson(element);
            if (!json.isEmpty()) {
                results.add(json);
            }
        }
        System.out.println(results.toStringPretty());
    }
}
