package com.yunqu.crawler.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.yunqu.crawler.domain.XtyTariffUnicomRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 信通院-中国联通公示资费数据视图对象 xty_tariff_unicom_record
 *
 * <AUTHOR>
 * @date 2025-05-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = XtyTariffUnicomRecord.class)
public class XtyTariffUnicomRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 任务ID
     */
    @ExcelProperty(value = "任务ID")
    private Long taskId;

    /**
     * 资费名称
     */
    @ExcelProperty(value = "资费名称")
    private String tariffName;

    /**
     * 资费方案编号
     */
    @ExcelProperty(value = "资费方案编号")
    private String tariffNo;

    /**
     * 资费标准
     */
    @ExcelProperty(value = "资费标准")
    private String tariffFee;

    /**
     * 服务内容
     */
    @ExcelProperty(value = "服务内容")
    private String serviceContent;

    /**
     * 适用范围
     */
    @ExcelProperty(value = "适用范围")
    private String scope;

    /**
     * 有效期限
     */
    @ExcelProperty(value = "有效期限")
    private String validPeriod;

    /**
     * 销售渠道
     */
    @ExcelProperty(value = "销售渠道")
    private String tariffChannel;

    /**
     * 上线日期
     */
    @ExcelProperty(value = "上线日期")
    private String onlineDate;

    /**
     * 下线日期
     */
    @ExcelProperty(value = "下线日期")
    private String offlineDate;

    /**
     * 在网要求
     */
    @ExcelProperty(value = "在网要求")
    private String onlineRequirement;

    /**
     * 退订方式
     */
    @ExcelProperty(value = "退订方式")
    private String unsubscribeMethod;

    /**
     * 违约责任
     */
    @ExcelProperty(value = "违约责任")
    private String breachResponsibility;

    /**
     * 其他说明
     */
    @ExcelProperty(value = "其他说明")
    private String others;

    /**
     * 活动收费标准
     */
    @ExcelProperty(value = "活动收费标准")
    private String activityFeeStandard;

    /**
     * 活动内容及规则
     */
    @ExcelProperty(value = "活动内容及规则")
    private String activityContent;

    /**
     * 活动范围
     */
    @ExcelProperty(value = "活动范围")
    private String activityScope;

    /**
     * 国内语音拨打分钟数
     */
    @ExcelProperty(value = "国内语音拨打分钟数")
    private String domesticVoiceCallMin;

    /**
     * 国内流量
     */
    @ExcelProperty(value = "国内流量")
    private String domesticTraffic;

    /**
     * 增值业务
     */
    @ExcelProperty(value = "增值业务")
    private String valueAddedService;

    /**
     * 权益
     */
    @ExcelProperty(value = "权益")
    private String rightsInterests;

    /**
     * 定向流量
     */
    @ExcelProperty(value = "定向流量")
    private String targetedTraffic;


}
