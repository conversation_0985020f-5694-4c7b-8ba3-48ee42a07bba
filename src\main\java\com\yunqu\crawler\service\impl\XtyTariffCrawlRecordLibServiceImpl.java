package com.yunqu.crawler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunqu.crawler.domain.XtyTariffCrawlRecordLib;
import com.yunqu.crawler.domain.bo.XtyTariffCrawlRecordLibBo;
import com.yunqu.crawler.domain.vo.XtyTariffCrawlRecordLibVo;
import com.yunqu.crawler.mapper.XtyTariffCrawlRecordLibMapper;
import com.yunqu.crawler.service.IXtyTariffCrawlRecordLibService;
import com.yunqu.emergency.common.core.utils.MapstructUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.mybatis.core.page.PageQuery;
import com.yunqu.emergency.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 信通院资费爬虫数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@RequiredArgsConstructor
@Service
public class XtyTariffCrawlRecordLibServiceImpl implements IXtyTariffCrawlRecordLibService {

    private final XtyTariffCrawlRecordLibMapper baseMapper;

    /**
     * 查询信通院资费爬虫数据
     *
     * @param id 主键
     * @return 信通院资费爬虫数据
     */
    @Override
    public XtyTariffCrawlRecordLibVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询信通院资费爬虫数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 信通院资费爬虫数据分页列表
     */
    @Override
    public TableDataInfo<XtyTariffCrawlRecordLibVo> queryPageList(XtyTariffCrawlRecordLibBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<XtyTariffCrawlRecordLib> lqw = buildQueryWrapper(bo);
        Page<XtyTariffCrawlRecordLibVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的信通院资费爬虫数据列表
     *
     * @param bo 查询条件
     * @return 信通院资费爬虫数据列表
     */
    @Override
    public List<XtyTariffCrawlRecordLibVo> queryList(XtyTariffCrawlRecordLibBo bo) {
        LambdaQueryWrapper<XtyTariffCrawlRecordLib> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<XtyTariffCrawlRecordLib> buildQueryWrapper(XtyTariffCrawlRecordLibBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<XtyTariffCrawlRecordLib> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(XtyTariffCrawlRecordLib::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getTariffRecordId()), XtyTariffCrawlRecordLib::getTariffRecordId, bo.getTariffRecordId());
        lqw.eq(bo.getTaskId() != null, XtyTariffCrawlRecordLib::getTaskId, bo.getTaskId());
        lqw.eq(StringUtils.isNotBlank(bo.getProvinceCode()), XtyTariffCrawlRecordLib::getProvinceCode, bo.getProvinceCode());
        lqw.like(StringUtils.isNotBlank(bo.getProvinceName()), XtyTariffCrawlRecordLib::getProvinceName, bo.getProvinceName());
        lqw.eq(StringUtils.isNotBlank(bo.getEntCode()), XtyTariffCrawlRecordLib::getEntCode, bo.getEntCode());
        lqw.like(StringUtils.isNotBlank(bo.getEntName()), XtyTariffCrawlRecordLib::getEntName, bo.getEntName());
        lqw.like(StringUtils.isNotBlank(bo.getName()), XtyTariffCrawlRecordLib::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getTariffNo()), XtyTariffCrawlRecordLib::getTariffNo, bo.getTariffNo());
        lqw.eq(StringUtils.isNotBlank(bo.getFees()), XtyTariffCrawlRecordLib::getFees, bo.getFees());
        lqw.eq(StringUtils.isNotBlank(bo.getFeesUnit()), XtyTariffCrawlRecordLib::getFeesUnit, bo.getFeesUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getCall()), XtyTariffCrawlRecordLib::getCall, bo.getCall());
        lqw.eq(StringUtils.isNotBlank(bo.getData()), XtyTariffCrawlRecordLib::getData, bo.getData());
        lqw.eq(StringUtils.isNotBlank(bo.getDataUnit()), XtyTariffCrawlRecordLib::getDataUnit, bo.getDataUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getSms()), XtyTariffCrawlRecordLib::getSms, bo.getSms());
        lqw.eq(StringUtils.isNotBlank(bo.getOrientTraffic()), XtyTariffCrawlRecordLib::getOrientTraffic, bo.getOrientTraffic());
        lqw.eq(StringUtils.isNotBlank(bo.getOrientTrafficUnit()), XtyTariffCrawlRecordLib::getOrientTrafficUnit, bo.getOrientTrafficUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getIptv()), XtyTariffCrawlRecordLib::getIptv, bo.getIptv());
        lqw.eq(StringUtils.isNotBlank(bo.getBandwidth()), XtyTariffCrawlRecordLib::getBandwidth, bo.getBandwidth());
        lqw.eq(StringUtils.isNotBlank(bo.getRights()), XtyTariffCrawlRecordLib::getRights, bo.getRights());
        lqw.eq(StringUtils.isNotBlank(bo.getIncrementBusiness()), XtyTariffCrawlRecordLib::getIncrementBusiness, bo.getIncrementBusiness());
        lqw.eq(StringUtils.isNotBlank(bo.getOtherContent()), XtyTariffCrawlRecordLib::getOtherContent, bo.getOtherContent());
        lqw.eq(StringUtils.isNotBlank(bo.getOnlineDay()), XtyTariffCrawlRecordLib::getOnlineDay, bo.getOnlineDay());
        lqw.eq(StringUtils.isNotBlank(bo.getOfflineDay()), XtyTariffCrawlRecordLib::getOfflineDay, bo.getOfflineDay());
        lqw.eq(StringUtils.isNotBlank(bo.getTariffAttr()), XtyTariffCrawlRecordLib::getTariffAttr, bo.getTariffAttr());
        lqw.eq(StringUtils.isNotBlank(bo.getApplicablePeople()), XtyTariffCrawlRecordLib::getApplicablePeople, bo.getApplicablePeople());
        lqw.eq(StringUtils.isNotBlank(bo.getApplicableArea()), XtyTariffCrawlRecordLib::getApplicableArea, bo.getApplicableArea());
        lqw.eq(StringUtils.isNotBlank(bo.getValidPeriod()), XtyTariffCrawlRecordLib::getValidPeriod, bo.getValidPeriod());
        lqw.eq(StringUtils.isNotBlank(bo.getChannel()), XtyTariffCrawlRecordLib::getChannel, bo.getChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getDuration()), XtyTariffCrawlRecordLib::getDuration, bo.getDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getUnsubscribe()), XtyTariffCrawlRecordLib::getUnsubscribe, bo.getUnsubscribe());
        lqw.eq(StringUtils.isNotBlank(bo.getResponsibility()), XtyTariffCrawlRecordLib::getResponsibility, bo.getResponsibility());
        lqw.eq(StringUtils.isNotBlank(bo.getOthers()), XtyTariffCrawlRecordLib::getOthers, bo.getOthers());
        lqw.eq(StringUtils.isNotBlank(bo.getExtendedFields()), XtyTariffCrawlRecordLib::getExtendedFields, bo.getExtendedFields());
        lqw.eq(bo.getDateId() != null, XtyTariffCrawlRecordLib::getDateId, bo.getDateId());
        lqw.eq(bo.getMonthId() != null, XtyTariffCrawlRecordLib::getMonthId, bo.getMonthId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionNo()), XtyTariffCrawlRecordLib::getVersionNo, bo.getVersionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTariffProvinceCode()), XtyTariffCrawlRecordLib::getTariffProvinceCode, bo.getTariffProvinceCode());
        lqw.like(StringUtils.isNotBlank(bo.getTariffProvinceName()), XtyTariffCrawlRecordLib::getTariffProvinceName, bo.getTariffProvinceName());
        lqw.like(StringUtils.isNotBlank(bo.getCrawlerProvinceName()), XtyTariffCrawlRecordLib::getCrawlerProvinceName, bo.getCrawlerProvinceName());
        lqw.eq(StringUtils.isNotBlank(bo.getCrawlerType()), XtyTariffCrawlRecordLib::getCrawlerType, bo.getCrawlerType());
        lqw.eq(StringUtils.isNotBlank(bo.getClassicTypeOne()), XtyTariffCrawlRecordLib::getClassicTypeOne, bo.getClassicTypeOne());
        lqw.eq(StringUtils.isNotBlank(bo.getClassicTypeTwo()), XtyTariffCrawlRecordLib::getClassicTypeTwo, bo.getClassicTypeTwo());
        lqw.eq(StringUtils.isNotBlank(bo.getTariffType()), XtyTariffCrawlRecordLib::getTariffType, bo.getTariffType());
        lqw.eq(StringUtils.isNotBlank(bo.getBak1()), XtyTariffCrawlRecordLib::getBak1, bo.getBak1());
        lqw.eq(StringUtils.isNotBlank(bo.getBak2()), XtyTariffCrawlRecordLib::getBak2, bo.getBak2());
        lqw.eq(StringUtils.isNotBlank(bo.getBak3()), XtyTariffCrawlRecordLib::getBak3, bo.getBak3());
        lqw.eq(StringUtils.isNotBlank(bo.getBak4()), XtyTariffCrawlRecordLib::getBak4, bo.getBak4());
        lqw.eq(StringUtils.isNotBlank(bo.getBak5()), XtyTariffCrawlRecordLib::getBak5, bo.getBak5());
        lqw.eq(StringUtils.isNotBlank(bo.getBak6()), XtyTariffCrawlRecordLib::getBak6, bo.getBak6());
        lqw.eq(StringUtils.isNotBlank(bo.getBak7()), XtyTariffCrawlRecordLib::getBak7, bo.getBak7());
        lqw.eq(StringUtils.isNotBlank(bo.getBak8()), XtyTariffCrawlRecordLib::getBak8, bo.getBak8());
        lqw.eq(StringUtils.isNotBlank(bo.getBak9()), XtyTariffCrawlRecordLib::getBak9, bo.getBak9());
        lqw.eq(StringUtils.isNotBlank(bo.getBak10()), XtyTariffCrawlRecordLib::getBak10, bo.getBak10());
        return lqw;
    }

    /**
     * 新增信通院资费爬虫数据
     *
     * @param bo 信通院资费爬虫数据
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(XtyTariffCrawlRecordLibBo bo) {
        XtyTariffCrawlRecordLib add = MapstructUtils.convert(bo, XtyTariffCrawlRecordLib.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改信通院资费爬虫数据
     *
     * @param bo 信通院资费爬虫数据
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(XtyTariffCrawlRecordLibBo bo) {
        XtyTariffCrawlRecordLib update = MapstructUtils.convert(bo, XtyTariffCrawlRecordLib.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(XtyTariffCrawlRecordLib entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除信通院资费爬虫数据信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
