package com.yunqu.crawler.config;

import com.yunqu.emergency.common.core.utils.StringUtils;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 爬虫任务配置属性
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "crawler")
public class CrawlerTaskProperties {

    /**
     * 主运营商列表（逗号分隔）
     */
    private String operator;

    /**
     * 溢出运营商列表（逗号分隔）
     */
    private String overflowOperator;

    /**
     * 服务器名称
     */
    private String serverName;

    /**
     * 获取主运营商列表
     *
     * @return 运营商列表
     */
    public List<String> getOperatorList() {
        return parseOperatorString(operator);
    }

    /**
     * 获取溢出运营商列表
     *
     * @return 溢出运营商列表
     */
    public List<String> getOverflowOperatorList() {
        return parseOperatorString(overflowOperator);
    }

    /**
     * 解析运营商字符串为列表
     *
     * @param operatorStr 运营商字符串
     * @return 运营商列表
     */
    private List<String> parseOperatorString(String operatorStr) {
        if (StringUtils.isBlank(operatorStr)) {
            return Collections.emptyList();
        }
        return StringUtils.splitList(operatorStr, ",");
    }

    /**
     * 检查是否配置了主运营商
     *
     * @return 是否配置了主运营商
     */
    public boolean hasOperator() {
        return StringUtils.isNotBlank(operator);
    }

    /**
     * 检查是否配置了溢出运营商
     *
     * @return 是否配置了溢出运营商
     */
    public boolean hasOverflowOperator() {
        return StringUtils.isNotBlank(overflowOperator);
    }
}