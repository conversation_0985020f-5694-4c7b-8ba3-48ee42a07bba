package com.yunqu.crawler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yunqu.crawler.domain.XtyCrawlerVersion;
import com.yunqu.crawler.domain.bo.XtyCrawlerVersionBo;
import com.yunqu.crawler.domain.vo.XtyCrawlerVersionVo;
import com.yunqu.crawler.mapper.XtyCrawlerVersionMapper;
import com.yunqu.crawler.service.IXtyCrawlerVersionService;
import com.yunqu.emergency.common.core.utils.MapstructUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.mybatis.core.page.PageQuery;
import com.yunqu.emergency.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@RequiredArgsConstructor
@Service
public class XtyCrawlerVersionServiceImpl extends ServiceImpl<XtyCrawlerVersionMapper, XtyCrawlerVersion> implements IXtyCrawlerVersionService {

    /**
     * 查询【请填写功能名称】
     *
     * @param id 主键
     * @return 【请填写功能名称】
     */
    @Override
    public XtyCrawlerVersionVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询【请填写功能名称】列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 【请填写功能名称】分页列表
     */
    @Override
    public TableDataInfo<XtyCrawlerVersionVo> queryPageList(XtyCrawlerVersionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<XtyCrawlerVersion> lqw = buildQueryWrapper(bo);
        Page<XtyCrawlerVersionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的【请填写功能名称】列表
     *
     * @param bo 查询条件
     * @return 【请填写功能名称】列表
     */
    @Override
    public List<XtyCrawlerVersionVo> queryList(XtyCrawlerVersionBo bo) {
        LambdaQueryWrapper<XtyCrawlerVersion> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<XtyCrawlerVersion> buildQueryWrapper(XtyCrawlerVersionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<XtyCrawlerVersion> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(XtyCrawlerVersion::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getVersionNo()), XtyCrawlerVersion::getVersionNo, bo.getVersionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getLastest()), XtyCrawlerVersion::getLastest, bo.getLastest());
        lqw.eq(bo.getIdx() != null, XtyCrawlerVersion::getIdx, bo.getIdx());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param bo 【请填写功能名称】
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(XtyCrawlerVersionBo bo) {
        XtyCrawlerVersion add = MapstructUtils.convert(bo, XtyCrawlerVersion.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param bo 【请填写功能名称】
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(XtyCrawlerVersionBo bo) {
        XtyCrawlerVersion update = MapstructUtils.convert(bo, XtyCrawlerVersion.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(XtyCrawlerVersion entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除【请填写功能名称】信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
