package com.yunqu.crawler.domain;

import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 资费记录对象 xty_tariff_record
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("xty_tariff_record")
public class XtyTariffRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "ID")
    private String id;

    /**
     * 企业ID
     */
    private String entId;

    /**
     * 订购ID
     */
    private String busiOrderId;

    /**
     * 创建人
     */
    private String createAcc;

    /**
     * 修改人
     */
    private String updateAcc;

    /**
     * 省份
     */
    private String province;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 地市
     */
    private String city;

    /**
     * 地市名称
     */
    private String cityName;

    /**
     * 企业，电信001、移动002、联通003、广电004
     */
    private String ent;

    /**
     * 企业名称
     */
    private String entName;

    /**
     * 备案方类型,  1-集团 、2-省企业
     */
    private String reporterType;

    /**
     * 创建方式，1-人工填报 2-接口填报
     */
    private String createType;

    /**
     * 创建日期id，格式如20231201
     */
    private Long dateId;

    /**
     * 序列号
     */
    private String seqNo;

    /**
     * 备案者
     */
    private String reportObj;

    /**
     * 备案号,如 202401beijing001100v0
     */
    private String reportNo;

    /**
     * 备案标识，如 202401beijing001100
     */
    private String reportKey;

    /**
     * 备案版本
     */
    private Long versionNo;

    /**
     * 版本号
     */
    private String versionNum;

    /**
     * 是否历史版本，Y-历史版本 N-正式版本，每个备案标识，只有1条正式版本
     */
    private String isHistory;

    /**
     * 备案主体，省份取值+附录3定义的运营商ID+部门序号（由各企业自行定义），如group0011，电信集团市场部
     */
    private String reporter;

    /**
     * 是否公示，Y（公示备案）、N（只备案不公示）
     */
    private String isPublic;

    /**
     * 不公示原因，描述只备案不公示的原因，如项目制资费
     */
    private String reasonNoPublic;

    /**
     * 一级分类：1（公众），2（政企）
     */
    private String type1;

    /**
     * 二级分类：1（单产品套餐，如单卡、单宽、单IPTV等）、2（融合套餐）、3（加装包，如数据包、语音包、云服务、权益等）、4（营销活动，含产品优惠、资源赠送、资费优惠等）、5（国内标准资费）、6（国际标准资费）、7（其他）
     */
    private String type2;

    /**
     * 名称
     */
    private String name;

    /**
     * 资费标准,数值（单位：元）
     */
    private String fees;

    /**
     * 语音，数值（单位：分钟）
     */
    private String callNum;

    /**
     * 流量，数值（单位：G）
     */
    private String dataNum;

    /**
     * 短彩信，数值（单位：条）
     */
    private String smsNum;

    /**
     * 国际语音，数值（单位：元/分钟）
     */
    private String internationalCall;

    /**
     * 国际漫游流量,数值（单位：元/G）
     */
    private String internationalRoamingData;

    /**
     * 国际短信,数值（单位：元/条）
     */
    private String internationalSms;

    /**
     * 定向流量,数值（单位：G）
     */
    private String orientTraffic;

    /**
     * IPTV
     */
    private String iptv;

    /**
     * 带宽
     */
    private String bandwidth;

    /**
     * 权益
     */
    private String rights;

    /**
     * 服务内容,含套外资费信息，如果是标准资费，可直接填写“标准国内资费”“标准国际资费”或“标准资费”
     */
    private String otherContent;

    /**
     * 适用人群,文本，如全网用户、校园用户、残疾人、老年人等
     */
    private String applicablePeople;

    /**
     * 适用地区,3位本地网区号，不足三位前面补0，多个区号之间用英文逗号分隔，如果是全国范围适用，则填“000”
     */
    private String applicableArea;

    /**
     * 有效期限,文本，时间段，如3个月、两年、长期或xxxx年xx月xx日到xxxx年xx月xx日
     */
    private String validPeriod;

    /**
     * 违约责任
     */
    private String responsibility;

    /**
     * 限制条件
     */
    private String restrictions;

    /**
     * 上线日期,格式为YYYYMMDD，如：20240101
     */
    private String onlineDay;

    /**
     * 下线日期,格式为YYYYMMDD，如：20240101，如不填则为长期有效
     */
    private String offlineDay;

    /**
     * 其他说明
     */
    private String others;

    /**
     * 销售渠道
     */
    private String channel;

    /**
     * 在网期限,时间段，如1个月、两年或xxxx年xx月xx日到xxxx年xx月xx日
     */
    private String duration;

    /**
     * 资费状态，1-正常 2-已删除
     */
    private String status;

    /**
     * 删除时间
     */
    private String delTime;

    /**
     * 删除原因
     */
    private String reason;

    /**
     * 存量订购用户服务预案
     */
    private String plan;

    /**
     * 删除操作人
     */
    private String delAcc;

    /**
     * 适用省份
     */
    private String applicableProvince;

    /**
     * 资费单位
     */
    private String feesUnit;

    /**
     * 流量单位
     */
    private String dataUnit;

    /**
     * 定向流量单位
     */
    private String orientTrafficUnit;

    /**
     * 适用地区名称
     */
    private String applicableAreaName;

    /**
     * 备案主体名称
     */
    private String reporterName;

    /**
     * 资费属性 1-全国 2-省内 3-本地
     */
    private String tariffAttr;

    /**
     * 地区描述
     */
    private String areaDesc;

    /**
     * 适用地区指定类型 1-全国，2-指定选择 3-指定排除
     */
    private String areaSelectType;

    /**
     * 别名-来自未备案资费资费方案资费名称
     */
    private String tariffAnotherName;

    /**
     * 退订方式
     */
    private String unsubscribe;

    /**
     * 报送字段检查
     */
    private String fieldCheckResult;

    /**
     * 报送字段检查时间
     */
    private String fieldCheckTime;

    /**
     *
     */
    private String fieldCheckNo;

    /**
     * 超出资费 1.超出套餐使用量的语音、流量、短彩信等计费标准 2.超出加装包使用量可继续使用的，明示超出使用量的计费标准，如不涉及则此点不需明示
     */
    private String extraFees;

    /**
     * 其他费用 如调试费、预存款、设备押金等其他各项费用
     */
    private String otherFees;

    /**
     * 1（通信类）：指含语音、流量、固网宽带、短信、彩信等基础通信业务以及副卡、小号、亲情卡等的资费产品 2（非通信类）：指仅含终端设备、IPTV、云产品、会员、安防服务、视频名片等产品的的资费产品
     */
    private String isTelecom;


}
