package com.yunqu.crawler.domain.bo;

import com.yunqu.crawler.domain.XtyCrawlerVersion;
import com.yunqu.emergency.common.core.validate.EditGroup;
import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 【请填写功能名称】业务对象 xty_crawler_version
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = XtyCrawlerVersion.class, reverseConvertGenerate = false)
public class XtyCrawlerVersionBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 版本号
     */
    private String versionNo;

    /**
     * 最新版本表示
     */
    private String lastest;

    /**
     * 序号
     */
    private Long idx;

    /**
     * 归属日期ID
     */
    private Integer belongDateId;

    /**
     * Crawler版本表名称
     */
    private String versionTableName;


    /**
     * 版本状态：ACTIVE-正常，INVALID-已作废
     */
    private String versionStatus;

    /**
     * 作废时间
     */
    private String invalidTime;

    /**
     * 作废操作人
     */
    private String invalidUser;

    /**
     * 作废原因
     */
    private String invalidReason;

    /**
     * 数据总数
     */
    private Integer dataCount;

    /**
     * 爬取状态：PENDING-待爬取，PROCESSING-爬取中，COMPLETED-已完成
     */
    private String crawlStatus;

    /**
     * 爬取时长
     */
    private Integer crawlDuration;

    /**
     * 爬取开始时间
     */
    private Date crawlStartTime;

    /**
     * 爬取结束时间
     */
    private Date crawlEndTime;

    /**
     * 稽核状态：PENDING-待稽核，PROCESSING-稽核中，COMPLETED-已完成
     */
    private String auditStatus;

    /**
     * 稽核开始时间
     */
    private Date auditStartTime;

    /**
     * 稽核结束时间
     */
    private Date auditEndTime;
}
