package com.yunqu.crawler.task;

import com.baomidou.lock.annotation.Lock4j;
import com.yunqu.crawler.constants.CrawlerConstants;
import com.yunqu.crawler.service.ICrawlerTariffTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 运营商资费信息爬虫定时任务
 *
 * 主要功能：
 * 1. 定时触发各运营商资费信息的爬取任务
 * 2. 定时执行待处理的爬虫任务
 * 3. 管理任务执行状态和并发控制
 *
 * 执行策略：
 * - 资费爬取任务：每天凌晨1点执行一次
 * - 任务执行检查：每分钟执行一次
 *
 * <AUTHOR>
 * @version 2.0
 */
@RequiredArgsConstructor
@Slf4j
@Component
public class CrawlerTariffTask {

    private final ICrawlerTariffTaskService crawlerTariffTaskService;

    /**
     * 爬取资费信息的主任务
     * 按顺序执行各运营商的资费爬取
     * 执行时间：每天凌晨1点执行一次
     */
    @Lock4j(name = CrawlerConstants.LOCK_CRAWLER_TARIFF)
    @Scheduled(cron = "0 0 1 * * ?")
    public void crawlerTariff() {
        try {
            crawlerTariffTaskService.excueteCreateVersionTask();
        } catch (Exception e) {
            log.error("创建爬虫任务执行异常", e);
        }
    }


    /**
     * 执行待处理的爬虫任务
     * 执行频率：每分钟检查一次
     * 执行策略：
     * 1. 检查是否有正在运行的任务
     * 2. 获取待执行的任务
     * 3. 执行任务并更新状态
     */
    @Lock4j(name = CrawlerConstants.LOCK_EXECUTE_CRAWLER_TASK)
    @Scheduled(cron = "0 0/1 * * * ?")
    public void excueteCrawlerTask() {
        try {
            crawlerTariffTaskService.excueteCrawlerTask(false);
        } catch (Exception e) {
            log.error("执行爬虫任务执行异常", e);
        }
    }


    /**
     * 执行待处理的爬虫任务
     * 执行频率：每分钟检查一次
     * 执行策略：
     * 1. 检查是否有正在运行的任务
     * 2. 获取待执行的任务
     * 3. 执行任务并更新状态
     */
    @Lock4j(name = CrawlerConstants.LOCK_EXECUTE_CRAWLER_UNICOM_TASK)
    @Scheduled(cron = "0 0/1 * * * ?")
    public void excueteCrawlerTaskWithoutUnicom() {
        try {
            crawlerTariffTaskService.excueteCrawlerTask(true);
        } catch (Exception e) {
            log.error("执行爬虫任务执行异常", e);
        }
    }


}
