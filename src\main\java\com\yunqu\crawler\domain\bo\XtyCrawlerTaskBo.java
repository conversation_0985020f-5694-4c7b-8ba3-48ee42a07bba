package com.yunqu.crawler.domain.bo;

import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.emergency.common.core.validate.EditGroup;
import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 资费爬取任务业务对象 xty_crawler_task
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = XtyCrawlerTask.class, reverseConvertGenerate = false)
public class XtyCrawlerTaskBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 日期ID
     */
    private Integer dateId;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 运营商类型1电信2移动3联通5广电
     */
    private Integer entType;

    /**
     * 状态1进行中2已完成
     */
    private Integer status;

    /**
     * 运营商编码
     */
    private String operatorCode;

    /**
     * 运营商名称
     */
    private String operatorName;

    /**
     * 运行服务器
     */
    private String runServer;

    /**
     * 备注
     */
    private String memo;

    /**
     * 本省资费公示数量
     */
    private Integer localProvinceCount;

    /**
     * 集团资费公示数量
     */
    private Integer groupCount;
}
