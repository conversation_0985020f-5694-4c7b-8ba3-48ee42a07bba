# 智能编码处理修复记录

## 问题背景
用户反馈：经过UTF-8强制编码修改后，原来不乱码的getTariffTypeList方法现在也乱码了。

## 根本原因分析
不同接口返回的Content-Type不同：
- **getProvinceList接口**：`Content-Type: application/json;charset=UTF-8` - 明确指定UTF-8编码
- **TariffMenuDataHomePageNew接口**：`Content-Type: application/json` - 没有指定charset，可能使用GBK等其他编码

## 新的解决方案

### 1. 智能编码检测
```java
static String extractResponseBody(Response response) throws IOException {
    if (response.body() == null) {
        return "";
    }
    
    // 获取Content-Type头信息
    String contentType = response.header("Content-Type");
    
    // 如果Content-Type包含charset，使用OkHttp默认处理
    if (contentType != null && contentType.toLowerCase().contains("charset=")) {
        return response.body().string();
    }
    
    // 如果没有指定charset，尝试多种编码方式
    byte[] bytes = response.body().bytes();
    
    // 首先尝试UTF-8解码
    try {
        String utf8Result = new String(bytes, StandardCharsets.UTF_8);
        if (isValidUtf8(utf8Result)) {
            return utf8Result;
        }
    } catch (Exception e) {
        // UTF-8解码失败，继续尝试其他编码
    }
    
    // 尝试GBK编码
    try {
        return new String(bytes, java.nio.charset.Charset.forName("GBK"));
    } catch (Exception e) {
        // 使用UTF-8作为最后的降级方案
        return new String(bytes, StandardCharsets.UTF_8);
    }
}
```

### 2. UTF-8有效性检测
```java
private static boolean isValidUtf8(String text) {
    // 检测UTF-8 BOM
    if (text.startsWith("\uFEFF")) {
        return true;
    }
    
    // 检测明显的乱码字符
    if (text.contains("???") || text.contains("��")) {
        return false;
    }
    
    return true;
}
```

## 处理逻辑
1. **有charset的情况**：让OkHttp根据Content-Type自动处理（如UTF-8）
2. **无charset的情况**：
   - 先尝试UTF-8解码
   - 检测是否为有效UTF-8
   - 如果无效，尝试GBK解码
   - 最后降级到UTF-8

## 预期效果
- getProvinceList（有charset=UTF-8）：正常处理，不乱码
- getTariffTypeList（无charset）：智能检测编码，解决乱码问题
- 其他接口：根据实际Content-Type动态处理

## 验证方法
1. 测试getProvinceList方法，确认UTF-8编码正常
2. 测试getTariffTypeList方法，确认智能编码检测有效
3. 检查日志输出中的中文字符显示
