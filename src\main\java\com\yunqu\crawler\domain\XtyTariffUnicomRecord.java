package com.yunqu.crawler.domain;

import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 信通院-中国联通公示资费数据对象 xty_tariff_unicom_record
 *
 * <AUTHOR>
 * @date 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("xty_tariff_unicom_record")
public class XtyTariffUnicomRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 资费名称
     */
    private String tariffName;

    /**
     * 资费方案编号
     */
    private String tariffNo;

    /**
     * 资费标准
     */
    private String tariffFee;

    /**
     * 服务内容
     */
    private String serviceContent;

    /**
     * 适用范围
     */
    private String scope;

    /**
     * 有效期限
     */
    private String validPeriod;

    /**
     * 销售渠道
     */
    private String tariffChannel;

    /**
     * 上线日期
     */
    private String onlineDate;

    /**
     * 下线日期
     */
    private String offlineDate;

    /**
     * 在网要求
     */
    private String onlineRequirement;

    /**
     * 退订方式
     */
    private String unsubscribeMethod;

    /**
     * 违约责任
     */
    private String breachResponsibility;

    /**
     * 其他说明
     */
    private String others;

    /**
     * 活动收费标准
     */
    private String activityFeeStandard;

    /**
     * 活动内容及规则
     */
    private String activityContent;

    /**
     * 活动范围
     */
    private String activityScope;

    /**
     * 国内语音拨打分钟数
     */
    private String domesticVoiceCallMin;

    /**
     * 国内流量
     */
    private String domesticTraffic;

    /**
     * 增值业务
     */
    private String valueAddedService;

    /**
     * 权益
     */
    private String rightsInterests;

    /**
     * 定向流量
     */
    private String targetedTraffic;


}
