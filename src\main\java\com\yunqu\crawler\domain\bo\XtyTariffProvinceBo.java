package com.yunqu.crawler.domain.bo;

import com.yunqu.crawler.domain.XtyTariffProvince;
import com.yunqu.emergency.common.core.validate.EditGroup;
import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 资费省份配置信息业务对象 xty_tariff_province
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = XtyTariffProvince.class, reverseConvertGenerate = false)
public class XtyTariffProvinceBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 省份代码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     *
     */
    private Long pId;

    /**
     * 省份简称
     */
    private String abbreviation;

    /**
     * 拼音
     */
    private String pinyin;

    /**
     * 序号
     */
    private Long number;

    /**
     * 资费专用省份编码
     */
    private String tariffProvinceCode;

    /**
     * 省份全称
     */
    private String provinceAllName;

    /**
     *
     */
    private String tariffProvinceName;


}
