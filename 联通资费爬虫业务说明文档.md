# 联通资费爬虫执行流程说明

## 系统作用
根据省份编码，自动从联通官网收集该省份的资费套餐信息，整理后存入数据库。

## 🔄 核心执行逻辑

### 整体流程概述
```
接收省份编码 → 获取地市集合 → 循环地市获取资费类型 → 循环资费类型获取具体资费 → 去重入库
```

### 双线并行机制
每个省份任务会**同时**执行两条收集线路：

| 线路类型 | 参数标识 | 收集内容 | 说明 |
|----------|----------|----------|------|
| **全网资费** | isItNationwide=1 | 全国通用套餐 | 如全国流量包、全国通话套餐 |
| **本省资费** | isItNationwide=0 | 省份特色套餐 | 如本地优惠包、地方特色资费 |

## 📋 详细执行步骤

### 第1步：接收任务
- **输入**：省份编码（如：北京=010，广东=020）
- **操作**：执行爬虫任务

### 第2步：获取地市集合
- **接口**：`/servicequerybusiness/queryTariff/provinceCity`
- **参数**：`provinceId=省份编码`
- **返回**：该省份下所有地市的列表（cityCode, provCode）

### 第3步：循环处理每个地市
**对每个地市执行以下操作：**

#### 3.1 获取资费类型菜单
- **接口**：`/servicequerybusiness/queryTariff/TariffMenuDataHomePageNew`
- **参数**：
  - `isItNationwide`：1=全网资费，0=本省资费
  - `provinceId`：省份编码
  - `cityId`：地市编码
- **返回**：资费分类菜单（oneTwoMenusList）

#### 3.2 循环处理每个资费类型
**资费类型结构：**
```
一级分类（nameOne）
├── 二级分类1（nameTwo）
├── 二级分类2（nameTwo）
└── 二级分类3（nameTwo）
```

**对每个二级分类执行：**

##### A. 判断资费获取方式
- **有linkFlag**：调用QG字典接口获取资费列表
- **无linkFlag**：调用三级对象接口获取资费详情

##### B. 获取具体资费数据
**方式1：字典接口**

- **接口**：`/servicequerybusiness/queryTariff/countryTariffQueryChange`
- **接口**：`/servicequerybusiness/queryTariff/tariffDetailInfoChange`

**方式2：三级对象接口**
- **接口**：`/servicequerybusiness/queryTariff/TariffMenuDataDetailHomePageChange`

### 第4步：数据解析处理
- 解析数据并组装数据入库的DTO对象

### 第5步：数据去重和入库
- **去重规则**：资费名称 + 资费编号 作为唯一标识
- **批量入库**：使用 `insertBatch` 批量插入数据库
- **任务状态更新**：更新任务完成状态

## ✅ 成功/失败判断

### 成功条件
```
全网资费数量 > 0  且  本省资费数量 > 0
```

### 失败条件
```
全网资费数量 = 0  或  本省资费数量 = 0
```

### 容错机制
- **网络异常**：自动重试3次
- **单个地市失败**：记录日志，继续处理下一个地市
- **单个资费失败**：记录日志，继续处理下一个资费

## 📊 执行流程图

```mermaid
graph TD
    A[接收省份编码任务] --> B[获取该省份地市集合]
    B --> C{双线并行处理}

    C --> D[线路A: 全网资费<br/>isItNationwide=1]
    C --> E[线路B: 本省资费<br/>isItNationwide=0]

    D --> F[循环处理每个地市]
    E --> F

    F --> G[获取资费类型菜单<br/>oneTwoMenusList]
    G --> H[循环一级分类nameOne]
    H --> I[循环二级分类nameTwo]

    I --> J{判断获取方式}
    J -->|有linkFlag| K[调用QG字典接口]
    J -->|无linkFlag| L[调用三级对象接口]

    K --> M[获取具体资费数据]
    L --> M

    M --> N[解析6类资费]
    N --> O[个人套餐]
    N --> P[企业套餐]
    N --> Q[固话宽带]
    N --> R[叠加包]
    N --> S[新业务]
    N --> T[营销活动]

    O --> U[数据去重处理]
    P --> U
    Q --> U
    R --> U
    S --> U
    T --> U

    U --> V{检查收集结果}
    V -->|全网>0 且 本省>0| W[✅ 批量入库成功]
    V -->|任一=0| X[❌ 任务失败]

    W --> Y[更新任务状态完成]

    style A fill:#e3f2fd
    style Y fill:#e8f5e8
    style X fill:#ffebee
    style V fill:#fff3e0
    style F fill:#f3e5f5
    style I fill:#f3e5f5
```

## ⚠️ 关键执行要点

### 三层循环结构
1. **地市循环**：遍历省份下的所有地市
2. **资费类型循环**：遍历每个地市的资费分类
3. **具体资费循环**：获取每个分类下的具体资费

### 双线并行机制
- **同一省份**：全网资费和本省资费同时收集
- **不同参数**：通过 `isItNationwide` 参数区分
- **独立处理**：两条线路互不影响

### 成功/失败条件
- **成功**：全网资费数量 > 0 且 本省资费数量 > 0
- **失败**：任何一类数据为空都算失败
- **容错**：单个接口失败不影响整体流程

### 数据去重规则
- **唯一标识**：资费名称 + 资费编号
- **去重时机**：所有数据收集完成后统一去重
- **入库方式**：批量插入提高效率

