package com.yunqu.crawler.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

import com.yunqu.crawler.domain.XtyCrawlerTaskLog;
import com.yunqu.crawler.enums.OperatorEnum;
import com.yunqu.crawler.enums.RunErrorEnum;
import com.yunqu.crawler.mapper.XtyCrawlerTaskLogMapper;
import com.yunqu.emergency.common.core.enums.FormatsType;
import com.yunqu.emergency.common.core.exception.ServiceException;
import com.yunqu.emergency.common.core.utils.DateUtils;
import com.yunqu.emergency.common.core.utils.SpringUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.io.IoUtil;

/**
 * 资费爬虫回调服务接口
 * 负责处理各运营商爬虫任务的回调数据，包括：
 * 1. 文件解压和处理
 * 2. 数据解析和入库
 * 3. 错误日志记录
 */
public interface ICrawlerCallbackService {

    Logger log = LoggerFactory.getLogger(ICrawlerCallbackService.class);

    /**
     * 回调接口
     * 由各运营商实现类实现具体的回调处理逻辑
     *
     * @param taskId     任务ID
     * @param file       回调文件
     * @param crawlerUrl 爬虫URL
     */
    void callback(Long taskId, MultipartFile file, String crawlerUrl);

    /**
     * 记录错误日志
     * 统一的错误日志记录，包含任务和数据源信息
     *
     * @param operator            运营商
     * @param errorEnum           错误类型
     * @param taskId              任务ID
     * @param dateId              日期ID
     * @param crawlerProvinceName 爬取省份
     * @param crawlerType         爬取类型
     * @param sourceData          源数据
     */
    default void recordErrorLog(OperatorEnum operator, RunErrorEnum errorEnum, Long taskId, Integer dateId,
                                String crawlerProvinceName, String crawlerType, String sourceData) {
        log.info("[ERROR-LOG] 开始记录错误日志, taskId: {}, errorCode: {}", taskId, errorEnum.getErrorCode());

        try {
            XtyCrawlerTaskLog taskLog = new XtyCrawlerTaskLog();
            taskLog.setTaskId(taskId);
            taskLog.setErrorCode(errorEnum.getErrorCode());
            taskLog.setErrorMsg(errorEnum.getErrorMsg());
            taskLog.setSourceData(sourceData);
            taskLog.setDateId(dateId);
            taskLog.setEntCode(operator.getCode());
            taskLog.setEntName(operator.getName());
            taskLog.setCrawlerProvinceName(crawlerProvinceName);
            taskLog.setCrawlerType(crawlerType);

            XtyCrawlerTaskLogMapper taskLogMapper = SpringUtils.getBean(XtyCrawlerTaskLogMapper.class);
            taskLogMapper.insert(taskLog);

            log.info("[ERROR-LOG] 错误日志记录成功, taskId: {}, errorCode: {}", taskId, errorEnum.getErrorCode());
        } catch (Exception e) {
            log.error("[ERROR-LOG] 错误日志记录失败, taskId: {}, errorCode: {}, 错误: {}",
                    taskId, errorEnum.getErrorCode(), e.getMessage(), e);
        }
    }

    /**
     * 解压ZIP文件到临时目录
     * 支持多种字符编码，包含错误处理和清理机制
     *
     * @param file        ZIP文件
     * @param tempDirPath 临时目录路径
     * @return 解压后的文件目录路径
     */
    static String dealUnzipFile(MultipartFile file, String tempDirPath) {
        log.info("[UNZIP-START] 开始解压文件, 文件名: {}, 临时目录: {}", file.getOriginalFilename(), tempDirPath);
        long startTime = System.currentTimeMillis();

        if (file == null || file.isEmpty()) {
            log.error("[UNZIP-ERROR] 上传的ZIP文件为空");
            throw new ServiceException("上传的ZIP文件为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".zip")) {
            log.error("[UNZIP-ERROR] 无效的文件格式: {}", originalFilename);
            throw new ServiceException("上传的文件不是ZIP格式");
        }

        File tempDir = new File(tempDirPath);
        if (!tempDir.exists() && !tempDir.mkdirs()) {
            log.error("[UNZIP-ERROR] 创建临时目录失败: {}", tempDirPath);
            throw new ServiceException("创建临时目录失败");
        }

        try {
            // 将上传的文件保存到临时文件
            File zipFile = File.createTempFile("upload_", ".zip");
            file.transferTo(zipFile);
            log.debug("[UNZIP-PROCESS] 保存临时文件成功: {}", zipFile.getAbsolutePath());

            // 尝试使用UTF-8解压
            unzipFile(zipFile, tempDir, startTime);

            // 删除临时ZIP文件
            if (!zipFile.delete()) {
                log.warn("[UNZIP-WARN] 删除临时ZIP文件失败: {}", zipFile.getAbsolutePath());
            }

            return tempDirPath;
        } catch (Exception e) {
            log.error("[UNZIP-FAIL] 解压文件异常, 错误: {}", e.getMessage(), e);
            deleteDirectory(tempDir);
            throw new ServiceException("解压ZIP文件失败: " + e.getMessage());
        }
    }

    /**
     * 解压zip文件
     * @param zipFile
     * @param tempDir
     * @param startTime
     */
    static void unzipFile(File zipFile, File tempDir, long startTime) {
        if (tryUnzipWithEncoding(zipFile, tempDir, StandardCharsets.UTF_8)) {
            log.info("[UNZIP-SUCCESS] 使用UTF-8编码解压成功, 耗时: {}ms", System.currentTimeMillis() - startTime);
        } else {
            // UTF-8失败，尝试使用GBK
            if (tryUnzipWithEncoding(zipFile, tempDir, java.nio.charset.Charset.forName("GBK"))) {
                log.info("[UNZIP-SUCCESS] 使用GBK编码解压成功, 耗时: {}ms", System.currentTimeMillis() - startTime);
            } else {
                log.error("[UNZIP-ERROR] 所有编码尝试都失败");
                deleteDirectory(tempDir);
                throw new ServiceException("所有编码尝试都失败");
            }
        }
    }

    /**
     * 使用指定编码尝试解压ZIP文件
     *
     * @param zipFile ZIP文件
     * @param tempDir 解压目标目录
     * @param charset 字符编码
     * @return 是否解压成功
     */
    static boolean tryUnzipWithEncoding(File zipFile, File tempDir, java.nio.charset.Charset charset) {
        log.debug("[UNZIP-TRY] 尝试使用{}编码解压文件: {}", charset, zipFile.getName());

        try (java.util.zip.ZipInputStream zipInputStream = new java.util.zip.ZipInputStream(
                new FileInputStream(zipFile), charset)) {
            java.util.zip.ZipEntry entry;
            byte[] buffer = new byte[1024];
            int entryCount = 0;

            while ((entry = zipInputStream.getNextEntry()) != null) {
                String entryName = entry.getName();
                entryCount++;

                // 跳过目录项
                if (entry.isDirectory()) {
                    File dir = new File(tempDir, entryName);
                    if (!dir.exists() && !dir.mkdirs()) {
                        log.warn("[UNZIP-WARN] 创建目录失败: {}", dir.getAbsolutePath());
                    }
                    continue;
                }

                // 创建父目录
                File entryFile = new File(tempDir, entryName);
                File parentDir = entryFile.getParentFile();
                if (!parentDir.exists() && !parentDir.mkdirs()) {
                    log.warn("[UNZIP-WARN] 创建父目录失败: {}", parentDir.getAbsolutePath());
                }

                // 写入文件内容
                try (java.io.FileOutputStream outputStream = new java.io.FileOutputStream(entryFile)) {
                    int len;
                    while ((len = zipInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }
                }

                zipInputStream.closeEntry();
                log.debug("[UNZIP-ENTRY] 解压文件项成功: {}", entryName);
            }

            log.info("[UNZIP-SUCCESS] 使用{}编码解压完成, 共处理{}个文件项", charset, entryCount);
            return true;
        } catch (Exception e) {
            log.debug("[UNZIP-FAIL] 使用{}编码解压失败: {}", charset, e.getMessage());
            return false;
        }
    }

    /**
     * 递归删除目录
     * 包含详细的日志记录和错误处理
     *
     * @param directory 要删除的目录
     */
    static void deleteDirectory(File directory) {
        if (directory == null || !directory.exists()) {
            return;
        }

        log.debug("[DELETE-START] 开始删除目录: {}", directory.getAbsolutePath());
        int successCount = 0;
        int failCount = 0;

        try {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    try {
                        if (file.isDirectory()) {
                            deleteDirectory(file);
                        } else {
                            if (file.delete()) {
                                successCount++;
                            } else {
                                failCount++;
                                log.warn("[DELETE-WARN] 删除文件失败: {}", file.getAbsolutePath());
                            }
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("[DELETE-ERROR] 删除文件异常: {}, 错误: {}", file.getAbsolutePath(), e.getMessage());
                    }
                }
            }

            if (!directory.delete()) {
                log.warn("[DELETE-WARN] 删除目录失败: {}", directory.getAbsolutePath());
            }

            log.info("[DELETE-END] 目录删除完成: {}, 成功: {}, 失败: {}", directory.getAbsolutePath(), successCount, failCount);
        } catch (Exception e) {
            log.error("[DELETE-FAIL] 删除目录异常: {}, 错误: {}", directory.getAbsolutePath(), e.getMessage(), e);
        }
    }

    /**
     * 递归删除目录
     * 包含详细的日志记录和错误处理
     *
     * @param directory 要删除的目录
     */
    static void deleteDirectory(String forlderPath) {
        File directory = new File(forlderPath);
        if (directory == null || !directory.exists()) {
            return;
        }

        log.debug("[DELETE-START] 开始删除目录: {}", directory.getAbsolutePath());
        int successCount = 0;
        int failCount = 0;

        try {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    try {
                        if (file.isDirectory()) {
                            deleteDirectory(file);
                        } else {
                            if (file.delete()) {
                                successCount++;
                            } else {
                                failCount++;
                                log.warn("[DELETE-WARN] 删除文件失败: {}", file.getAbsolutePath());
                            }
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("[DELETE-ERROR] 删除文件异常: {}, 错误: {}", file.getAbsolutePath(), e.getMessage());
                    }
                }
            }

            if (!directory.delete()) {
                log.warn("[DELETE-WARN] 删除目录失败: {}", directory.getAbsolutePath());
            }

            log.info("[DELETE-END] 目录删除完成: {}, 成功: {}, 失败: {}", directory.getAbsolutePath(), successCount, failCount);
        } catch (Exception e) {
            log.error("[DELETE-FAIL] 删除目录异常: {}, 错误: {}", directory.getAbsolutePath(), e.getMessage(), e);
        }
    }

    /**
     * 读取文件内容为字符串
     * 包含错误处理和资源关闭
     *
     * @param file 文件
     * @return 文件内容字符串
     */
    default String readFileContent(File file) {
        log.debug("[READ-START] 开始读取文件: {}", file.getName());

        try (InputStream inputStream = new FileInputStream(file)) {
            String content = IoUtil.read(inputStream, StandardCharsets.UTF_8);
            log.debug("[READ-SUCCESS] 文件读取成功: {}, 内容长度: {}", file.getName(), content.length());
            return content;
        } catch (Exception e) {
            log.error("[READ-FAIL] 文件读取失败: {}, 错误: {}", file.getName(), e.getMessage(), e);
            return null;
        }
    }

    String[] PARSE_PATTERNS = new String[]{"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss",
            "yyyy.MM.dd HH:mm", "yyyy.MM", "yyyy年MM月dd日"};

    default String formatDateStr(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return "";
        }
        try {
            if (StringUtils.contains(dateStr, "-")) {
                return DateUtils.parseDateToStr(FormatsType.YYYYMMDD, DateUtils.parseDate(dateStr, PARSE_PATTERNS));
            }
            if (StringUtils.contains(dateStr, "年")) {
                return DateUtils.parseDateToStr(FormatsType.YYYYMMDD, DateUtils.parseDate(dateStr, PARSE_PATTERNS));
            }
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
        return dateStr;
    }
}
