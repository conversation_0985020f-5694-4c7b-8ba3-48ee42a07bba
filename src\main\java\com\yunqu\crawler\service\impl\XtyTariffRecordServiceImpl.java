package com.yunqu.crawler.service.impl;

import com.yunqu.crawler.domain.XtyTariffRecord;
import com.yunqu.crawler.domain.bo.XtyTariffRecordBo;
import com.yunqu.crawler.domain.vo.XtyTariffRecordVo;
import com.yunqu.crawler.mapper.XtyTariffRecordMapper;
import com.yunqu.crawler.service.IXtyTariffRecordService;
import com.yunqu.emergency.common.core.utils.MapstructUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.mybatis.core.page.TableDataInfo;
import com.yunqu.emergency.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 资费记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@RequiredArgsConstructor
@Service
public class XtyTariffRecordServiceImpl implements IXtyTariffRecordService {

    private final XtyTariffRecordMapper baseMapper;

    /**
     * 查询资费记录
     *
     * @param id 主键
     * @return 资费记录
     */
    @Override
    public XtyTariffRecordVo queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询资费记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 资费记录分页列表
     */
    @Override
    public TableDataInfo<XtyTariffRecordVo> queryPageList(XtyTariffRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<XtyTariffRecord> lqw = buildQueryWrapper(bo);
        Page<XtyTariffRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的资费记录列表
     *
     * @param bo 查询条件
     * @return 资费记录列表
     */
    @Override
    public List<XtyTariffRecordVo> queryList(XtyTariffRecordBo bo) {
        LambdaQueryWrapper<XtyTariffRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<XtyTariffRecord> buildQueryWrapper(XtyTariffRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<XtyTariffRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(XtyTariffRecord::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getEntId()), XtyTariffRecord::getEntId, bo.getEntId());
        lqw.eq(StringUtils.isNotBlank(bo.getBusiOrderId()), XtyTariffRecord::getBusiOrderId, bo.getBusiOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateAcc()), XtyTariffRecord::getCreateAcc, bo.getCreateAcc());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdateAcc()), XtyTariffRecord::getUpdateAcc, bo.getUpdateAcc());
        lqw.eq(StringUtils.isNotBlank(bo.getProvince()), XtyTariffRecord::getProvince, bo.getProvince());
        lqw.like(StringUtils.isNotBlank(bo.getProvinceName()), XtyTariffRecord::getProvinceName, bo.getProvinceName());
        lqw.eq(StringUtils.isNotBlank(bo.getCity()), XtyTariffRecord::getCity, bo.getCity());
        lqw.like(StringUtils.isNotBlank(bo.getCityName()), XtyTariffRecord::getCityName, bo.getCityName());
        lqw.eq(StringUtils.isNotBlank(bo.getEnt()), XtyTariffRecord::getEnt, bo.getEnt());
        lqw.like(StringUtils.isNotBlank(bo.getEntName()), XtyTariffRecord::getEntName, bo.getEntName());
        lqw.eq(StringUtils.isNotBlank(bo.getReporterType()), XtyTariffRecord::getReporterType, bo.getReporterType());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateType()), XtyTariffRecord::getCreateType, bo.getCreateType());
        lqw.eq(bo.getDateId() != null, XtyTariffRecord::getDateId, bo.getDateId());
        lqw.eq(StringUtils.isNotBlank(bo.getSeqNo()), XtyTariffRecord::getSeqNo, bo.getSeqNo());
        lqw.eq(StringUtils.isNotBlank(bo.getReportObj()), XtyTariffRecord::getReportObj, bo.getReportObj());
        lqw.eq(StringUtils.isNotBlank(bo.getReportNo()), XtyTariffRecord::getReportNo, bo.getReportNo());
        lqw.eq(StringUtils.isNotBlank(bo.getReportKey()), XtyTariffRecord::getReportKey, bo.getReportKey());
        lqw.eq(bo.getVersionNo() != null, XtyTariffRecord::getVersionNo, bo.getVersionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionNum()), XtyTariffRecord::getVersionNum, bo.getVersionNum());
        lqw.eq(StringUtils.isNotBlank(bo.getIsHistory()), XtyTariffRecord::getIsHistory, bo.getIsHistory());
        lqw.eq(StringUtils.isNotBlank(bo.getReporter()), XtyTariffRecord::getReporter, bo.getReporter());
        lqw.eq(StringUtils.isNotBlank(bo.getIsPublic()), XtyTariffRecord::getIsPublic, bo.getIsPublic());
        lqw.eq(StringUtils.isNotBlank(bo.getReasonNoPublic()), XtyTariffRecord::getReasonNoPublic, bo.getReasonNoPublic());
        lqw.eq(StringUtils.isNotBlank(bo.getType1()), XtyTariffRecord::getType1, bo.getType1());
        lqw.eq(StringUtils.isNotBlank(bo.getType2()), XtyTariffRecord::getType2, bo.getType2());
        lqw.like(StringUtils.isNotBlank(bo.getName()), XtyTariffRecord::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getFees()), XtyTariffRecord::getFees, bo.getFees());
        lqw.eq(StringUtils.isNotBlank(bo.getCallNum()), XtyTariffRecord::getCallNum, bo.getCallNum());
        lqw.eq(StringUtils.isNotBlank(bo.getDataNum()), XtyTariffRecord::getDataNum, bo.getDataNum());
        lqw.eq(StringUtils.isNotBlank(bo.getSmsNum()), XtyTariffRecord::getSmsNum, bo.getSmsNum());
        lqw.eq(StringUtils.isNotBlank(bo.getInternationalCall()), XtyTariffRecord::getInternationalCall, bo.getInternationalCall());
        lqw.eq(StringUtils.isNotBlank(bo.getInternationalRoamingData()), XtyTariffRecord::getInternationalRoamingData, bo.getInternationalRoamingData());
        lqw.eq(StringUtils.isNotBlank(bo.getInternationalSms()), XtyTariffRecord::getInternationalSms, bo.getInternationalSms());
        lqw.eq(StringUtils.isNotBlank(bo.getOrientTraffic()), XtyTariffRecord::getOrientTraffic, bo.getOrientTraffic());
        lqw.eq(StringUtils.isNotBlank(bo.getIptv()), XtyTariffRecord::getIptv, bo.getIptv());
        lqw.eq(StringUtils.isNotBlank(bo.getBandwidth()), XtyTariffRecord::getBandwidth, bo.getBandwidth());
        lqw.eq(StringUtils.isNotBlank(bo.getRights()), XtyTariffRecord::getRights, bo.getRights());
        lqw.eq(StringUtils.isNotBlank(bo.getOtherContent()), XtyTariffRecord::getOtherContent, bo.getOtherContent());
        lqw.eq(StringUtils.isNotBlank(bo.getApplicablePeople()), XtyTariffRecord::getApplicablePeople, bo.getApplicablePeople());
        lqw.eq(StringUtils.isNotBlank(bo.getApplicableArea()), XtyTariffRecord::getApplicableArea, bo.getApplicableArea());
        lqw.eq(StringUtils.isNotBlank(bo.getValidPeriod()), XtyTariffRecord::getValidPeriod, bo.getValidPeriod());
        lqw.eq(StringUtils.isNotBlank(bo.getResponsibility()), XtyTariffRecord::getResponsibility, bo.getResponsibility());
        lqw.eq(StringUtils.isNotBlank(bo.getRestrictions()), XtyTariffRecord::getRestrictions, bo.getRestrictions());
        lqw.eq(StringUtils.isNotBlank(bo.getOnlineDay()), XtyTariffRecord::getOnlineDay, bo.getOnlineDay());
        lqw.eq(StringUtils.isNotBlank(bo.getOfflineDay()), XtyTariffRecord::getOfflineDay, bo.getOfflineDay());
        lqw.eq(StringUtils.isNotBlank(bo.getOthers()), XtyTariffRecord::getOthers, bo.getOthers());
        lqw.eq(StringUtils.isNotBlank(bo.getChannel()), XtyTariffRecord::getChannel, bo.getChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getDuration()), XtyTariffRecord::getDuration, bo.getDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), XtyTariffRecord::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getDelTime()), XtyTariffRecord::getDelTime, bo.getDelTime());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), XtyTariffRecord::getReason, bo.getReason());
        lqw.eq(StringUtils.isNotBlank(bo.getPlan()), XtyTariffRecord::getPlan, bo.getPlan());
        lqw.eq(StringUtils.isNotBlank(bo.getDelAcc()), XtyTariffRecord::getDelAcc, bo.getDelAcc());
        lqw.eq(StringUtils.isNotBlank(bo.getApplicableProvince()), XtyTariffRecord::getApplicableProvince, bo.getApplicableProvince());
        lqw.eq(StringUtils.isNotBlank(bo.getFeesUnit()), XtyTariffRecord::getFeesUnit, bo.getFeesUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getDataUnit()), XtyTariffRecord::getDataUnit, bo.getDataUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getOrientTrafficUnit()), XtyTariffRecord::getOrientTrafficUnit, bo.getOrientTrafficUnit());
        lqw.like(StringUtils.isNotBlank(bo.getApplicableAreaName()), XtyTariffRecord::getApplicableAreaName, bo.getApplicableAreaName());
        lqw.like(StringUtils.isNotBlank(bo.getReporterName()), XtyTariffRecord::getReporterName, bo.getReporterName());
        lqw.eq(StringUtils.isNotBlank(bo.getTariffAttr()), XtyTariffRecord::getTariffAttr, bo.getTariffAttr());
        lqw.eq(StringUtils.isNotBlank(bo.getAreaDesc()), XtyTariffRecord::getAreaDesc, bo.getAreaDesc());
        lqw.eq(StringUtils.isNotBlank(bo.getAreaSelectType()), XtyTariffRecord::getAreaSelectType, bo.getAreaSelectType());
        lqw.like(StringUtils.isNotBlank(bo.getTariffAnotherName()), XtyTariffRecord::getTariffAnotherName, bo.getTariffAnotherName());
        lqw.eq(StringUtils.isNotBlank(bo.getUnsubscribe()), XtyTariffRecord::getUnsubscribe, bo.getUnsubscribe());
        lqw.eq(StringUtils.isNotBlank(bo.getFieldCheckResult()), XtyTariffRecord::getFieldCheckResult, bo.getFieldCheckResult());
        lqw.eq(StringUtils.isNotBlank(bo.getFieldCheckTime()), XtyTariffRecord::getFieldCheckTime, bo.getFieldCheckTime());
        lqw.eq(StringUtils.isNotBlank(bo.getFieldCheckNo()), XtyTariffRecord::getFieldCheckNo, bo.getFieldCheckNo());
        lqw.eq(StringUtils.isNotBlank(bo.getExtraFees()), XtyTariffRecord::getExtraFees, bo.getExtraFees());
        lqw.eq(StringUtils.isNotBlank(bo.getOtherFees()), XtyTariffRecord::getOtherFees, bo.getOtherFees());
        lqw.eq(StringUtils.isNotBlank(bo.getIsTelecom()), XtyTariffRecord::getIsTelecom, bo.getIsTelecom());
        return lqw;
    }

    /**
     * 新增资费记录
     *
     * @param bo 资费记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(XtyTariffRecordBo bo) {
        XtyTariffRecord add = MapstructUtils.convert(bo, XtyTariffRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改资费记录
     *
     * @param bo 资费记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(XtyTariffRecordBo bo) {
        XtyTariffRecord update = MapstructUtils.convert(bo, XtyTariffRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(XtyTariffRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除资费记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
