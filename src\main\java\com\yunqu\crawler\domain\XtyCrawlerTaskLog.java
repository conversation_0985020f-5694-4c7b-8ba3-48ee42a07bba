package com.yunqu.crawler.domain;

import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 信通院爬虫执行异常日志信息对象 xty_crawler_task_log
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("xty_crawler_task_log")
public class XtyCrawlerTaskLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 异常编码
     */
    private Integer errorCode;

    /**
     * 异常信息
     */
    private String errorMsg;

    /**
     * 源数据
     */
    private String sourceData;

    /**
     * 日期ID
     */
    private Integer dateId;

    /**
     * 运营商编码
     */
    private String entCode;

    /**
     * 运营商名称
     */
    private String entName;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 爬取省份名称
     */
    private String crawlerProvinceName;

    /**
     * 爬取类型
     */
    private String crawlerType;


}
