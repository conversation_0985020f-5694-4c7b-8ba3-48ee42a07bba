package com.yunqu.crawler.service.impl.telecom;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.base.StringAmountUnitParser;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.util.ParseDateUtil;
import com.yunqu.emergency.common.core.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 西藏电信资费数据处理器
 * </p>
 *
 * @ClassName XzTelecomRecordAnalysisHandler
 * <AUTHOR> Copy This Tag)
 * @Description 西藏电信资费数据解析处理
 * @Since create in 2025/5/28 14:08
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
public class XzTelecomRecordAnalysisHandler extends BaseTelecomRecordAnalysisHandler implements IRecordAnalysisHandler {
    /*@Override
    public List<XtyTariffCrawlRecord> analysis(String content) {
        JSONObject entries = JSONUtil.parseObj(content);
        JSONObject object = entries.getJSONObject("data");
        if (object == null) return List.of();
        JSONArray jsonArray = object.getJSONArray("tariffPackageList");
        if (jsonArray == null || jsonArray.isEmpty()) return List.of();
        return analysisJson(jsonArray);
    }*/


    private List<XtyTariffCrawlRecord> analysisJson(JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.size() == 0) {
            return List.of();
        }

        List<XtyTariffCrawlRecord> records = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
            record.setExtendedFields(JSONUtil.toJsonStr(object));
            record.setName(StringUtils.trimToEmpty(object.getStr("name")));
            record.setTariffNo(StringUtils.trimToEmpty(object.getStr("reportNo")));

            String fees = StringUtils.trimToEmpty(object.getStr("fees"));
            if (StringUtils.isNotBlank(fees)) {
                StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(fees);
                if (parse != null) {
                    record.setFees(parse.getAmount());
                    record.setFeesUnit(parse.getUnit());
                }
            }
            record.setCall(StringUtils.trimToEmpty(object.getStr("call")));

            String data = StringUtils.trimToEmpty(object.getStr("data"));
            if (StringUtils.isNotBlank(data)) {
                StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(data);
                if (parse != null) {
                    record.setData(parse.getAmount());
                    record.setDataUnit(parse.getUnit());
                }
            }
            record.setSms(StringUtils.trimToEmpty(object.getStr("sms")));

            String orientTraffic = StringUtils.trimToEmpty(object.getStr("orientTraffic"));
            if (StringUtils.isNotBlank(orientTraffic)) {
                StringAmountUnitParser.AmountUnitResult parse = StringAmountUnitParser.parse(orientTraffic);
                if (parse != null) {
                    record.setOrientTraffic(parse.getAmount());
                    record.setOrientTrafficUnit(parse.getUnit());
                }
            }
            record.setIptv(StringUtils.trimToEmpty(object.getStr("iptv")));
            record.setBandwidth(StringUtils.trimToEmpty(object.getStr("bandwidth")));
            record.setRights(StringUtils.trimToEmpty(object.getStr("rights")));
            record.setOtherContent(StringUtils.trimToEmpty(object.getStr("otherContent")));
            String onlineDay = StringUtils.trimToEmpty(object.getStr("onlineDay"));
            if (StringUtils.isNotBlank(onlineDay)) {
                ParseDateUtil.DateRangeResult dateRangeResult = ParseDateUtil.parseDateRange(onlineDay);
                if (dateRangeResult != null) {
                    record.setOnlineDay(ParseDateUtil.formatDateStr(dateRangeResult.getStartDate()));
                    record.setOfflineDay(ParseDateUtil.formatDateStr(dateRangeResult.getEndDate()));
                }
            }

            record.setApplicablePeople(StringUtils.trimToEmpty(object.getStr("applicablePeople")));
            record.setValidPeriod(StringUtils.trimToEmpty(object.getStr("validPeriod")));
            record.setChannel(StringUtils.trimToEmpty(object.getStr("channel")));
            record.setDuration(StringUtils.trimToEmpty(object.getStr("duration")));
            record.setUnsubscribe(StringUtils.trimToEmpty(object.getStr("unsubscribe")));
            record.setResponsibility(StringUtils.trimToEmpty(object.getStr("responsibility")));
            record.setOthers(StringUtils.trimToEmpty(object.getStr("others")));
            records.add(record);
        }
        return records;
    }
}
