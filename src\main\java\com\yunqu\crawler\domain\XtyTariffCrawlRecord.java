package com.yunqu.crawler.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yunqu.emergency.common.mybatis.annotation.DynamicTable;
import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 信通院资费爬虫数据对象 xty_tariff_crawl_record
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@DynamicTable
@TableName(value = "xty_tariff_crawl_record")
public class XtyTariffCrawlRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 运营商编码
     */
    private String entCode;

    /**
     * 运营商名称
     */
    private String entName;

    /**
     * 资费名称
     */
    @TableField(value = "`NAME`")
    private String name;

    /**
     * 资费方案编号
     */
    private String tariffNo;

    /**
     * 资费标准
     */
    private String fees;

    /**
     * 资费单位
     */
    private String feesUnit;

    /**
     * 语音
     */
    @TableField(value = "`CALL`")
    private String call;

    /**
     * 通用流量
     */
    @TableField(value = "`DATA`")
    private String data;

    /**
     * 流量单位
     */
    private String dataUnit;

    /**
     * 短彩信
     */
    private String sms;

    /**
     * 定向流量
     */
    private String orientTraffic;

    /**
     * 定向流量单位
     */
    private String orientTrafficUnit;

    /**
     * IPTV
     */
    private String iptv;

    /**
     * 带宽
     */
    private String bandwidth;

    /**
     * 权益
     */
    private String rights;

    /**
     * 增值业务
     */
    private String incrementBusiness;

    /**
     * 服务内容
     */
    private String otherContent;

    /**
     * 上线日期
     */
    private String onlineDay;

    /**
     * 下线日期
     */
    private String offlineDay;

    /**
     * 资费属性
     */
    private String tariffAttr;

    /**
     * 适用范围
     */
    private String applicablePeople;

    /**
     * 适用地区
     */
    private String applicableArea;

    /**
     * 有效期限
     */
    private String validPeriod;

    /**
     * 销售渠道
     */
    private String channel;

    /**
     * 在网要求
     */
    private String duration;

    /**
     * 退订方式
     */
    private String unsubscribe;

    /**
     * 违约责任
     */
    private String responsibility;

    /**
     * 其他说明
     */
    private String others;

    /**
     * 爬取数据日期ID
     */
    private Integer dateId;

    /**
     * 爬取数据页面URL
     */
    private String crawlUrl;

    /**
     * 比对时间
     */
    private Date compareTime;

    /**
     * 与报送库比对不一致的字段
     */
    private String diffFields;

    /**
     * 省份代码
     */
    private String tariffProvinceCode;

    /**
     * 省份全称
     */
    private String tariffProvinceName;

    /**
     * 爬取省份名称
     */
    private String crawlerProvinceName;

    /**
     * 爬取类型
     */
    private String crawlerType;

    /**
     * 爬虫回调文件名称
     */
    private String crawlerCallbackFileName;

    /**
     * 爬虫数据所属文件名称
     */
    private String crawlerDataBelongFileName;

    /**
     *  Crawler classic type one
     */
    private String classicTypeOne;

    /**
     *  Crawler classic type two
     */
    private String classicTypeTwo;

    /**
     *  Crawler tariff type
     */
    private String tariffType;

    /**
     * 版本号
     */
    private String versionNo;

    /**
     * 扩展字段
     */
    private String extendedFields;

    /**
     * 超出资费说明
     */
    private String outFeeContent;

    /**
     * 其他费用说明
     */
    private String otherFeeContent;

    /**
     * 国际通话
     */
    private String internationalCall;
}
