package com.yunqu.crawler.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.yunqu.crawler.domain.XtyCrawlerTask;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 资费爬取任务视图对象 xty_crawler_task
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = XtyCrawlerTask.class)
public class XtyCrawlerTaskVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 日期ID
     */
    @ExcelProperty(value = "日期ID")
    private Integer dateId;

    /**
     * 省份编码
     */
    @ExcelProperty(value = "省份编码")
    private String provinceCode;

    /**
     * 省份名称
     */
    @ExcelProperty(value = "省份名称")
    private String provinceName;

    /**
     * 运营商类型1电信2移动3联通5广电
     */
    @ExcelProperty(value = "运营商类型1电信2移动3联通5广电")
    private Integer entType;

    /**
     * 状态1进行中2已完成
     */
    @ExcelProperty(value = "状态1进行中2已完成")
    private Integer status;


    /**
     * 运营商编码
     */
    private String operatorCode;

    /**
     * 运营商名称
     */
    private String operatorName;

    /**
     * 执行任务服务器
     */
    private String runServer;

    /**
     * 描述
     */
    private String memo;

    /**
     * 本省资费公示数量
     */
    private Integer localProvinceCount;

    /**
     * 集团资费公示数量
     */
    private Integer groupCount;

}
