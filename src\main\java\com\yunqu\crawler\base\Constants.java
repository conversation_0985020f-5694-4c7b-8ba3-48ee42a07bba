package com.yunqu.crawler.base;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

/**
 * 常量类
 */
public interface Constants {

    /**
     * 任务状态-未运行
     */
    Integer CRAWLER_TASK_STATUS_0 = 0;

    /**
     * 任务状态-运行中
     */
    Integer CRAWLER_TASK_STATUS_1 = 1;

    /**
     * 任务状态-已完成
     */
    Integer CRAWLER_TASK_STATUS_2 = 2;

    /**
     * 运营商类型-电信
     */
    String OPERATOR_TELECOM = "telecom";

    /**
     * 运营商类型-移动
     */
    String OPERATOR_MOBILE = "mobile";

    /**
     * 运营商类型-联通
     */
    String OPERATOR_UNICOM = "unicom";

    /**
     * 运营商类型-广电
     */
    String OPERATOR_GDT = "broadnet";

    String GDT_PROVINCE_DICT_STR = """
            [
                    {"code": "北京市", "name": "北京", "provinceCode": "110000", "provinceName": "北京"},
                    {"code": "安徽省", "name": "安徽", "provinceCode": "340000", "provinceName": "安徽"},
                    {"code": "重庆市", "name": "重庆", "provinceCode": "500000", "provinceName": "重庆"},
                    {"code": "福建省", "name": "福建", "provinceCode": "350000", "provinceName": "福建"},
                    {"code": "广东省", "name": "广东", "provinceCode": "440000", "provinceName": "广东"},
                    {"code": "广州市", "name": "广州市", "provinceCode": "440000", "provinceName": "广东"},
                    {"code": "深圳市", "name": "深圳市", "provinceCode": "440000", "provinceName": "广东"},
                    {"code": "甘肃省", "name": "甘肃", "provinceCode": "620000", "provinceName": "甘肃"},
                    {"code": "广西壮族自治区", "name": "广西", "provinceCode": "450000", "provinceName": "广西"},
                    {"code": "贵州省", "name": "贵州", "provinceCode": "520000", "provinceName": "贵州"},
                    {"code": "湖北省", "name": "湖北", "provinceCode": "420000", "provinceName": "湖北"},
                    {"code": "湖南省", "name": "湖南", "provinceCode": "430000", "provinceName": "湖南"},
                    {"code": "河北省", "name": "河北", "provinceCode": "130000", "provinceName": "河北"},
                    {"code": "河南省", "name": "河南", "provinceCode": "410000", "provinceName": "河南"},
                    {"code": "海南省", "name": "海南", "provinceCode": "460000", "provinceName": "海南"},
                    {"code": "黑龙江省", "name": "黑龙江", "provinceCode": "230000", "provinceName": "黑龙江"},
                    {"code": "江苏省", "name": "江苏", "provinceCode": "320000", "provinceName": "江苏"},
                    {"code": "吉林省", "name": "吉林", "provinceCode": "220000", "provinceName": "吉林"},
                    {"code": "江西省", "name": "江西", "provinceCode": "360000", "provinceName": "江西"},
                    {"code": "辽宁省", "name": "辽宁", "provinceCode": "210000", "provinceName": "辽宁"},
                    {"code": "内蒙古自治区", "name": "内蒙古", "provinceCode": "150000", "provinceName": "内蒙古"},
                    {"code": "宁夏回族自治区", "name": "宁夏", "provinceCode": "640000", "provinceName": "宁夏"},
                    {"code": "青海省", "name": "青海", "provinceCode": "630000", "provinceName": "青海"},
                    {"code": "山东省", "name": "山东", "provinceCode": "370000", "provinceName": "山东"},
                    {"code": "上海市", "name": "上海", "provinceCode": "310000", "provinceName": "上海"},
                    {"code": "山西省", "name": "山西", "provinceCode": "140000", "provinceName": "山西"},
                    {"code": "陕西省", "name": "陕西", "provinceCode": "610000", "provinceName": "陕西"},
                    {"code": "四川省", "name": "四川", "provinceCode": "510000", "provinceName": "四川"},
                    {"code": "天津市", "name": "天津", "provinceCode": "120000", "provinceName": "天津"},
                    {"code": "新疆维吾尔自治区", "name": "新疆", "provinceCode": "650000", "provinceName": "新疆"},
                    {"code": "西藏自治区", "name": "西藏", "provinceCode": "540000", "provinceName": "西藏"},
                    {"code": "云南省", "name": "云南", "provinceCode": "530000", "provinceName": "云南"},
                    {"code": "浙江省", "name": "浙江", "provinceCode": "330000", "provinceName": "浙江"}
            ]
            """;

    /**
     * 电信省份字典
     */
    String TELECOM_PROVINCE_DICT_STR = """
                [
                    {"code": "bj", "name": "北京", "provinceCode": "110000", "provinceName": "北京"},
                    {"code": "ah", "name": "安徽", "provinceCode": "340000", "provinceName": "安徽"},
                    {"code": "cq", "name": "重庆", "provinceCode": "500000", "provinceName": "重庆"},
                    {"code": "fj", "name": "福建", "provinceCode": "350000", "provinceName": "福建"},
                    {"code": "gd", "name": "广东", "provinceCode": "440000", "provinceName": "广东"},
                    {"code": "gs", "name": "甘肃", "provinceCode": "620000", "provinceName": "甘肃"},
                    {"code": "gx", "name": "广西", "provinceCode": "450000", "provinceName": "广西"},
                    {"code": "gz", "name": "贵州", "provinceCode": "520000", "provinceName": "贵州"},
                    {"code": "hb", "name": "湖北", "provinceCode": "420000", "provinceName": "湖北"},
                    {"code": "hn", "name": "湖南", "provinceCode": "430000", "provinceName": "湖南"},
                    {"code": "he", "name": "河北", "provinceCode": "130000", "provinceName": "河北"},
                    {"code": "ha", "name": "河南", "provinceCode": "410000", "provinceName": "河南"},
                    {"code": "hi", "name": "海南", "provinceCode": "460000", "provinceName": "海南"},
                    {"code": "hl", "name": "黑龙江", "provinceCode": "230000", "provinceName": "黑龙江"},
                    {"code": "js", "name": "江苏", "provinceCode": "320000", "provinceName": "江苏"},
                    {"code": "jl", "name": "吉林", "provinceCode": "220000", "provinceName": "吉林"},
                    {"code": "jx", "name": "江西", "provinceCode": "360000", "provinceName": "江西"},
                    {"code": "ln", "name": "辽宁", "provinceCode": "210000", "provinceName": "辽宁"},
                    {"code": "nm", "name": "内蒙古", "provinceCode": "150000", "provinceName": "内蒙古"},
                    {"code": "nx", "name": "宁夏", "provinceCode": "640000", "provinceName": "宁夏"},
                    {"code": "qh", "name": "青海", "provinceCode": "630000", "provinceName": "青海"},
                    {"code": "sd", "name": "山东", "provinceCode": "370000", "provinceName": "山东"},
                    {"code": "sh", "name": "上海", "provinceCode": "310000", "provinceName": "上海"},
                    {"code": "sx", "name": "山西", "provinceCode": "140000", "provinceName": "山西"},
                    {"code": "sn", "name": "陕西", "provinceCode": "610000", "provinceName": "陕西"},
                    {"code": "sc", "name": "四川", "provinceCode": "510000", "provinceName": "四川"},
                    {"code": "tj", "name": "天津", "provinceCode": "120000", "provinceName": "天津"},
                    {"code": "xj", "name": "新疆", "provinceCode": "650000", "provinceName": "新疆"},
                    {"code": "xz", "name": "西藏", "provinceCode": "540000", "provinceName": "西藏"},
                    {"code": "yn", "name": "云南", "provinceCode": "530000", "provinceName": "云南"},
                    {"code": "zj", "name": "浙江", "provinceCode": "330000", "provinceName": "浙江"}
                    ]
            """;

    /**
     * 省份字典
     */
    String MOBILE_PROVINCE_DICT_STR = """
            [
                {
                    "id": "100",
                    "pid": "0",
                    "text": "北京",
                    "Level": 0,
                    "provinceCode": "110000",
                    "provinceName": "北京"
                },
                {
                    "id": "200",
                    "pid": "0",
                    "text": "广东",
                    "Level": 0,
                    "provinceCode": "440000",
                    "provinceName": "广东"
                },
                {
                    "id": "210",
                    "pid": "0",
                    "text": "上海",
                    "Level": 0,
                    "provinceCode": "310000",
                    "provinceName": "上海"
                },
                {
                    "id": "220",
                    "pid": "0",
                    "text": "天津",
                    "Level": 0,
                    "provinceCode": "120000",
                    "provinceName": "天津"
                },
                {
                    "id": "230",
                    "pid": "0",
                    "text": "重庆",
                    "Level": 0,
                    "provinceCode": "500000",
                    "provinceName": "重庆"
                },
                {
                    "id": "240",
                    "pid": "0",
                    "text": "辽宁",
                    "Level": 0,
                    "provinceCode": "210000",
                    "provinceName": "辽宁"
                },
                {
                    "id": "250",
                    "pid": "0",
                    "text": "江苏",
                    "Level": 0,
                    "provinceCode": "320000",
                    "provinceName": "江苏"
                },
                {
                    "id": "270",
                    "pid": "0",
                    "text": "湖北",
                    "Level": 0,
                    "provinceCode": "420000",
                    "provinceName": "湖北"
                },
                {
                    "id": "280",
                    "pid": "0",
                    "text": "四川",
                    "Level": 0,
                    "provinceCode": "510000",
                    "provinceName": "四川"
                },
                {
                    "id": "290",
                    "pid": "0",
                    "text": "陕西",
                    "Level": 0,
                    "provinceCode": "610000",
                    "provinceName": "陕西"
                },
                {
                    "id": "311",
                    "pid": "0",
                    "text": "河北",
                    "Level": 0,
                    "provinceCode": "130000",
                    "provinceName": "河北"
                },
                {
                    "id": "351",
                    "pid": "0",
                    "text": "山西",
                    "Level": 0,
                    "provinceCode": "140000",
                    "provinceName": "山西"
                },
                {
                    "id": "371",
                    "pid": "0",
                    "text": "河南",
                    "Level": 0,
                    "provinceCode": "410000",
                    "provinceName": "河南"
                },
                {
                    "id": "431",
                    "pid": "0",
                    "text": "吉林",
                    "Level": 0,
                    "provinceCode": "220000",
                    "provinceName": "吉林"
                },
                {
                    "id": "451",
                    "pid": "0",
                    "text": "黑龙江",
                    "Level": 0,
                    "provinceCode": "230000",
                    "provinceName": "黑龙江"
                },
                {
                    "id": "471",
                    "pid": "0",
                    "text": "内蒙古",
                    "Level": 0,
                    "provinceCode": "150000",
                    "provinceName": "内蒙古"
                },
                {
                    "id": "531",
                    "pid": "0",
                    "text": "山东",
                    "Level": 0,
                    "provinceCode": "370000",
                    "provinceName": "山东"
                },
                {
                    "id": "551",
                    "pid": "0",
                    "text": "安徽",
                    "Level": 0,
                    "provinceCode": "340000",
                    "provinceName": "安徽"
                },
                {
                    "id": "571",
                    "pid": "0",
                    "text": "浙江",
                    "Level": 0,
                    "provinceCode": "330000",
                    "provinceName": "浙江"
                },
                {
                    "id": "591",
                    "pid": "0",
                    "text": "福建",
                    "Level": 0,
                    "provinceCode": "350000",
                    "provinceName": "福建"
                },
                {
                    "id": "731",
                    "pid": "0",
                    "text": "湖南",
                    "Level": 0,
                    "provinceCode": "430000",
                    "provinceName": "湖南"
                },
                {
                    "id": "771",
                    "pid": "0",
                    "text": "广西",
                    "Level": 0,
                    "provinceCode": "450000",
                    "provinceName": "广西"
                },
                {
                    "id": "791",
                    "pid": "0",
                    "text": "江西",
                    "Level": 0,
                    "provinceCode": "360000",
                    "provinceName": "江西"
                },
                {
                    "id": "851",
                    "pid": "0",
                    "text": "贵州",
                    "Level": 0,
                    "provinceCode": "520000",
                    "provinceName": "贵州"
                },
                {
                    "id": "871",
                    "pid": "0",
                    "text": "云南",
                    "Level": 0,
                    "provinceCode": "530000",
                    "provinceName": "云南"
                },
                {
                    "id": "891",
                    "pid": "0",
                    "text": "西藏",
                    "Level": 0,
                    "provinceCode": "540000",
                    "provinceName": "西藏"
                },
                {
                    "id": "898",
                    "pid": "0",
                    "text": "海南",
                    "Level": 0,
                    "provinceCode": "460000",
                    "provinceName": "海南"
                },
                {
                    "id": "931",
                    "pid": "0",
                    "text": "甘肃",
                    "Level": 0,
                    "provinceCode": "620000",
                    "provinceName": "甘肃"
                },
                {
                    "id": "951",
                    "pid": "0",
                    "text": "宁夏",
                    "Level": 0,
                    "provinceCode": "640000",
                    "provinceName": "宁夏"
                },
                {
                    "id": "971",
                    "pid": "0",
                    "text": "青海",
                    "Level": 0,
                    "provinceCode": "630000",
                    "provinceName": "青海"
                },
                {
                    "id": "991",
                    "pid": "0",
                    "text": "新疆",
                    "Level": 0,
                    "provinceCode": "650000",
                    "provinceName": "新疆"
                }
            ]
            """;

    /**
     * 省份字典
     */
    // JSONArray MOBILE_PROVINCE_DICT =
    // JSONUtil.parseArray(MOBILE_PROVINCE_DICT_STR);

    /**
     * 联通省份字典
     */
    String UNICOM_PROVINCE_DICT_STR = """
            [
                {
                    "code": "011",
                    "name": "北京",
                    "provinceCode": "110000",
                    "provinceName": "北京"
                },
                {
                    "code": "030",
                    "name": "安徽",
                    "provinceCode": "340000",
                    "provinceName": "安徽"
                },
                {
                    "code": "083",
                    "name": "重庆",
                    "provinceCode": "500000",
                    "provinceName": "重庆"
                },
                {
                    "code": "038",
                    "name": "福建",
                    "provinceCode": "350000",
                    "provinceName": "福建"
                },
                {
                    "code": "087",
                    "name": "甘肃",
                    "provinceCode": "620000",
                    "provinceName": "甘肃"
                },
                {
                    "code": "051",
                    "name": "广东",
                    "provinceCode": "440000",
                    "provinceName": "广东"
                },
                {
                    "code": "059",
                    "name": "广西",
                    "provinceCode": "450000",
                    "provinceName": "广西"
                },
                {
                    "code": "085",
                    "name": "贵州",
                    "provinceCode": "520000",
                    "provinceName": "贵州"
                },
                {
                    "code": "050",
                    "name": "海南",
                    "provinceCode": "460000",
                    "provinceName": "海南"
                },
                {
                    "code": "018",
                    "name": "河北",
                    "provinceCode": "130000",
                    "provinceName": "河北"
                },
                {
                    "code": "076",
                    "name": "河南",
                    "provinceCode": "410000",
                    "provinceName": "河南"
                },
                {
                    "code": "097",
                    "name": "黑龙江",
                    "provinceCode": "230000",
                    "provinceName": "黑龙江"
                },
                {
                    "code": "071",
                    "name": "湖北",
                    "provinceCode": "420000",
                    "provinceName": "湖北"
                },
                {
                    "code": "074",
                    "name": "湖南",
                    "provinceCode": "430000",
                    "provinceName": "湖南"
                },
                {
                    "code": "090",
                    "name": "吉林",
                    "provinceCode": "220000",
                    "provinceName": "吉林"
                },
                {
                    "code": "034",
                    "name": "江苏",
                    "provinceCode": "320000",
                    "provinceName": "江苏"
                },
                {
                    "code": "075",
                    "name": "江西",
                    "provinceCode": "360000",
                    "provinceName": "江西"
                },
                {
                    "code": "091",
                    "name": "辽宁",
                    "provinceCode": "210000",
                    "provinceName": "辽宁"
                },
                {
                    "code": "010",
                    "name": "内蒙古",
                    "provinceCode": "150000",
                    "provinceName": "内蒙古"
                },
                {
                    "code": "088",
                    "name": "宁夏",
                    "provinceCode": "640000",
                    "provinceName": "宁夏"
                },
                {
                    "code": "070",
                    "name": "青海",
                    "provinceCode": "630000",
                    "provinceName": "青海"
                },
                {
                    "code": "017",
                    "name": "山东",
                    "provinceCode": "370000",
                    "provinceName": "山东"
                },
                {
                    "code": "019",
                    "name": "山西",
                    "provinceCode": "140000",
                    "provinceName": "山西"
                },
                {
                    "code": "084",
                    "name": "陕西",
                    "provinceCode": "610000",
                    "provinceName": "陕西"
                },
                {
                    "code": "031",
                    "name": "上海",
                    "provinceCode": "310000",
                    "provinceName": "上海"
                },
                {
                    "code": "081",
                    "name": "四川",
                    "provinceCode": "510000",
                    "provinceName": "四川"
                },
                {
                    "code": "013",
                    "name": "天津",
                    "provinceCode": "120000",
                    "provinceName": "天津"
                },
                {
                    "code": "079",
                    "name": "西藏",
                    "provinceCode": "540000",
                    "provinceName": "西藏"
                },
                {
                    "code": "089",
                    "name": "新疆",
                    "provinceCode": "650000",
                    "provinceName": "新疆"
                },
                {
                    "code": "086",
                    "name": "云南",
                    "provinceCode": "530000",
                    "provinceName": "云南"
                },
                {
                    "code": "036",
                    "name": "浙江",
                    "provinceCode": "330000",
                    "provinceName": "浙江"
                }
            ]
            """;

    // JSONArray UNICOM_PROVINCE_DICT =
    // JSONUtil.parseArray(UNICOM_PROVINCE_DICT_STR);

    String UNICOM_TARIFF_CLASSIC = """
            {
                "1": {
                    "name": "个人套餐",
                    "child": {
                        "1": "5G",
                        "2": "4G",
                        "3": "宽带",
                        "4": "固话",
                        "5": "助老资费",
                        "6": "助残资费",
                        "7": "宽带套餐资费",
                        "8": "互联网专线"
                    }
                },
                "2": {
                    "name": "融合套餐",
                    "child": {
                        "1": "本地融合",
                        "2": "跨域融合"
                    }
                },
                "3": {
                    "name": "叠加包",
                    "child": {
                        "1": "流量包",
                        "2": "短信包",
                        "3": "语音包",
                        "4": "权益包",
                        "5": "其他"
                    }
                },
                "4": {
                    "name": "新业务",
                    "child": {
                        "1": "视频彩铃",
                        "2": "联通超清",
                        "3": "联通云盘",
                        "4": "联通组网",
                        "5": "联通助理",
                        "6": "联通看家",
                        "7": "联通安全管家",
                        "8": "机器人",
                        "9": "联通爱听",
                        "10": "视频客服",
                        "11": "FTTR",
                        "12": "云加速",
                        "13": "提速包",
                        "14": "宽视界",
                        "15": "其他",
                        "16": "5G畅联",
                        "17": "XR通话",
                        "18": "联通学堂",
                        "19": "视频名片",
                        "20": "数字人",
                        "21": "生态权益",
                        "22": "联通云手机",
                        "23": "5G宽视界",
                        "24": "联通智家",
                        "25": "安全管家",
                        "26": "王卡云智包",
                        "27": "联通云智手机",
                        "28": "组网",
                        "29": "智家通通"
                    }
                },
                "5": {
                    "name": "营销活动",
                    "child": {
                        "1": "促销",
                        "2": "合约"
                    }
                },
                "6": {
                    "name": "港澳台业务",
                    "child": {
                        "1": "港澳台长途",
                        "2": "港澳台漫游标准资费",
                        "3": "港澳台漫游流量包",
                        "4": "其他"
                    }
                },
                "7": {
                    "name": "国际业务",
                    "child": {
                        "1": "国际长途",
                        "2": "国际漫游标准资费",
                        "3": "国际漫游流量包",
                        "4": "其他"
                    }
                },
                "8": {
                    "name": "政企套餐",
                    "child": {
                        "1": "4G政企资费",
                        "2": "5G政企资费"
                    }
                },
                "9": {
                    "name": "其他",
                    "child": {
                        "1": "其他"
                    }
                }
            }
            """;

    /**
     * 省份字典
     */
    JSONObject UNICOM_TARIFF_CLASSIC_DICT = JSONUtil.parseObj(UNICOM_TARIFF_CLASSIC);

    String CRAWLER_TYPE_DICT_STR = """
            北京资费,
            天津资费,
            河北资费,
            山西资费,
            内蒙古资费,
            辽宁资费,
            吉林资费,
            黑龙江资费,
            上海资费,
            江苏资费,
            浙江资费,
            安徽资费,
            福建资费,
            江西资费,
            山东资费,
            河南资费,
            湖北资费,
            湖南资费,
            广东资费,
            广西资费,
            海南资费,
            重庆资费,
            四川资费,
            贵州资费,
            云南资费,
            西藏资费,
            陕西资费,
            甘肃资费,
            青海资费,
            宁夏资费,
            新疆资费,
            全网资费
            """;

}
