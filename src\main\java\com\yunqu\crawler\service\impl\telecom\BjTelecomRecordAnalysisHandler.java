package com.yunqu.crawler.service.impl.telecom;

import java.util.ArrayList;
import java.util.List;

import com.yunqu.crawler.base.StringAmountUnitParser;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.util.ParseDateUtil;
import com.yunqu.emergency.common.core.utils.StringUtils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

/**
 * 北京电信流量包解析器
 *
 * @ClassName BjTelecomRecordAnalysisHandler
 * <AUTHOR> Copy This Tag)
 * @Description 北京电信流量包解析器，实现IRecordAnalysisHandler接口
 * @Since create in 2025/5/28 13:56
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
public class BjTelecomRecordAnalysisHandler implements IRecordAnalysisHandler {

    @Override
    public List<XtyTariffCrawlRecord> analysis(String content) {
        JSONObject contentJson = JSONUtil.parseObj(content);
        String returnStr = contentJson.getStr("returnStr");
        if (StringUtils.isBlank(returnStr))
            return List.of();
        JSONObject returnJson = JSONUtil.parseObj(returnStr);
        JSONArray objects = returnJson.getJSONArray("object");
        if (objects == null || objects.isEmpty())
            return List.of();
        List<XtyTariffCrawlRecord> results = new ArrayList<>();
        for (int i = 0; i < objects.size(); i++) {
            JSONObject object = objects.getJSONObject(i);
            XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
            record.setExtendedFields(JSONUtil.toJsonStr(object));
            record.setName(StringUtils.trimToEmpty(object.getStr("name")));
            String tariffNo = StringUtils.trimToEmpty(object.getStr("reportNo"));
            record.setTariffNo(tariffNo);
            String fee = StringUtils.trimToEmpty(object.getStr("fee"));
            record.setFees(fee);
            String feeUnit = StringUtils.trimToEmpty(object.getStr("feeUnit"));
            if (StringUtils.isBlank(feeUnit)) {
                StringAmountUnitParser.AmountUnitResult parseResult = StringAmountUnitParser.parse(fee);
                if (parseResult != null) {
                    record.setFeesUnit(StringUtils.trimToEmpty(parseResult.getUnit()));
                }
            } else {
                StringAmountUnitParser.AmountUnitResult parseResult = StringAmountUnitParser.parse(feeUnit);
                if (parseResult != null) {
                    record.setFeesUnit(StringUtils.trimToEmpty(parseResult.getUnit()));
                }
            }
            record.setCall(StringUtils.trimToEmpty(object.getStr("call")));

            String uniData = StringUtils.trimToEmpty(object.getStr("uniData"));
            if (StringUtils.isNotBlank(uniData)) {
                StringAmountUnitParser.AmountUnitResult parseResult = StringAmountUnitParser.parse(uniData);
                if (parseResult != null) {
                    record.setData(StringUtils.trimToEmpty(parseResult.getAmount()));
                    record.setDataUnit(StringUtils.trimToEmpty(parseResult.getUnit()));
                }
            }

            record.setSms(StringUtils.trimToEmpty(object.getStr("mms")));

            String tarData = StringUtils.trimToEmpty(object.getStr("tarData"));
            if (StringUtils.isNotBlank(tarData)) {
                StringAmountUnitParser.AmountUnitResult parseResult = StringAmountUnitParser.parse(tarData);
                if (parseResult != null) {
                    record.setOrientTraffic(StringUtils.trimToEmpty(parseResult.getAmount()));
                    record.setOrientTrafficUnit(StringUtils.trimToEmpty(parseResult.getUnit()));
                }
            }

            record.setIptv(StringUtils.trimToEmpty(object.getStr("television")));
            record.setBandwidth(StringUtils.trimToEmpty(object.getStr("bandwidth")));
            record.setRights(StringUtils.trimToEmpty(object.getStr("rights")));

            record.setOtherContent(StringUtils.trimToEmpty(object.getStr("otherContent")));

            String onlineDate = StringUtils.trimToEmpty(object.getStr("onlineDate"));
            if (StringUtils.isNotBlank(onlineDate)) {
                record.setOnlineDay(ParseDateUtil.formatDateStr(onlineDate));
            }
            String offlineDate = StringUtils.trimToEmpty(object.getStr("offlineDate"));
            if (StringUtils.isNotBlank(offlineDate)) {
                record.setOfflineDay(ParseDateUtil.formatDateStr(offlineDate));
            }

            record.setApplicablePeople(StringUtils.trimToEmpty(object.getStr("applicableRange")));
            record.setValidPeriod(StringUtils.trimToEmpty(object.getStr("validPeriod")));
            record.setChannel(StringUtils.trimToEmpty(object.getStr("channel")));
            record.setDuration(StringUtils.trimToEmpty(object.getStr("duration")));
            record.setUnsubscribe(StringUtils.trimToEmpty(object.getStr("unsubWay")));
            record.setResponsibility(StringUtils.trimToEmpty(object.getStr("responsibility")));
            results.add(record);
        }
        return results;
    }

}
