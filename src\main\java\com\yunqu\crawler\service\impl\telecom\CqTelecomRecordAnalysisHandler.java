package com.yunqu.crawler.service.impl.telecom;

import java.util.ArrayList;
import java.util.List;

import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.util.ParseDateUtil;
import com.yunqu.emergency.common.core.utils.StringUtils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

/**
 * 重庆电信流量包解析器
 *
 * @ClassName CqTelecomRecordAnalysisHandler
 * <AUTHOR> Copy This Tag)
 * @Description 重庆电信流量包解析器，实现IRecordAnalysisHandler接口
 * @Since create in 2025/5/28 13:57
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
public class CqTelecomRecordAnalysisHandler implements IRecordAnalysisHandler {

    @Override
    public List<XtyTariffCrawlRecord> analysis(String content) {
        if (StringUtils.isBlank(content))
            return List.of();
        JSONObject contentJson = JSONUtil.parseObj(content);
        JSONArray jsonArray = contentJson.getJSONArray("data");
        if (jsonArray == null || jsonArray.isEmpty())
            return List.of();

        List<XtyTariffCrawlRecord> records = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);

            XtyTariffCrawlRecord record = new XtyTariffCrawlRecord();
            record.setExtendedFields(JSONUtil.toJsonStr(object));
            record.setName(StringUtils.trimToEmpty(object.getStr("name")));
            String tariffNo = StringUtils.trimToEmpty(object.getStr("reportNo"));
            record.setTariffNo(tariffNo);
            record.setFees(StringUtils.trimToEmpty(object.getStr("fees")));
            record.setFeesUnit(StringUtils.trimToEmpty(object.getStr("feesUnit")));
            record.setCall(StringUtils.trimToEmpty(object.getStr("call")));
            record.setData(StringUtils.trimToEmpty(object.getStr("flow")));
            record.setDataUnit(StringUtils.trimToEmpty(object.getStr("flowUnit")));
            record.setSms(StringUtils.trimToEmpty(object.getStr("sms")));
            record.setOrientTraffic(StringUtils.trimToEmpty(object.getStr("orientTrafficFlow")));
            record.setOrientTrafficUnit(StringUtils.trimToEmpty(object.getStr("orientTrafficFlowUnit")));
            record.setIptv(StringUtils.trimToEmpty(object.getStr("iptv")));
            record.setBandwidth(StringUtils.trimToEmpty(object.getStr("bandwidth")));
            record.setRights(StringUtils.trimToEmpty(object.getStr("rights")));
            record.setOtherContent(StringUtils.trimToEmpty(object.getStr("otherContent")));
            String onlineDate = StringUtils.trimToEmpty(object.getStr("onlineDate"));
            if (StringUtils.isNotBlank(onlineDate)) {
                record.setOnlineDay(ParseDateUtil.formatDateStr(onlineDate));
            }
            String offlineDate = StringUtils.trimToEmpty(object.getStr("offlineDate"));
            if (StringUtils.isNotBlank(offlineDate)) {
                record.setOfflineDay(ParseDateUtil.formatDateStr(offlineDate));
            }
            record.setApplicablePeople(StringUtils.trimToEmpty(object.getStr("applicableRange")));
            record.setValidPeriod(StringUtils.trimToEmpty(object.getStr("validPeriod")));
            record.setChannel(StringUtils.trimToEmpty(object.getStr("channel")));
            record.setDuration(StringUtils.trimToEmpty(object.getStr("duration")));
            record.setUnsubscribe(StringUtils.trimToEmpty(object.getStr("unsubscribe")));
            record.setResponsibility(StringUtils.trimToEmpty(object.getStr("responsibility")));
            record.setOthers(StringUtils.trimToEmpty(object.getStr("others")));
            records.add(record);
        }
        return records;
    }
}
