package com.yunqu.crawler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yunqu.crawler.domain.XtyTariffProvince;
import com.yunqu.crawler.domain.bo.XtyTariffProvinceBo;
import com.yunqu.crawler.domain.vo.XtyTariffProvinceVo;
import com.yunqu.crawler.mapper.XtyTariffProvinceMapper;
import com.yunqu.crawler.service.IXtyTariffProvinceService;
import com.yunqu.emergency.common.core.utils.MapstructUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.mybatis.core.page.PageQuery;
import com.yunqu.emergency.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 资费省份配置信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class XtyTariffProvinceServiceImpl implements IXtyTariffProvinceService {

    private final XtyTariffProvinceMapper baseMapper;

    /**
     * 查询资费省份配置信息
     *
     * @param id 主键
     * @return 资费省份配置信息
     */
    @Override
    public XtyTariffProvinceVo queryById(Long id) {
        log.info("开始查询资费省份配置信息, id: {}", id);
        XtyTariffProvinceVo result = baseMapper.selectVoById(id);
        log.info("完成查询资费省份配置信息, id: {}, 结果: {}", id, result != null ? "存在" : "不存在");
        return result;
    }

    /**
     * 分页查询资费省份配置信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 资费省份配置信息分页列表
     */
    @Override
    public TableDataInfo<XtyTariffProvinceVo> queryPageList(XtyTariffProvinceBo bo, PageQuery pageQuery) {
        log.info("开始分页查询资费省份配置信息, 查询条件: {}, 分页参数: {}", bo, pageQuery);
        long startTime = System.currentTimeMillis();

        LambdaQueryWrapper<XtyTariffProvince> lqw = buildQueryWrapper(bo);
        Page<XtyTariffProvinceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        long endTime = System.currentTimeMillis();
        log.info("完成分页查询资费省份配置信息, 耗时: {}ms, 总记录数: {}", endTime - startTime, result.getTotal());
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的资费省份配置信息列表
     *
     * @param bo 查询条件
     * @return 资费省份配置信息列表
     */
    @Override
    public List<XtyTariffProvinceVo> queryList(XtyTariffProvinceBo bo) {
        log.info("开始查询资费省份配置信息列表, 查询条件: {}", bo);
        long startTime = System.currentTimeMillis();

        LambdaQueryWrapper<XtyTariffProvince> lqw = buildQueryWrapper(bo);
        List<XtyTariffProvinceVo> result = baseMapper.selectVoList(lqw);

        long endTime = System.currentTimeMillis();
        log.info("完成查询资费省份配置信息列表, 耗时: {}ms, 结果数量: {}", endTime - startTime, result.size());
        return result;
    }

    private LambdaQueryWrapper<XtyTariffProvince> buildQueryWrapper(XtyTariffProvinceBo bo) {
        log.debug("构建查询条件, 参数: {}", bo);
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<XtyTariffProvince> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(XtyTariffProvince::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getProvinceCode()), XtyTariffProvince::getProvinceCode, bo.getProvinceCode());
        lqw.like(StringUtils.isNotBlank(bo.getProvinceName()), XtyTariffProvince::getProvinceName, bo.getProvinceName());
        lqw.eq(bo.getPId() != null, XtyTariffProvince::getPId, bo.getPId());
        lqw.eq(StringUtils.isNotBlank(bo.getAbbreviation()), XtyTariffProvince::getAbbreviation, bo.getAbbreviation());
        lqw.eq(StringUtils.isNotBlank(bo.getPinyin()), XtyTariffProvince::getPinyin, bo.getPinyin());
        lqw.eq(bo.getNumber() != null, XtyTariffProvince::getNumber, bo.getNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getTariffProvinceCode()), XtyTariffProvince::getTariffProvinceCode, bo.getTariffProvinceCode());
        lqw.like(StringUtils.isNotBlank(bo.getProvinceAllName()), XtyTariffProvince::getProvinceAllName, bo.getProvinceAllName());
        lqw.like(StringUtils.isNotBlank(bo.getTariffProvinceName()), XtyTariffProvince::getTariffProvinceName, bo.getTariffProvinceName());
        return lqw;
    }

    /**
     * 新增资费省份配置信息
     *
     * @param bo 资费省份配置信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(XtyTariffProvinceBo bo) {
        log.info("开始新增资费省份配置信息, 配置信息: {}", bo);
        XtyTariffProvince add = MapstructUtils.convert(bo, XtyTariffProvince.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            log.info("完成新增资费省份配置信息, id: {}", add.getId());
        } else {
            log.warn("新增资费省份配置信息失败");
        }
        return flag;
    }

    /**
     * 修改资费省份配置信息
     *
     * @param bo 资费省份配置信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(XtyTariffProvinceBo bo) {
        log.info("开始修改资费省份配置信息, 配置信息: {}", bo);
        XtyTariffProvince update = MapstructUtils.convert(bo, XtyTariffProvince.class);
        validEntityBeforeSave(update);
        boolean result = baseMapper.updateById(update) > 0;
        log.info("完成修改资费省份配置信息, id: {}, 结果: {}", update.getId(), result ? "成功" : "失败");
        return result;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(XtyTariffProvince entity) {
        log.debug("执行资费省份配置信息保存前校验, 配置信息: {}", entity);
        // TODO: 实现具体的校验逻辑，如唯一约束等
        if (StringUtils.isBlank(entity.getProvinceCode())) {
            log.warn("省份编码不能为空");
        }
        if (StringUtils.isBlank(entity.getProvinceName())) {
            log.warn("省份名称不能为空");
        }
    }

    /**
     * 校验并批量删除资费省份配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        log.info("开始批量删除资费省份配置信息, ids: {}, 是否校验: {}", ids, isValid);
        if (isValid) {
            // TODO: 实现业务校验逻辑
            log.debug("执行删除前业务校验");
        }
        boolean result = baseMapper.deleteByIds(ids) > 0;
        log.info("完成批量删除资费省份配置信息, 结果: {}", result ? "成功" : "失败");
        return result;
    }

    /**
     * 根据tariffProvinceCode查询资费省份配置信息
     * 该方法使用缓存优化查询性能
     *
     * @param tariffProvinceCode 资费省份编码
     * @return 资费省份配置信息
     */
    @Cacheable(cacheNames = "xty_tariff_province", key = "#tariffProvinceCode")
    @Override
    public XtyTariffProvinceVo getByTariffProvinceCode(String tariffProvinceCode) {
        log.info("开始查询资费省份配置信息, tariffProvinceCode: {}", tariffProvinceCode);
        if (StringUtils.isBlank(tariffProvinceCode)) {
            log.warn("tariffProvinceCode为空，返回null");
            return null;
        }

        LambdaQueryWrapper<XtyTariffProvince> wrapper = Wrappers.lambdaQuery(XtyTariffProvince.class)
                .eq(XtyTariffProvince::getTariffProvinceCode, tariffProvinceCode);
        XtyTariffProvinceVo result = baseMapper.selectVoOne(wrapper);

        if (result == null) {
            log.warn("未找到对应的资费省份配置信息, tariffProvinceCode: {}", tariffProvinceCode);
        } else {
            log.info("完成查询资费省份配置信息, tariffProvinceCode: {}, provinceName: {}",
                    tariffProvinceCode, result.getProvinceName());
        }
        return result;
    }
}
