package com.yunqu.crawler.util;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

import com.yunqu.emergency.common.core.enums.FormatsType;
import com.yunqu.emergency.common.core.utils.DateUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 日期解析工具类
 * </p>
 */
@Slf4j
public class ParseDateUtil {

    private static String[] PARSE_PATTERNS = new String[] { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd HH:mm:ss.S", "yyyy-MM-dd HH:mm",
            "yyyy-MM", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd",
            "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM", "yyyy年MM月dd日" };

    public static String formatDateStr(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return "";
        }
        try {
            if (StringUtils.contains(dateStr, "-")) {
                return DateUtils.parseDateToStr(FormatsType.YYYYMMDD, DateUtils.parseDate(dateStr, PARSE_PATTERNS));
            }
            if (StringUtils.contains(dateStr, "年")) {
                return DateUtils.parseDateToStr(FormatsType.YYYYMMDD, DateUtils.parseDate(dateStr, PARSE_PATTERNS));
            }
            if (StringUtils.length(dateStr) == 13) {
                // 将13为的时间戳格式化转换为yyyyMMdd
                return formatTimestamp(dateStr);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return dateStr;
    }

    public static String formatTimestamp(String timestampStr) {
        if (StringUtils.isBlank(timestampStr)) {
            return "";
        }
        long timestamp = Long.parseLong(timestampStr);
        // 把时间戳转为Instant对象
        Instant instant = Instant.ofEpochMilli(timestamp);
        // 获取系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();
        // 将Instant转换为LocalDate
        LocalDate localDate = instant.atZone(zoneId).toLocalDate();
        // 按照yyyyMMdd格式进行格式化
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return localDate.format(formatter);
    }

    /**
     * 解析日期区间字符串
     * 
     * @param input 输入字符串，格式为："上线时间:20230815/下线时间:20251231"
     * @return 日期区间解析结果
     */
    public static DateRangeResult parseDateRange(String input) {
        if (StringUtils.isBlank(input)) {
            return null;
        }

        try {
            String[] parts = input.split("/");
            if (parts.length != 2) {
                return null;
            }

            String onlineDate = parts[0].split(":")[1];
            String offlineDate = parts[1].split(":")[1];

            return new DateRangeResult(
                    formatDateStr(onlineDate),
                    formatDateStr(offlineDate));
        } catch (Exception e) {
            log.error("解析日期区间失败: {}", input, e);
            return null;
        }
    }

    /**
     * 日期区间解析结果类
     */
    public static class DateRangeResult {
        private final String startDate;
        private final String endDate;

        public DateRangeResult(String startDate, String endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }

        public String getStartDate() {
            return startDate;
        }

        public String getEndDate() {
            return endDate;
        }

        @Override
        public String toString() {
            return "DateRangeResult{" +
                    "startDate='" + startDate + '\'' +
                    ", endDate='" + endDate + '\'' +
                    '}';
        }
    }
}
