package com.yunqu.crawler.enums;

/**
 * 爬虫任务执行错误类型数据字典
 */
public enum RunErrorEnum {
    EMPTY_TARIFF_NO(1, "资费方案编码为空"),
    ERROR_TARIFF_NO(2, "资费方案编号格式错误");


    private Integer errorCode;


    private String errorMsg;


    RunErrorEnum(Integer errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public RunErrorEnum setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
        return this;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public RunErrorEnum setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
        return this;
    }
}
