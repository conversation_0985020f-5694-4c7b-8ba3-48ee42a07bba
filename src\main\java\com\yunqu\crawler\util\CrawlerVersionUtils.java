package com.yunqu.crawler.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.yunqu.crawler.constants.CrawlerConstants;
import com.yunqu.emergency.common.redis.utils.RedisUtils;

import java.util.Date;

/**
 * 爬虫版本工具类
 *
 * <AUTHOR>
 * @version 1.0
 */
public class CrawlerVersionUtils {

    /**
     * 生成版本号
     * 格式：V{dateId}_{序号}
     *
     * @param dateId 日期ID
     * @return 版本号
     */
    public static String generateVersion(int dateId) {
        long index = RedisUtils.incrAtomicValue(CrawlerConstants.REDIS_VERSION_KEY_PREFIX + dateId);
        return CrawlerConstants.VERSION_PREFIX + dateId + CrawlerConstants.VERSION_SEPARATOR + index;
    }

    /**
     * 生成版本排序索引
     *
     * @return 排序索引
     */
    public static long generateVersionOrder() {
        return RedisUtils.incrAtomicValue(CrawlerConstants.REDIS_VERSION_ORDER_KEY);
    }

    /**
     * 获取当前日期ID
     * 格式：yyyyMMdd
     *
     * @return 日期ID
     */
    public static Integer getCurrentDateId() {
        String dateStr = DateUtil.format(new Date(), CrawlerConstants.DATE_FORMAT);
        return Convert.toInt(dateStr);
    }

    /**
     * 获取当前日期的天数
     *
     * @return 当前月份的第几天
     */
    public static String getCurrentDay() {
        return String.valueOf(DateUtil.dayOfMonth(new Date()));
    }

    /**
     * 私有构造函数，防止实例化
     */
    private CrawlerVersionUtils() {
        throw new IllegalStateException("工具类不能被实例化");
    }
}