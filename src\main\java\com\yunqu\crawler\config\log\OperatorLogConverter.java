package com.yunqu.crawler.config.log;

import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;

/**
 * 运营商日志颜色转换器
 * 为不同运营商的日志添加不同的颜色标识
 */
public class OperatorLogConverter extends MessageConverter {
    
    private static final String ANSI_RESET = "\u001B[0m";
    private static final String ANSI_RED = "\u001B[31m";
    private static final String ANSI_GREEN = "\u001B[32m";
    private static final String ANSI_YELLOW = "\u001B[33m";
    private static final String ANSI_BLUE = "\u001B[34m";
    
    @Override
    public String convert(ILoggingEvent event) {
        String message = event.getFormattedMessage();
        
        // 根据日志前缀添加颜色
        if (message.contains("[GDT]")) {
            return ANSI_GREEN + message + ANSI_RESET;
        } else if (message.contains("[UNICOM]")) {
            return ANSI_BLUE + message + ANSI_RESET;
        } else if (message.contains("[MOBILE]")) {
            return ANSI_RED + message + ANSI_RESET;
        } else if (message.contains("[TELECOM]")) {
            return ANSI_YELLOW + message + ANSI_RESET;
        }
        
        return message;
    }
} 